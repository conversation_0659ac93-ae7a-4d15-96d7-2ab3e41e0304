# 🏗️ 电动车功能模块技术架构设计文档

## 📋 文档信息

| 项目 | 信息 |
|------|------|
| **文档版本** | v1.0.0 |
| **创建日期** | 2025-01-30 |
| **负责人** | Bob (架构师) |
| **项目名称** | 微信小程序电动车功能模块技术架构 |
| **文档状态** | 已完成 |

## 🎯 架构设计目标

### 核心设计原则
1. **一致性原则**：与现有项目架构模式保持完全一致
2. **可扩展性原则**：支持未来功能扩展和设备适配
3. **可靠性原则**：确保蓝牙通信稳定和数据准确性
4. **性能优化原则**：最小化资源消耗和响应延迟

### 架构约束条件
1. 必须复用现有的管理器模式（参考amapManager.js）
2. 必须集成现有的错误处理系统（errorHandler.js）
3. 必须遵循现有的UI设计规范和组件结构
4. 必须支持微信小程序蓝牙API限制

## 🏛️ 整体架构设计

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    微信小程序应用层                          │
├─────────────────────────────────────────────────────────────┤
│  Pages Layer (页面层)                                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   vehicle   │  │route-guide  │  │    index    │         │
│  │  车辆状态页  │  │  导航页面   │  │   首页入口   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  Utils Layer (工具层)                                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │bluetooth    │  │vehicleData  │  │vehicleData  │         │
│  │Manager      │  │Parser       │  │Cache        │         │
│  │蓝牙管理器   │  │数据解析器   │  │数据缓存器   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐                          │
│  │errorHandler │  │amapManager  │                          │
│  │错误处理器   │  │地图管理器   │  (现有组件复用)          │
│  └─────────────┘  └─────────────┘                          │
├─────────────────────────────────────────────────────────────┤
│  API Layer (接口层)                                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │微信蓝牙API  │  │本地存储API  │  │高德地图API  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  Hardware Layer (硬件层)                                    │
│  ┌─────────────┐                                            │
│  │GPS模块      │  ←→ 串口蓝牙通信                           │
│  │(电动车)     │                                            │
│  └─────────────┘                                            │
└─────────────────────────────────────────────────────────────┘
```

### 数据流架构

```
GPS模块 → 串口蓝牙 → 微信蓝牙API → bluetoothManager → vehicleDataParser 
    ↓
vehicleDataCache ← UI组件更新 ← 页面状态管理 ← 格式化数据
    ↓
本地存储 ← 数据持久化
```

## 🔧 核心模块设计

### 1. 蓝牙管理器 (bluetoothManager.js)

#### 设计模式
参考 `amapManager.js` 的单例模式和统一调用机制

#### 核心接口设计
```javascript
const bluetoothManager = {
  // 单例实例管理
  bluetoothInstance: null,
  connectedDevice: null,
  connectionState: 'disconnected', // disconnected | connecting | connected | error
  
  // 统一调用方法（参考amapManager.safeCall）
  safeCall: function(method, options = {}) {
    return this.checkBluetoothPermission()
      .then(() => this.initBluetooth())
      .then(() => this[method](options))
      .catch(error => errorHandler.handleApiError(error));
  },
  
  // 权限检查（参考amapManager权限模式）
  checkBluetoothPermission: function() {
    return new Promise((resolve, reject) => {
      wx.openBluetoothAdapter({
        success: resolve,
        fail: reject
      });
    });
  },
  
  // 设备搜索
  searchDevices: function(options = {}) {
    const { timeout = 10000, deviceName = null } = options;
    return new Promise((resolve, reject) => {
      // 实现设备搜索逻辑
    });
  },
  
  // 设备连接
  connectDevice: function(deviceId) {
    return new Promise((resolve, reject) => {
      // 实现设备连接逻辑
    });
  },
  
  // 数据读取
  readData: function() {
    return new Promise((resolve, reject) => {
      // 实现数据读取逻辑
    });
  },
  
  // 数据写入（模式切换指令）
  writeData: function(data) {
    return new Promise((resolve, reject) => {
      // 实现数据写入逻辑
    });
  },
  
  // 断开连接
  disconnect: function() {
    // 实现断开连接逻辑
  },
  
  // 资源清理（参考amapManager.destroyInstance）
  destroy: function() {
    // 清理蓝牙资源
  }
};
```

#### 错误处理集成
```javascript
// 集成现有错误处理系统
const errorHandler = require('./errorHandler.js');

// 扩展错误类型
const BLUETOOTH_ERROR_TYPES = {
  'BLUETOOTH_NOT_AVAILABLE': {
    title: '蓝牙不可用',
    message: '设备不支持蓝牙或蓝牙已关闭',
    suggestion: '请检查设备蓝牙设置'
  },
  'DEVICE_NOT_FOUND': {
    title: '设备未找到',
    message: '未找到指定的车载设备',
    suggestion: '请确认设备已开启并在范围内'
  },
  'CONNECTION_FAILED': {
    title: '连接失败',
    message: '无法连接到车载设备',
    suggestion: '请重试或检查设备状态'
  }
};
```

### 2. 车辆数据解析器 (vehicleDataParser.js)

#### 设计模式
参考 `routeStepParser.js` 的数据解析和格式化模式

#### 数据协议定义
```javascript
// 串口数据协议格式
const VEHICLE_DATA_PROTOCOL = {
  // 数据包结构：[起始位][数据类型][数据长度][数据内容][校验位][结束位]
  START_BYTE: 0xAA,
  END_BYTE: 0x55,
  
  DATA_TYPES: {
    BATTERY: 0x01,      // 电量数据
    SPEED: 0x02,        // 时速数据
    MODE: 0x03,         // 驾驶模式
    STATUS: 0x04        // 车辆状态
  }
};

// 驾驶模式配置
const DRIVING_MODES = {
  0: { 
    name: '青少年模式', 
    icon: '🧒', 
    maxSpeed: 15, 
    color: '#4caf50',
    description: '安全限速，适合青少年使用'
  },
  1: { 
    name: '成人模式', 
    icon: '👨', 
    maxSpeed: 25, 
    color: '#2196f3',
    description: '标准性能，平衡续航'
  },
  2: { 
    name: '老人模式', 
    icon: '👴', 
    maxSpeed: 12, 
    color: '#ff9800',
    description: '舒适驾驶，稳定输出'
  }
};
```

#### 核心解析方法
```javascript
const vehicleDataParser = {
  // 解析原始数据包
  parseRawData: function(rawData) {
    try {
      // 数据包验证
      if (!this.validateDataPacket(rawData)) {
        throw new Error('数据包格式错误');
      }
      
      // 解析数据内容
      const dataType = rawData[1];
      const dataContent = rawData.slice(3, -2);
      
      switch(dataType) {
        case VEHICLE_DATA_PROTOCOL.DATA_TYPES.BATTERY:
          return this.parseBattery(dataContent);
        case VEHICLE_DATA_PROTOCOL.DATA_TYPES.SPEED:
          return this.parseSpeed(dataContent);
        case VEHICLE_DATA_PROTOCOL.DATA_TYPES.MODE:
          return this.parseDrivingMode(dataContent);
        default:
          throw new Error('未知数据类型');
      }
    } catch (error) {
      console.error('数据解析错误:', error);
      return null;
    }
  },
  
  // 解析电量数据
  parseBattery: function(data) {
    const batteryLevel = data[0];
    return {
      type: 'battery',
      value: Math.max(0, Math.min(100, batteryLevel)),
      timestamp: Date.now(),
      isLow: batteryLevel < 20
    };
  },
  
  // 解析时速数据
  parseSpeed: function(data) {
    const speed = (data[0] << 8) | data[1]; // 16位速度数据
    return {
      type: 'speed',
      value: Math.max(0, speed / 10), // 转换为km/h
      timestamp: Date.now()
    };
  },
  
  // 解析驾驶模式
  parseDrivingMode: function(data) {
    const modeId = data[0];
    const mode = DRIVING_MODES[modeId];
    
    if (!mode) {
      throw new Error('无效的驾驶模式');
    }
    
    return {
      type: 'drivingMode',
      value: modeId,
      mode: mode,
      timestamp: Date.now()
    };
  },
  
  // 数据包验证
  validateDataPacket: function(data) {
    if (!data || data.length < 5) return false;
    if (data[0] !== VEHICLE_DATA_PROTOCOL.START_BYTE) return false;
    if (data[data.length - 1] !== VEHICLE_DATA_PROTOCOL.END_BYTE) return false;
    
    // 校验位验证
    const checksum = this.calculateChecksum(data.slice(1, -2));
    return checksum === data[data.length - 2];
  },
  
  // 计算校验位
  calculateChecksum: function(data) {
    return data.reduce((sum, byte) => sum + byte, 0) & 0xFF;
  }
};
```

### 3. 数据缓存管理器 (vehicleDataCache.js)

#### 设计模式
基于微信小程序存储API的缓存管理系统

#### 核心功能设计
```javascript
const vehicleDataCache = {
  // 缓存配置
  CACHE_CONFIG: {
    MAX_HISTORY_DAYS: 7,        // 最大历史数据天数
    MAX_CACHE_SIZE: 10 * 1024 * 1024, // 最大缓存大小 10MB
    CLEANUP_INTERVAL: 24 * 60 * 60 * 1000 // 清理间隔 24小时
  },
  
  // 缓存车辆数据
  cacheVehicleData: function(data) {
    try {
      const cacheKey = `vehicle_data_${this.getDateKey()}`;
      const existingData = wx.getStorageSync(cacheKey) || [];
      
      existingData.push({
        ...data,
        timestamp: Date.now()
      });
      
      // 限制单日数据量
      if (existingData.length > 1000) {
        existingData.splice(0, existingData.length - 1000);
      }
      
      wx.setStorageSync(cacheKey, existingData);
    } catch (error) {
      console.error('缓存数据失败:', error);
    }
  },
  
  // 获取历史数据
  getHistoryData: function(days = 7) {
    const historyData = [];
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const cacheKey = `vehicle_data_${this.formatDate(date)}`;
      
      try {
        const dayData = wx.getStorageSync(cacheKey);
        if (dayData && dayData.length > 0) {
          historyData.push(...dayData);
        }
      } catch (error) {
        console.error('读取历史数据失败:', error);
      }
    }
    
    return historyData.sort((a, b) => a.timestamp - b.timestamp);
  },
  
  // 数据统计分析
  getDataStatistics: function(days = 7) {
    const historyData = this.getHistoryData(days);
    
    const batteryData = historyData.filter(d => d.type === 'battery');
    const speedData = historyData.filter(d => d.type === 'speed');
    
    return {
      totalRecords: historyData.length,
      avgBattery: this.calculateAverage(batteryData.map(d => d.value)),
      maxSpeed: Math.max(...speedData.map(d => d.value)),
      avgSpeed: this.calculateAverage(speedData.map(d => d.value)),
      drivingTime: this.calculateDrivingTime(speedData)
    };
  },
  
  // 清理过期数据
  cleanupExpiredData: function() {
    try {
      const storageInfo = wx.getStorageInfoSync();
      const keys = storageInfo.keys.filter(key => key.startsWith('vehicle_data_'));
      
      keys.forEach(key => {
        const dateStr = key.replace('vehicle_data_', '');
        const date = new Date(dateStr);
        const daysDiff = (Date.now() - date.getTime()) / (1000 * 60 * 60 * 24);
        
        if (daysDiff > this.CACHE_CONFIG.MAX_HISTORY_DAYS) {
          wx.removeStorageSync(key);
        }
      });
    } catch (error) {
      console.error('清理缓存失败:', error);
    }
  }
};
```

## 📱 页面架构设计

### 1. 车辆状态页面 (pages/vehicle/)

#### 页面结构设计
参考 `pages/location/` 的页面组织模式

```javascript
// vehicle.js 页面逻辑结构
Page({
  data: {
    // 车辆连接状态
    bluetoothStatus: 'disconnected', // disconnected | connecting | connected | error
    connectedDevice: null,
    
    // 车辆实时数据
    vehicleData: {
      battery: 0,
      speed: 0,
      drivingMode: 1,
      lastUpdate: null
    },
    
    // UI状态管理
    isConnecting: false,
    showModeSelector: false,
    dataUpdateInterval: null,
    
    // 历史数据
    historyStats: null,
    showHistory: false
  },
  
  // 生命周期管理（参考location.js模式）
  onLoad: function() {
    this.initBluetoothConnection();
    this.loadHistoryData();
  },
  
  onShow: function() {
    if (this.data.bluetoothStatus === 'connected') {
      this.startDataMonitoring();
    }
  },
  
  onHide: function() {
    this.pauseDataMonitoring();
  },
  
  onUnload: function() {
    this.cleanupResources();
  },
  
  // 蓝牙连接管理
  initBluetoothConnection: function() {
    const bluetoothManager = require('../../utils/bluetoothManager.js');
    
    bluetoothManager.safeCall('checkBluetoothPermission')
      .then(() => {
        this.setData({ bluetoothStatus: 'ready' });
      })
      .catch(error => {
        this.handleConnectionError(error);
      });
  },
  
  // 数据监控管理
  startDataMonitoring: function() {
    const that = this;
    
    // 每2秒更新一次数据
    const interval = setInterval(() => {
      if (that.data.bluetoothStatus !== 'connected') {
        clearInterval(interval);
        return;
      }
      that.updateVehicleData();
    }, 2000);
    
    this.setData({ dataUpdateInterval: interval });
  },
  
  // 资源清理
  cleanupResources: function() {
    if (this.data.dataUpdateInterval) {
      clearInterval(this.data.dataUpdateInterval);
    }
    
    const bluetoothManager = require('../../utils/bluetoothManager.js');
    bluetoothManager.disconnect();
  }
});
```

#### UI组件设计规范

遵循现有项目的UI设计模式：

```css
/* vehicle.wxss - 样式设计 */

/* 页面容器 - 参考现有页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 状态卡片 - 参考setup.wxss的卡片样式 */
.status-card {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

/* 渐变背景 - 参考现有设计 */
.header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 15rpx;
  padding: 40rpx 20rpx;
  text-align: center;
  margin-bottom: 30rpx;
}

/* 数据显示组件 */
.data-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.data-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.data-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.data-unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 10rpx;
}

/* 驾驶模式选择器 */
.mode-selector {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
}

.mode-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  margin: 0 10rpx;
  border-radius: 10rpx;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.mode-item.active {
  border-color: #2196f3;
  background-color: #e3f2fd;
}

.mode-icon {
  font-size: 40rpx;
  display: block;
  margin-bottom: 10rpx;
}

.mode-name {
  font-size: 24rpx;
  color: #333;
}
```

### 2. 导航页面集成设计

#### 集成策略
在 `pages/route-guide/` 中添加车辆状态悬浮窗

```xml
<!-- route-guide.wxml 集成设计 -->

<!-- 现有GPS状态显示区域下方添加车辆状态 -->
<view class="vehicle-status-float" wx:if="{{vehicleConnected && showVehicleStatus}}">
  <view class="vehicle-header">
    <text class="status-title">🚗 车辆状态</text>
    <button class="toggle-btn" bindtap="toggleVehicleStatus">
      <text class="toggle-icon">{{vehicleStatusExpanded ? '▼' : '▲'}}</text>
    </button>
  </view>
  
  <view class="vehicle-data" wx:if="{{vehicleStatusExpanded}}">
    <view class="data-item">
      <text class="data-icon">🔋</text>
      <text class="data-value">{{vehicleData.battery}}%</text>
    </view>
    <view class="data-item">
      <text class="data-icon">⚡</text>
      <text class="data-value">{{vehicleData.speed}}km/h</text>
    </view>
    <view class="data-item">
      <text class="data-icon">{{vehicleData.drivingMode.icon}}</text>
      <text class="data-value">{{vehicleData.drivingMode.name}}</text>
    </view>
  </view>
</view>
```

## 🔄 数据流设计

### 实时数据流
```
1. GPS模块发送数据 → 串口蓝牙传输
2. 微信蓝牙API接收 → bluetoothManager处理
3. vehicleDataParser解析 → 数据格式化
4. 页面状态更新 → UI组件渲染
5. vehicleDataCache缓存 → 本地存储
```

### 模式切换流程
```
1. 用户选择驾驶模式 → UI交互
2. 确认对话框 → 安全检查
3. bluetoothManager.writeData → 发送指令
4. GPS模块响应 → 模式切换确认
5. 页面状态更新 → UI反馈
6. 本地配置保存 → 持久化存储
```

## 🛡️ 安全与性能设计

### 安全设计
1. **数据校验**：所有接收数据进行格式和范围校验
2. **权限控制**：蓝牙权限检查和用户授权引导
3. **错误隔离**：车辆功能错误不影响导航核心功能
4. **安全限制**：驾驶模式切换需要用户确认

### 性能优化
1. **数据更新频率**：2秒间隔，平衡实时性和性能
2. **内存管理**：及时清理定时器和蓝牙连接
3. **缓存策略**：本地缓存减少重复计算
4. **懒加载**：历史数据按需加载

### 兼容性设计
1. **设备适配**：支持不同蓝牙版本和设备类型
2. **降级处理**：蓝牙不可用时的功能降级
3. **错误恢复**：连接中断时的自动重连机制

## 📊 技术选型说明

### 核心技术栈
1. **微信小程序蓝牙API**：wx.openBluetoothAdapter系列
2. **数据存储**：wx.setStorageSync/wx.getStorageSync
3. **UI框架**：微信小程序原生组件
4. **架构模式**：模块化 + 单例模式 + 统一管理

### 技术决策理由
1. **复用现有架构**：确保代码一致性和可维护性
2. **单例模式**：避免多实例冲突，统一资源管理
3. **Promise化API**：统一异步处理，便于错误处理
4. **模块化设计**：职责分离，便于测试和扩展

## 🚀 部署和集成指南

### 开发环境配置
1. 确保微信开发者工具支持蓝牙调试
2. 准备支持串口蓝牙的GPS测试设备
3. 配置蓝牙权限和域名白名单

### 集成步骤
1. **Phase 1**：创建核心工具模块
2. **Phase 2**：开发车辆状态页面
3. **Phase 3**：集成到现有导航系统
4. **Phase 4**：测试和优化

### 测试策略
1. **单元测试**：各工具模块独立测试
2. **集成测试**：蓝牙通信和数据解析测试
3. **兼容性测试**：不同设备和系统版本测试
4. **性能测试**：内存使用和响应时间测试

---

**文档状态**：✅ 已完成
**下一步行动**：开始核心模块开发
**负责人**：Bob → Alex (工程师)