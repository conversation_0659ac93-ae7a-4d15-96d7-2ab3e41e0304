# 🔧 高德地图API问题解决方案

## 📋 问题诊断清单

### **您遇到的问题：静态地图API错误**

根据您的描述"我在运行小程序获取静态地图时一直失败，显示地图API错误"，我已经为您创建了完整的诊断工具。

---

## 🎯 **立即解决方案**

### **步骤1：使用API诊断工具**

1. **在微信开发者工具中运行小程序**
2. **点击首页的"🔧 API诊断工具"按钮**
3. **点击"🔍 运行完整诊断"**
4. **查看诊断结果和修复建议**

### **步骤2：检查API密钥配置**

您当前的API密钥：`63431c51b3983f3712144e7f0c45a047`

**验证步骤：**
1. 登录高德开放平台：https://console.amap.com/dev/key/app
2. 确认密钥状态为"正常"
3. 检查每日配额使用情况
4. 确认"Web服务"权限已开启

---

## 🚨 **常见问题及解决方案**

### **问题1：API密钥无效 (INVALID_USER_KEY)**

**症状：** API返回401错误，提示密钥无效

**解决方案：**
```javascript
// 检查config.js中的密钥配置
AMAP_WEB_KEY: '63431c51b3983f3712144e7f0c45a047'
```

**验证方法：**
- 在高德控制台确认密钥正确
- 确保密钥为32位十六进制字符串
- 检查密钥是否被意外修改

### **问题2：权限不足 (INSUFFICIENT_PRIVILEGES)**

**症状：** API返回403错误，提示权限不足

**解决方案：**
1. 登录高德控制台
2. 进入应用管理 → 选择您的应用
3. 确认"Web服务"已勾选
4. 保存配置并等待生效（2-5分钟）

### **问题3：配额用完 (DAILY_QUERY_OVER_LIMIT)**

**症状：** API返回403错误，提示配额超限

**解决方案：**
1. 检查控制台中的配额使用情况
2. 等待次日配额重置
3. 或升级到付费版本

### **问题4：域名配置问题**

**症状：** 微信开发者工具提示域名不在合法域名列表

**临时解决方案（开发阶段）：**
1. 微信开发者工具 → 详情 → 本地设置
2. 勾选"不校验合法域名、web-view（业务域名）、TLS版本以及HTTPS证书"
3. 重新编译项目

**正式解决方案（生产环境）：**
1. 登录微信公众平台：https://mp.weixin.qq.com
2. 开发 → 开发管理 → 开发设置
3. 在"服务器域名"中添加：
   - `https://restapi.amap.com`
   - `https://webapi.amap.com`

### **问题5：网络连接问题**

**症状：** 请求超时或网络错误

**解决方案：**
1. 检查网络连接
2. 尝试切换网络环境
3. 检查防火墙设置
4. 确认高德服务器状态正常

---

## 🔍 **详细诊断步骤**

### **手动验证API密钥**

您可以在浏览器中直接测试API：

```
https://restapi.amap.com/v3/staticmap?key=63431c51b3983f3712144e7f0c45a047&location=116.397428,39.90923&zoom=10&size=400*300&markers=mid,0xFF0000,A:116.397428,39.90923
```

**预期结果：**
- 正常：返回一张北京天安门的地图图片
- 异常：返回JSON格式的错误信息

### **检查返回的错误信息**

常见错误代码：
- `10001`: API密钥不正确
- `10002`: 请求过于频繁
- `10003`: 权限不足
- `10004`: 配额用完
- `10005`: 参数错误

---

## 🛠️ **使用诊断工具**

我已经为您创建了专门的API诊断工具，位于：
- 页面路径：`pages/api-test/api-test`
- 工具文件：`utils/apiKeyDiagnostic.js`

**诊断工具功能：**
1. ✅ 自动检查API密钥格式
2. ✅ 测试API连接性
3. ✅ 分析错误原因
4. ✅ 提供修复建议
5. ✅ 生成详细诊断报告

---

## 📞 **获取帮助**

### **如果问题仍未解决：**

1. **运行诊断工具**并截图诊断结果
2. **复制完整的错误信息**
3. **检查微信开发者工具的控制台日志**
4. **确认网络环境和开发工具设置**

### **联系技术支持：**

- 高德开放平台技术支持：https://lbs.amap.com/dev/
- 微信小程序技术支持：https://developers.weixin.qq.com/

---

## 🎉 **预期结果**

完成上述步骤后，您应该能够：
1. ✅ 成功获取静态地图
2. ✅ 看到北京天安门的地图图片
3. ✅ 在诊断工具中看到"API调用成功"
4. ✅ 正常使用所有地图功能

---

## 📝 **注意事项**

1. **开发阶段**：建议关闭域名校验以便调试
2. **生产环境**：必须配置正确的合法域名
3. **API配额**：注意监控每日调用量
4. **密钥安全**：不要在客户端暴露敏感密钥

---

**🔧 立即开始诊断：在小程序首页点击"API诊断工具"按钮！**
