# 微信小程序域名配置指南

## 🚨 重要提示

您遇到的错误：`https://restapi.amap.com 不在以下 request 合法域名列表中`

这是因为微信小程序需要在后台配置合法的请求域名才能访问外部API。

## 🔧 解决方案

### 方案1：配置合法域名（推荐 - 正式发布必需）

#### 步骤1：登录微信公众平台
1. 访问：https://mp.weixin.qq.com
2. 使用您的小程序账号登录（AppID: wx3c0d98856197a34a）

#### 步骤2：进入开发设置
1. 点击左侧菜单 **"开发"** → **"开发管理"**
2. 找到 **"开发设置"** 部分
3. 在 **"服务器域名"** 区域点击 **"修改"**

#### 步骤3：添加合法域名
在 **request合法域名** 中添加以下域名：
```
https://restapi.amap.com
```

#### 步骤4：保存配置
1. 点击 **"保存并提交"**
2. 等待配置生效（通常2-5分钟）

### 方案2：开发阶段临时解决

#### 在微信开发者工具中：
1. 点击右上角 **"详情"** 按钮
2. 选择 **"本地设置"** 标签页
3. 勾选 **"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"**
4. 重新编译项目

⚠️ **注意**：此方法仅适用于开发测试，正式发布时必须配置合法域名。

## 📋 需要配置的完整域名列表

为了确保电动车导航小程序的所有功能正常工作，建议配置以下域名：

### request合法域名：
```
https://restapi.amap.com
https://webapi.amap.com
```

### uploadFile合法域名：
```
https://restapi.amap.com
```

### downloadFile合法域名：
```
https://webapi.amap.com
```

## 🎯 配置完成后的验证

1. **重新编译小程序**
2. **测试定位功能**
3. **测试路线规划功能**
4. **检查控制台是否还有域名错误**

## 🔍 常见问题

### Q: 配置后仍然报错？
A: 请等待2-5分钟让配置生效，然后重新编译项目。

### Q: 无法访问微信公众平台？
A: 请确认您有该小程序的管理员权限。

### Q: 开发阶段可以跳过配置吗？
A: 可以使用方案2临时跳过，但正式发布前必须配置。

## 📱 高德地图API功能说明

配置域名后，以下功能将正常工作：
- ✅ 地理编码/逆地理编码
- ✅ 路线规划
- ✅ POI搜索
- ✅ 天气查询
- ✅ 静态地图
- ✅ 距离计算

## 🚀 下一步

配置完成后，您可以：
1. 测试现有的导航功能
2. 继续开发电动车相关功能
3. 测试蓝牙设备连接

---

**如有问题，请参考微信官方文档：**
https://developers.weixin.qq.com/miniprogram/dev/framework/ability/network.html
