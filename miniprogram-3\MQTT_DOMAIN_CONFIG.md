# MQTT域名配置指南

## 🚀 **您的MQTT服务器配置**

```
服务器地址: s3.v100.vip
端口: 33880
用户名: mdfk
密码: 1HUJIFDAHSOUF
客户端ID: mdfk124xfasrf
```

## 🔧 **微信小程序域名配置**

### **步骤1：登录微信公众平台**
1. 访问：https://mp.weixin.qq.com
2. 使用您的小程序账号登录（AppID: wx3c0d98856197a34a）

### **步骤2：配置合法域名**

#### **socket合法域名**（MQTT WebSocket连接）
```
wss://s3.v100.vip:33880
```

#### **request合法域名**（备用HTTP接口）
```
https://s3.v100.vip:33880
https://restapi.amap.com
```

#### **uploadFile合法域名**
```
https://s3.v100.vip:33880
https://restapi.amap.com
```

#### **downloadFile合法域名**
```
https://s3.v100.vip:33880
https://webapi.amap.com
```

### **步骤3：保存配置**
1. 点击 **"保存并提交"**
2. 等待配置生效（2-5分钟）

## 🛠️ **开发阶段临时解决方案**

如果暂时无法配置域名，可以在微信开发者工具中：

1. 点击右上角 **"详情"**
2. 选择 **"本地设置"** 标签
3. 勾选 **"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"**
4. 重新编译项目

⚠️ **注意**：此方法仅适用于开发测试，正式发布必须配置合法域名。

## 📱 **MQTT功能说明**

### **车辆数据主题**
```javascript
// 订阅主题（接收车辆数据）
vehicle/status      // 车辆状态
vehicle/battery     // 电池信息
vehicle/speed       // 速度信息
vehicle/location    // 位置信息

// 发布主题（发送控制指令）
vehicle/control/mode        // 驾驶模式控制
vehicle/control/speed_limit // 速度限制控制
vehicle/control/system      // 系统控制
```

### **数据格式示例**

#### **电池信息** (`vehicle/battery`)
```json
{
  "level": 85,
  "voltage": 48.2,
  "current": 2.5,
  "temperature": 25,
  "charging": false
}
```

#### **速度信息** (`vehicle/speed`)
```json
{
  "current": 18,
  "max": 25,
  "average": 15
}
```

#### **位置信息** (`vehicle/location`)
```json
{
  "latitude": 39.90923,
  "longitude": 116.397428,
  "altitude": 50,
  "heading": 180,
  "accuracy": 5
}
```

#### **驾驶模式控制** (`vehicle/control/mode`)
```json
{
  "action": "switch_mode",
  "mode": "adult",
  "timestamp": 1703123456789
}
```

## 🎯 **使用方法**

### **1. 初始化车辆数据管理器**
```javascript
const vehicleDataManager = require('./utils/vehicleDataManager.js');

// 初始化
vehicleDataManager.initialize()
  .then(result => {
    console.log('车辆数据管理器初始化成功');
  })
  .catch(error => {
    console.error('初始化失败:', error);
  });
```

### **2. 监听车辆数据**
```javascript
// 监听电池数据
vehicleDataManager.addDataListener('battery', (batteryData) => {
  console.log('电池数据更新:', batteryData);
  // 更新UI显示
});

// 监听速度数据
vehicleDataManager.addDataListener('speed', (speedData) => {
  console.log('速度数据更新:', speedData);
  // 更新速度显示
});

// 监听警告信息
vehicleDataManager.addDataListener('warning', (warning) => {
  console.log('收到警告:', warning);
  // 显示警告提示
});
```

### **3. 控制车辆**
```javascript
// 切换驾驶模式
vehicleDataManager.switchDrivingMode('youth')
  .then(result => {
    console.log('模式切换成功:', result);
  });

// 设置速度限制
vehicleDataManager.setSpeedLimit(20)
  .then(result => {
    console.log('速度限制设置成功:', result);
  });
```

### **4. 获取车辆数据**
```javascript
const vehicleData = vehicleDataManager.getVehicleData();
console.log('当前车辆数据:', vehicleData);
```

## 🔍 **驾驶模式配置**

```javascript
const DRIVING_MODES = {
  youth: {
    name: '青少年模式',
    maxSpeed: 15,        // 限速15km/h
    acceleration: 'slow',
    description: '限速15km/h，适合青少年使用'
  },
  adult: {
    name: '成人模式', 
    maxSpeed: 25,        // 限速25km/h
    acceleration: 'normal',
    description: '限速25km/h，正常驾驶模式'
  },
  elderly: {
    name: '老人模式',
    maxSpeed: 12,        // 限速12km/h
    acceleration: 'gentle',
    description: '限速12km/h，平稳舒适驾驶'
  }
};
```

## 🚨 **常见问题**

### **Q: WebSocket连接失败？**
A: 
1. 检查域名配置是否正确
2. 确认服务器地址和端口
3. 检查网络连接状态
4. 验证MQTT服务器是否支持WebSocket

### **Q: 无法接收车辆数据？**
A:
1. 检查MQTT主题订阅是否成功
2. 确认车载设备是否正常发送数据
3. 检查数据格式是否正确
4. 查看控制台错误信息

### **Q: 控制指令无效？**
A:
1. 检查发布主题是否正确
2. 确认指令格式符合要求
3. 检查车载设备是否接收到指令
4. 验证MQTT连接状态

## 📋 **测试清单**

配置完成后，请测试以下功能：

- [ ] MQTT连接建立成功
- [ ] 车辆数据主题订阅成功
- [ ] 能够接收电池信息
- [ ] 能够接收速度信息
- [ ] 能够接收位置信息
- [ ] 驾驶模式切换功能正常
- [ ] 速度限制设置功能正常
- [ ] 警告提示功能正常

## 🔗 **相关文档**

- [微信小程序网络配置](https://developers.weixin.qq.com/miniprogram/dev/framework/ability/network.html)
- [WebSocket API文档](https://developers.weixin.qq.com/miniprogram/dev/api/network/websocket/wx.connectSocket.html)
- [MQTT协议规范](http://mqtt.org/)

---

**配置完成后，您的电动车导航小程序将具备完整的车辆数据通信能力！** 🎉
