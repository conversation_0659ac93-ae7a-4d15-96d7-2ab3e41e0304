# 🧭 微信小程序高德地图导航功能配置指南

## 📋 必需配置操作清单

### 1. **静态地图放大优化** ✅ 已完成
- 将默认缩放级别从10提升到16-17级
- 增大地图尺寸从400*400到600*400
- 使用大号标记点提高可见性
- 默认启用高清显示（scale=2）

### 2. **微信小程序导航功能实现方案**

#### 2.1 **技术架构分析**
**重要发现：** Android导航SDK无法直接在微信小程序中使用

**解决方案：** 使用高德Web服务API + 微信小程序map组件实现导航功能

#### 2.2 **API配置要求**
- **API密钥类型：** Web服务API（必须）
- **当前密钥：** `fa8516b9618f8fc794edbb1c26893bc2`
- **域名配置：** `https://restapi.amap.com`（必须在微信公众平台配置）

#### 2.3 **路线规划API支持**
✅ **步行路径规划** - `/v3/direction/walking`
✅ **驾车路径规划** - `/v3/direction/driving`  
✅ **公交路径规划** - `/v3/direction/transit/integrated`
✅ **骑行路径规划** - `/v4/direction/bicycling`

### 3. **微信小程序权限配置**

#### 3.1 **app.json配置**
```json
{
  "permission": {
    "scope.userLocation": {
      "desc": "您的位置信息将用于导航和路线规划"
    }
  },
  "requiredPrivateInfos": ["getLocation", "chooseLocation"]
}
```

#### 3.2 **域名白名单配置**
在微信公众平台配置：
- **request合法域名：** `https://restapi.amap.com`

### 4. **导航功能特性**

#### 4.1 **路线规划功能**
- 🚗 **驾车导航** - 支持多种策略（最快路线、避免拥堵、不走高速等）
- 🚶 **步行导航** - 最大支持100km步行路线
- 🚌 **公交导航** - 综合公交、地铁、火车等公共交通
- 🚴 **骑行导航** - 最大支持500km骑行路线

#### 4.2 **地图显示功能**
- 📍 **起终点标记** - 清晰的起点和终点标识
- 🛣️ **路线绘制** - 在地图上绘制完整路线
- 🎯 **当前位置** - 实时显示用户当前位置
- 🔍 **地图控制** - 缩放、移动、定位等操作

#### 4.3 **交互功能**
- 📱 **位置选择** - 通过wx.chooseLocation选择起终点
- 🔄 **起终点交换** - 一键交换起点和终点
- 📊 **路线对比** - 显示多条路线供用户选择
- ⏱️ **时间距离** - 显示预计时间和距离

### 5. **技术实现细节**

#### 5.1 **核心文件结构**
```
├── pages/navigation/           # 导航功能页面
│   ├── navigation.js          # 页面逻辑
│   ├── navigation.wxml        # 页面结构  
│   ├── navigation.wxss        # 页面样式
│   └── navigation.json        # 页面配置
├── utils/
│   ├── navigationConfig.js    # 导航配置和API调用
│   └── staticMapConfig.js     # 静态地图配置（已优化）
```

#### 5.2 **API调用示例**
```javascript
// 驾车路线规划
const drivingUrl = 'https://restapi.amap.com/v3/direction/driving?' +
  'key=fa8516b9618f8fc794edbb1c26893bc2&' +
  'origin=116.397428,39.90923&' +
  'destination=116.465302,40.004717&' +
  'strategy=10&' +
  'extensions=all';

// 步行路线规划  
const walkingUrl = 'https://restapi.amap.com/v3/direction/walking?' +
  'key=fa8516b9618f8fc794edbb1c26893bc2&' +
  'origin=116.397428,39.90923&' +
  'destination=116.465302,40.004717';
```

#### 5.3 **地图组件配置**
```xml
<map
  id="navigationMap"
  longitude="{{mapCenter.longitude}}"
  latitude="{{mapCenter.latitude}}"
  scale="{{mapScale}}"
  markers="{{markers}}"
  polyline="{{polylines}}"
  show-location="{{true}}">
</map>
```

### 6. **驾车策略说明**

| 策略代码 | 说明 | 推荐场景 |
|---------|------|----------|
| 10 | 躲避拥堵，路程较短，尽量缩短时间 | **推荐默认** |
| 12 | 躲避拥堵 | 高峰期出行 |
| 13 | 不走高速 | 节省过路费 |
| 14 | 避免收费 | 经济出行 |
| 19 | 高速优先 | 长途出行 |
| 20 | 躲避拥堵&高速优先 | 长途且考虑路况 |

### 7. **错误处理和调试**

#### 7.1 **常见错误**
- **API密钥错误** - 确保使用Web服务API密钥
- **域名未配置** - 在微信公众平台配置域名白名单
- **定位权限** - 确保用户授权位置权限
- **网络问题** - 检查网络连接状态

#### 7.2 **调试方法**
1. 使用配置指南页面测试API连接
2. 查看控制台日志输出
3. 使用浏览器测试API URL
4. 检查返回数据格式

### 8. **性能优化建议**

#### 8.1 **地图性能**
- 合理设置地图缩放级别
- 限制同时显示的标记点数量
- 使用节流控制地图事件频率

#### 8.2 **API调用优化**
- 缓存路线规划结果
- 避免频繁重复请求
- 使用合适的extensions参数

### 9. **用户体验优化**

#### 9.1 **交互优化**
- 提供清晰的加载状态提示
- 支持手势操作地图
- 提供快捷的位置选择方式

#### 9.2 **视觉优化**
- 使用不同颜色区分路线类型
- 提供清晰的起终点标识
- 合理的信息层级展示

## 🎯 总结

微信小程序导航功能已完整实现，包括：

✅ **静态地图放大优化** - 提升地图清晰度和可见性
✅ **完整导航功能** - 支持4种出行方式的路线规划
✅ **地图交互** - 完整的地图操作和路线显示
✅ **用户界面** - 直观的导航界面和控制面板
✅ **错误处理** - 完善的错误提示和处理机制

**下一步：** 确保API密钥配置正确，在微信公众平台配置域名白名单，即可正常使用所有导航功能。

---

**最后更新：** 2025年6月26日  
**版本：** 2.0.0
