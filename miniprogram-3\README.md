# 🗺️ 高德地图微信小程序导航系统

一个功能完整的微信小程序导航应用，集成高德地图API，支持静态地图显示、实时定位、路线规划、GPS导航等功能。

## 📋 项目概述

### 🎯 核心功能
- **🗺️ 静态地图显示** - 高清地图展示，支持多种缩放级别
- **📍 实时定位服务** - 基于GPS的精确定位
- **🧭 智能路线规划** - 支持步行、骑行、驾车等多种出行方式
- **🛰️ GPS实时导航** - 真实GPS位置跟踪，自动步骤切换
- **🎮 演示导航模式** - 手动体验导航功能
- **🔍 POI搜索** - 周边兴趣点搜索和导航

### 🏗️ 技术架构

#### **双API密钥设计**
为了最大化功能完整性，项目采用双API密钥架构：

| API密钥类型 | 用途 | 密钥值 |
|------------|------|-------|
| **微信小程序SDK专用** | AMapWX SDK调用 | `fa8516b9618f8fc794edbb1c26893bc2` |
| **Web服务API专用** | HTTP API调用 | `6dcf54a0b06c091e3764140865088543` |

#### **模块化架构**
```
├── pages/               # 页面模块
│   ├── index/           # 首页 - 静态地图展示
│   ├── location/        # 定位功能页面  
│   ├── navigation/      # 路线规划页面
│   ├── route-guide/     # 实时导航页面
│   ├── config/          # API配置说明
│   ├── setup/           # 配置指南
│   └── logs/            # 日志查看
├── utils/               # 工具模块
│   ├── amapManager.js   # 🔧 SDK统一管理器
│   ├── errorHandler.js  # 🔧 统一错误处理
│   ├── config.js        # ⚙️ 双API密钥配置
│   ├── staticMapConfig.js # 静态地图生成
│   ├── poiSearchConfig.js # POI搜索功能
│   ├── navigationConfig.js # 导航配置
│   ├── routeStepParser.js # 路线步骤解析
│   └── util.js          # 通用工具函数
└── libs/                # 第三方SDK
    └── amap-wx.130.js   # 高德地图SDK
```

## 🚀 快速开始

### 1. 环境配置

#### **微信小程序配置**
在 `app.json` 中已配置：
```json
{
  "permission": {
    "scope.userLocation": {
      "desc": "您的位置信息将用于小程序定位功能"
    }
  },
  "requiredPrivateInfos": ["getLocation"]
}
```

#### **域名白名单配置**
在微信公众平台配置以下域名：
- `https://restapi.amap.com` (request合法域名)

### 2. API密钥配置

API密钥在 `utils/config.js` 中配置，支持两种使用模式：

```javascript
module.exports = {
  // 微信小程序SDK专用密钥（用于AMapWX SDK）
  AMAP_KEY: 'b27cdb743832c88b5747b96580df8062',

  // Web服务API专用密钥（用于HTTP请求）
  AMAP_WEB_KEY: 'b27cdb743832c88b5747b96580df8062'
};
```

### 3. 项目启动

1. 在微信开发者工具中导入项目
2. 确保已配置域名白名单
3. 运行项目，首页会自动测试API连接

## 📱 功能模块详解

### 🏠 首页 (pages/index)
**功能**：静态地图展示和功能导航
- 自动获取当前位置显示高清静态地图
- 支持多种缩放级别（3级到18级）
- 快速跳转到各功能模块

**核心特性**：
- 🔍 **智能缩放** - 根据用户需求调整地图显示范围
- 🎯 **高清显示** - scale=2，实际分辨率1024*800
- 📍 **位置标记** - 清晰的当前位置标识

### 📍 定位页面 (pages/location)
**功能**：高德地图定位和周边服务
- 实时GPS定位
- 天气信息查询
- 周边POI搜索

**技术实现**：
```javascript
// 使用统一SDK管理器安全调用
amapManager.safeCall('getRegeo')
  .then(result => {
    console.log('定位成功:', result.data);
  })
  .catch(error => {
    errorHandler.handleApiError(error);
  });
```

### 🧭 导航页面 (pages/navigation)
**功能**：路线规划和导航设置
- 起点自动获取（当前位置）
- 终点搜索输入（POI搜索）
- 多种出行方式选择
- 路线方案对比

**支持的导航类型**：
| 类型 | 图标 | 策略 | 特点 |
|-----|------|------|------|
| 步行 | 🚶 | SINGLE | 最大100km |
| 骑行 | 🚴 | SINGLE | 最大500km |
| 步行(多路线) | 🚶 | MULTIPLE | 提供多种路线选择 |
| 骑行(多路线) | 🚴 | MULTIPLE | 比较不同骑行路线 |

### 🛰️ 路线指引页面 (pages/route-guide)
**功能**：实时GPS导航和路线指引

#### **双导航模式**

**🛰️ GPS导航模式**：
- 每3秒获取GPS位置
- 距离目标20米内自动前进
- 偏离路线智能检测和重新规划
- 实时剩余距离计算

**🎮 演示模式**：
- 手动控制导航步骤
- 上一步/下一步按钮
- 适合功能演示和测试

#### **GPS导航核心算法**
```javascript
// GPS位置更新处理
processGPSUpdate: function(currentLocation) {
  const currentStep = routeSteps[currentStepIndex];
  const distance = calculateDistance(currentLocation, stepEndPoint);
  
  if (distance < 20) {
    // 自动前进到下一步
    this.autoAdvanceToNextStep();
  } else if (distance > 100) {
    // 检查偏离路线
    this.checkOffRoute(currentLocation);
  }
}
```

### ⚙️ 配置页面 (pages/config & pages/setup)
**功能**：API配置说明和状态检测
- API密钥有效性测试
- 域名配置指导
- 功能状态监控

## 🔧 核心工具模块

### 🛡️ SDK统一管理器 (utils/amapManager.js)
**作用**：统一管理高德SDK实例和权限
- 单例模式确保SDK唯一性
- 自动权限检查和申请
- 统一错误处理

**使用方式**：
```javascript
const amapManager = require('../../utils/amapManager.js');

// 安全调用SDK方法（自动处理权限和错误）
amapManager.safeCall('getRegeo', options)
  .then(result => {
    // 处理成功结果
  })
  .catch(error => {
    // 统一错误处理
  });
```

### 🚨 统一错误处理 (utils/errorHandler.js)
**作用**：智能错误识别和用户友好提示
- API错误类型映射
- 错误恢复建议
- 多种展示模式（Toast、Modal、弹窗）

**错误类型覆盖**：
- API密钥相关错误
- 定位权限问题
- 网络连接异常
- 服务调用超限

### 🗺️ 静态地图配置 (utils/staticMapConfig.js)
**作用**：高清静态地图生成和优化
- 多种地图样式配置
- URL长度优化
- 路线可视化

**关键配置**：
```javascript
STATIC_MAP_CONFIG = {
  BASE_URL: 'https://restapi.amap.com/v3/staticmap',
  DEFAULTS: {
    zoom: 14,
    size: '600*400',
    scale: 2  // 高清模式
  }
}
```

### 🔍 POI搜索配置 (utils/poiSearchConfig.js)
**作用**：兴趣点搜索和地理编码
- 关键词搜索
- 周边搜索
- 地址解析
- 逆地理编码

### 🧭 导航配置 (utils/navigationConfig.js)
**作用**：路线规划参数配置
- 多种出行方式支持
- 路线策略选择
- API调用封装

### 📊 路线步骤解析 (utils/routeStepParser.js)
**作用**：路线数据格式化和处理
- 导航指令生成
- 转向图标映射
- 语音提示生成
- 距离时间格式化

## 🔄 数据流程

### 典型导航流程
```
1. [首页] 用户点击"路线导航"
   ↓
2. [导航页面] 获取当前位置 → 搜索目的地 → 规划路线
   ↓
3. [路线指引页面] 选择导航模式 → 开始GPS导航
   ↓
4. [实时导航] GPS监听 → 位置更新 → 自动步骤切换
```

### 页面间数据传递
```javascript
// navigation.js → route-guide.js
wx.navigateTo({
  url: `../route-guide/route-guide?routeData=${routeData}&routeType=${routeType}&startLocation=${startLocation}&endLocation=${endLocation}`
});
```

## 🛠️ 开发指南

### 代码规范
- **模块化设计**：每个功能独立封装
- **统一错误处理**：使用errorHandler处理所有错误
- **Promise化API**：所有异步操作返回Promise
- **代码注释**：关键功能添加详细注释

### 添加新功能
1. 在对应的utils模块中添加配置
2. 使用amapManager进行API调用
3. 使用errorHandler处理错误
4. 遵循现有的命名规范

### 调试技巧
- 查看控制台日志了解API调用状态
- 使用config页面测试API连接
- 检查网络请求确认域名配置

## 🚨 常见问题解决

### API相关问题

**Q: API密钥无效**
```
A: 检查以下几点：
1. 确认密钥是否正确复制
2. 检查密钥是否已在高德平台激活
3. 确认密钥类型匹配（SDK/Web服务）
```

**Q: 网络请求失败**
```
A: 检查域名配置：
1. 微信公众平台是否已配置 https://restapi.amap.com
2. 网络连接是否正常
3. API调用是否超限
```

### 定位相关问题

**Q: 定位失败**
```
A: 权限和环境检查：
1. 确认已授权位置权限
2. 检查设备GPS是否开启
3. 在室外环境测试
4. 使用真机测试而非模拟器
```

**Q: GPS导航不准确**
```
A: 精度优化建议：
1. 在空旷环境使用
2. 等待GPS信号稳定
3. 调整监听频率（当前3秒）
4. 增加偏离检测阈值
```

### 功能相关问题

**Q: 静态地图不显示**
```
A: 图片加载检查：
1. 检查生成的URL是否有效
2. 确认图片尺寸配置
3. 测试网络图片访问权限
```

**Q: 路线规划失败**
```
A: 参数验证：
1. 确认起终点坐标有效
2. 检查路线类型配置
3. 验证距离是否超限
```

## 📈 性能优化

### 已实现的优化
- **SDK单例模式** - 避免重复初始化
- **权限预检查** - 减少无效API调用  
- **错误缓存** - 避免重复错误提示
- **资源自动清理** - 页面销毁时清理定时器
- **智能降级** - GPS失败时切换到演示模式

### 建议优化方向
- 添加路线缓存机制
- 实现离线地图支持
- 优化大数据量POI搜索
- 增加网络状态检测

## 📄 更新日志

### v1.0.0 (当前版本)
- ✅ 基础定位功能
- ✅ 静态地图显示
- ✅ 路线规划功能
- ✅ GPS实时导航
- ✅ 双API密钥架构
- ✅ 统一错误处理
- ✅ 权限管理优化

### 规划功能
- 🚧 离线地图支持
- 🚧 语音导航TTS
- 🚧 历史路线记录
- 🚧 个性化偏好设置

## 📞 技术支持

如果在使用过程中遇到问题：

1. **查看控制台日志** - 了解详细错误信息
2. **测试API连接** - 使用config页面检测配置
3. **检查权限设置** - 确认位置权限已授权
4. **验证网络环境** - 确保网络连接正常

---

**🎯 项目目标**：构建一个功能完整、用户体验优秀的微信小程序导航应用，展示高德地图API的强大功能。

**💡 技术亮点**：双API密钥架构、统一错误处理、GPS实时导航、模块化设计。
