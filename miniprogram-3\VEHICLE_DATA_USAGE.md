# 车辆数据使用指南

## 📊 **您的数据格式**

根据您提供的JSON结构，系统已完全适配：

```json
{
    "user": [
        {
            "id": 1,
            "role": "admin",
            "username": "admin",
            "password": "admin123",
            "_id": 0
        }
    ],
    "gps_data": [
        {
            "id": 1,
            "longitude": 52014141414,
            "latitude": 43.923,
            "_id": 0
        }
    ],
    "car_data": [
        {
            "id": 1,
            "speed": 123,
            "power": 87,
            "mod": 1,
            "_id": 0
        }
    ]
}
```

## 🔧 **数据字段说明**

### **用户数据 (user)**
- `id`: 用户ID
- `role`: 用户角色 (admin/user)
- `username`: 用户名
- `password`: 密码
- `_id`: 内部ID

### **GPS数据 (gps_data)**
- `id`: GPS数据ID
- `longitude`: 经度（支持大数字格式自动转换）
- `latitude`: 纬度
- `_id`: 内部ID

### **车辆数据 (car_data)**
- `id`: 车辆ID
- `speed`: 当前速度 (km/h)
- `power`: 电量百分比 (0-100)
- `mod`: 驾驶模式 (1=青少年, 2=成人, 3=老人)
- `_id`: 内部ID

## 🚀 **使用方法**

### **1. 初始化车辆数据管理器**

```javascript
// 在页面的 onLoad 中初始化
const vehicleDataManager = require('../../utils/vehicleDataManager.js');

Page({
  onLoad: function() {
    // 初始化车辆数据管理器
    vehicleDataManager.initialize()
      .then(result => {
        console.log('车辆数据管理器初始化成功');
        this.setupDataListeners();
      })
      .catch(error => {
        console.error('初始化失败:', error);
        wx.showToast({
          title: '连接车辆失败',
          icon: 'error'
        });
      });
  },
  
  setupDataListeners: function() {
    // 设置数据监听器
    vehicleDataManager.addDataListener('data_update', this.onVehicleDataUpdate.bind(this));
    vehicleDataManager.addDataListener('warning', this.onVehicleWarning.bind(this));
  }
});
```

### **2. 监听车辆数据更新**

```javascript
// 处理车辆数据更新
onVehicleDataUpdate: function(data) {
  console.log('车辆数据更新:', data);
  
  // 更新页面显示
  this.setData({
    battery: data.battery.level,
    speed: data.speed.current,
    mode: data.mode.current,
    location: {
      latitude: data.location.latitude,
      longitude: data.location.longitude
    },
    user: data.user.username,
    connected: data.connected
  });
  
  // 更新电池显示颜色
  const batteryColor = data.battery.level > 20 ? '#4CAF50' : '#FF5722';
  this.setData({ batteryColor });
},

// 处理车辆警告
onVehicleWarning: function(warning) {
  console.log('收到车辆警告:', warning);
  
  let title = '';
  let icon = 'none';
  
  switch (warning.type) {
    case 'low_battery':
      title = `电量不足：${warning.level}%`;
      icon = 'error';
      break;
    case 'overspeed':
      title = `超速警告：${warning.currentSpeed}km/h`;
      icon = 'error';
      break;
    case 'gps_invalid':
      title = 'GPS信号异常';
      icon = 'error';
      break;
  }
  
  wx.showToast({ title, icon });
}
```

### **3. 控制车辆**

```javascript
// 切换驾驶模式
switchMode: function(mode) {
  vehicleDataManager.switchDrivingMode(mode)
    .then(result => {
      wx.showToast({
        title: result.message,
        icon: 'success'
      });
    })
    .catch(error => {
      wx.showToast({
        title: '模式切换失败',
        icon: 'error'
      });
    });
},

// 按钮事件处理
onYouthModeClick: function() {
  this.switchMode(1); // 青少年模式
},

onAdultModeClick: function() {
  this.switchMode(2); // 成人模式
},

onElderlyModeClick: function() {
  this.switchMode(3); // 老人模式
}
```

### **4. 获取车辆数据**

```javascript
// 获取当前车辆数据
getCurrentVehicleData: function() {
  const data = vehicleDataManager.getVehicleData();
  console.log('当前车辆数据:', data);
  
  return {
    battery: `${data.battery.level}%`,
    speed: `${data.speed.current} km/h`,
    mode: this.getModeName(data.mode.current),
    location: `${data.location.latitude.toFixed(6)}, ${data.location.longitude.toFixed(6)}`,
    connected: data.connected ? '已连接' : '未连接'
  };
},

// 获取模式名称
getModeName: function(modeCode) {
  const modeNames = {
    1: '青少年模式',
    2: '成人模式',
    3: '老人模式'
  };
  return modeNames[modeCode] || '未知模式';
}
```

## 📱 **页面示例**

### **WXML 模板**

```xml
<!-- 车辆状态显示 -->
<view class="vehicle-status">
  <view class="status-item">
    <text class="label">电量:</text>
    <text class="value" style="color: {{batteryColor}}">{{battery}}%</text>
  </view>
  
  <view class="status-item">
    <text class="label">速度:</text>
    <text class="value">{{speed}} km/h</text>
  </view>
  
  <view class="status-item">
    <text class="label">模式:</text>
    <text class="value">{{getModeName(mode)}}</text>
  </view>
  
  <view class="status-item">
    <text class="label">连接:</text>
    <text class="value">{{connected ? '已连接' : '未连接'}}</text>
  </view>
</view>

<!-- 模式切换按钮 -->
<view class="mode-buttons">
  <button class="mode-btn {{mode === 1 ? 'active' : ''}}" 
          bindtap="onYouthModeClick">青少年模式</button>
  <button class="mode-btn {{mode === 2 ? 'active' : ''}}" 
          bindtap="onAdultModeClick">成人模式</button>
  <button class="mode-btn {{mode === 3 ? 'active' : ''}}" 
          bindtap="onElderlyModeClick">老人模式</button>
</view>
```

### **WXSS 样式**

```css
.vehicle-status {
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 10rpx;
  margin: 20rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

.label {
  color: #666;
  font-size: 28rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
  font-weight: bold;
}

.mode-buttons {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
}

.mode-btn {
  flex: 1;
  margin: 0 10rpx;
  background: #fff;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 24rpx;
}

.mode-btn.active {
  background: #007aff;
  color: white;
  border-color: #007aff;
}
```

## 🔍 **调试和测试**

### **1. 运行数据解析测试**

```javascript
// 在控制台中运行测试
const vehicleDataTest = require('../../test/vehicleDataTest.js');
vehicleDataTest.runAllTests();
```

### **2. 模拟数据测试**

```javascript
// 模拟接收MQTT数据
const testData = {
  "user": [{"id": 1, "role": "admin", "username": "admin", "_id": 0}],
  "gps_data": [{"id": 1, "longitude": 116.397428, "latitude": 39.90923, "_id": 0}],
  "car_data": [{"id": 1, "speed": 20, "power": 75, "mod": 2, "_id": 0}]
};

// 手动触发数据处理
vehicleDataManager.handleMqttMessage('vehicle/data', JSON.stringify(testData));
```

## ⚠️ **注意事项**

1. **经度格式**: 您的数据中经度为大数字格式 (52014141414)，系统会自动进行转换
2. **数据验证**: 系统会自动验证数据范围和完整性
3. **错误处理**: 所有数据解析错误都会被捕获和记录
4. **性能优化**: 数据更新会进行防抖处理，避免频繁更新UI

## 🎯 **下一步**

1. **配置MQTT域名** (参考 MQTT_DOMAIN_CONFIG.md)
2. **测试数据连接**
3. **集成到导航页面**
4. **添加更多车辆控制功能**

---

**现在您的电动车数据已完全适配，可以开始测试和使用了！** 🎉
