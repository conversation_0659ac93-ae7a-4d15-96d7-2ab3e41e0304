// app.js
const CONFIG = require('./utils/config.js');

App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 初始化地图配置
    this.initMapConfig();

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },

  // 初始化地图配置
  initMapConfig() {
    // 设置地图API密钥到全局
    wx.setStorageSync('AMAP_KEY', CONFIG.AMAP_KEY);
    wx.setStorageSync('AMAP_WEB_KEY', CONFIG.AMAP_WEB_KEY);

    console.log('地图API密钥已初始化:', {
      AMAP_KEY: CONFIG.AMAP_KEY,
      AMAP_WEB_KEY: CONFIG.AMAP_WEB_KEY
    });
  },
  globalData: {
    userInfo: null
  }
})
