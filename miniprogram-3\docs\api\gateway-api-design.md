# 网关API接口设计文档

## 概述
为解决微信小程序无法直接连接本地MQTT服务器的问题，设计HTTP API网关接口，实现小程序与本地数据库的数据交互。

## API基础信息

### 基础URL
```
开发环境: http://localhost:3000/api
生产环境: https://your-domain.com/api
```

### 通用响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2023-12-21T10:30:00.000Z"
}
```

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "code": "ERROR_CODE",
  "timestamp": "2023-12-21T10:30:00.000Z"
}
```

## 车辆数据接口

### 1. 获取车辆实时数据
```
GET /vehicle/data
```

**响应示例:**
```json
{
  "success": true,
  "message": "获取车辆数据成功",
  "data": {
    "user": "admin",
    "password": "admin123",
    "gps": {
      "longitude": 116.397428,
      "latitude": 39.90923
    },
    "car": {
      "speed": 12.5,
      "power": 84,
      "mode": "adult",
      "connected": true
    },
    "_id": "1703123456789",
    "timestamp": "2023-12-21T10:30:00.000Z"
  }
}
```

### 2. 获取车辆历史数据
```
GET /vehicle/history?limit=10&offset=0
```

**查询参数:**
- `limit`: 返回数据条数（默认10，最大100）
- `offset`: 偏移量（默认0）
- `startTime`: 开始时间（ISO格式）
- `endTime`: 结束时间（ISO格式）

### 3. 设置车辆模式
```
POST /vehicle/mode
Content-Type: application/json
```

**请求体:**
```json
{
  "mode": "youth" // youth | adult | elderly
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "模式设置成功",
  "data": {
    "mode": "youth",
    "timestamp": "2023-12-21T10:30:00.000Z"
  }
}
```

### 4. 获取车辆状态
```
GET /vehicle/status
```

**响应示例:**
```json
{
  "success": true,
  "message": "获取状态成功",
  "data": {
    "connected": true,
    "lastUpdate": "2023-12-21T10:30:00.000Z",
    "battery": 84,
    "mode": "adult",
    "location": {
      "longitude": 116.397428,
      "latitude": 39.90923
    }
  }
}
```

## 用户认证接口

### 1. 用户登录
```
POST /auth/login
Content-Type: application/json
```

**请求体:**
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "jwt_token_here",
    "user": {
      "id": "user_id",
      "username": "admin",
      "role": "admin"
    }
  }
}
```

### 2. 验证Token
```
GET /auth/verify
Authorization: Bearer jwt_token_here
```

## 实时数据接口

### 1. 长轮询获取实时数据
```
GET /vehicle/realtime?timeout=30
```

**查询参数:**
- `timeout`: 超时时间（秒，默认30，最大60）

**说明:** 如果在超时时间内有新数据，立即返回；否则超时返回空数据

### 2. 服务器发送事件 (SSE)
```
GET /vehicle/stream
Accept: text/event-stream
```

**响应格式:**
```
data: {"type":"vehicle_data","data":{...}}

data: {"type":"mode_change","data":{"mode":"youth"}}

data: {"type":"connection_status","data":{"connected":false}}
```

## 网关实现示例 (Node.js + Express)

### 基础服务器设置
```javascript
const express = require('express');
const cors = require('cors');
const mqtt = require('mqtt');
const app = express();

// 中间件
app.use(cors());
app.use(express.json());

// MQTT客户端连接本地服务器
const mqttClient = mqtt.connect('mqtt://localhost:1883', {
  username: 'mdfk',
  password: '1HUJIFDAHSOUF',
  clientId: 'gateway_' + Date.now()
});

// 存储最新的车辆数据
let latestVehicleData = null;

// 监听MQTT消息
mqttClient.on('message', (topic, message) => {
  if (topic === 'vehicle/data') {
    latestVehicleData = JSON.parse(message.toString());
  }
});

// 订阅车辆数据主题
mqttClient.on('connect', () => {
  mqttClient.subscribe('vehicle/data');
  console.log('网关已连接到本地MQTT服务器');
});
```

### API路由实现
```javascript
// 获取车辆实时数据
app.get('/api/vehicle/data', (req, res) => {
  if (!latestVehicleData) {
    return res.status(404).json({
      success: false,
      message: '暂无车辆数据',
      timestamp: new Date().toISOString()
    });
  }
  
  res.json({
    success: true,
    message: '获取车辆数据成功',
    data: latestVehicleData,
    timestamp: new Date().toISOString()
  });
});

// 设置车辆模式
app.post('/api/vehicle/mode', (req, res) => {
  const { mode } = req.body;
  
  if (!['youth', 'adult', 'elderly'].includes(mode)) {
    return res.status(400).json({
      success: false,
      message: '无效的模式参数',
      timestamp: new Date().toISOString()
    });
  }
  
  // 发送MQTT命令
  const command = {
    type: 'setMode',
    mode: mode,
    timestamp: new Date().toISOString()
  };
  
  mqttClient.publish('vehicle/command', JSON.stringify(command));
  
  res.json({
    success: true,
    message: '模式设置成功',
    data: { mode, timestamp: new Date().toISOString() }
  });
});

// 启动服务器
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`网关服务器运行在端口 ${PORT}`);
});
```

## 部署建议

### 1. 本地开发
- 网关运行在 `http://localhost:3000`
- 小程序开发工具中启用"不校验合法域名"选项

### 2. 生产环境
- 使用云服务器部署网关
- 配置HTTPS证书
- 在微信公众平台配置域名白名单

### 3. 内网穿透（临时方案）
```bash
# 使用ngrok暴露本地服务
ngrok http 3000

# 获得公网HTTPS地址，如：
# https://abc123.ngrok.io
```

## 安全考虑

### 1. 认证授权
- 实现JWT token认证
- API访问频率限制
- IP白名单控制

### 2. 数据加密
- HTTPS传输加密
- 敏感数据字段加密
- 请求签名验证

### 3. 错误处理
- 统一错误响应格式
- 详细日志记录
- 异常情况降级处理
