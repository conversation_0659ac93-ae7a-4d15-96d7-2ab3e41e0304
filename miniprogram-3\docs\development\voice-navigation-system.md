# 实时导航指令播报功能技术文档

## 概述

本文档记录了微信小程序电动车导航系统中实时导航指令播报功能的完整实现，包括语音合成、播报时机控制、电动车特有播报内容和用户设置界面。

## 系统架构

### 核心组件

1. **VoiceManager (语音管理器)** - `utils/voiceManager.js`
   - 语音合成和播放控制
   - 配置管理和状态跟踪
   - 播报队列和重试机制

2. **RouteStepParser (路线解析器)** - `utils/routeStepParser.js`
   - 导航指令文本生成
   - 电动车特有播报内容
   - 语音文本优化

3. **Route-Guide Page (导航页面)** - `pages/route-guide/`
   - 用户界面和交互控制
   - 语音设置面板
   - 车辆数据监控集成

## 功能特性

### 1. 基础语音播报功能

#### 1.1 语音合成配置
- **音量控制**: 0-100% 可调节
- **语速控制**: 0.5x-2.0x 可调节
- **语言设置**: 中文(zh-CN)
- **自动播放**: 支持开启/关闭

#### 1.2 播报时机控制
- **位置变化触发**: 基于GPS位置变化自动播报
- **重复播报控制**: 30秒内相同内容不重复播报
- **播报队列管理**: 支持播报队列和优先级控制

### 2. 导航指令播报

#### 2.1 标准导航指令
```javascript
// 基础导航指令示例
"前方100米左转"
"直行500米"
"到达目的地"
```

#### 2.2 距离播报优化
- **远距离**: "剩余2.5公里"
- **中距离**: "前方200米左转"
- **近距离**: "即将左转"

#### 2.3 电动车专用增强
- **安全提醒**: "请减速慢行"
- **路况提醒**: "注意车流"
- **坡度提醒**: "注意控制车速"

### 3. 电动车特有播报

#### 3.1 电量监控播报
```javascript
// 电量不足播报
"电量不足20%，建议尽快充电"
"电量严重不足，仅剩10%，请立即寻找充电站"
```

#### 3.2 充电站推荐
```javascript
// 充电站播报
"前方500米有充电站'国家电网充电站'，是否前往充电？"
```

#### 3.3 限速和限行提醒
```javascript
// 限速播报
"当前限速25公里每小时，请控制车速"

// 限行播报
"前方进入限行区域，电动车请注意绕行"
```

#### 3.4 天气和路况播报
```javascript
// 天气播报
"当前下雨天气，路面湿滑，请减速慢行"
"当前风力较大，请注意保持平衡"

// 拥堵播报
"前方路段拥堵，预计延误5分钟，建议选择其他路线"
```

### 4. 语音设置界面

#### 4.1 基础设置
- **语音开关**: 一键开启/关闭语音播报
- **音量调节**: 滑块控制，0-100%
- **语速调节**: 滑块控制，0.5x-2.0x

#### 4.2 播报内容选择
- **导航指令**: 基础转向和距离播报
- **电量提醒**: 低电量警告播报
- **限速提醒**: 超速和限速区域播报
- **充电站推荐**: 附近充电站信息播报

#### 4.3 测试功能
- **语音测试**: 一键测试当前语音设置
- **实时预览**: 设置变更即时生效

## 技术实现

### 1. VoiceManager 核心功能

#### 1.1 初始化和配置
```javascript
// 语音管理器初始化
VoiceManager.init()
  .then(() => {
    console.log('语音管理器初始化成功');
  });

// 配置设置
VoiceManager.setConfig({
  enabled: true,
  volume: 0.8,
  rate: 1.0
});
```

#### 1.2 语音播报主函数
```javascript
// 基础语音播报
VoiceManager.speak(text, options)
  .then(() => console.log('播报完成'))
  .catch(error => console.error('播报失败', error));

// 导航指令播报
VoiceManager.speakNavigation(instruction, distance);

// 电动车特有信息播报
VoiceManager.speakElectricVehicleInfo(type, data);
```

#### 1.3 播报状态管理
```javascript
// 检查是否需要播报
VoiceManager.shouldSpeak(text);

// 停止播报
VoiceManager.stop();

// 获取播报状态
VoiceManager.getStatus();
```

### 2. 语音文本生成

#### 2.1 基础文本生成
```javascript
// 生成导航语音文本
const voiceText = routeStepParser.generateVoiceText(step, remainingDistance, {
  vehicleType: 'electrobike'
});
```

#### 2.2 电动车特有文本生成
```javascript
// 生成电动车特有播报文本
const electricText = routeStepParser.generateElectricVehicleVoiceText(type, data);
```

### 3. 车辆数据监控集成

#### 3.1 电量监控
```javascript
// 监听电量变化
vehicleDataManager.onBatteryUpdate((batteryData) => {
  if (batteryData.level <= 20) {
    voiceManager.speakElectricVehicleInfo('lowBattery', batteryData);
  }
});
```

#### 3.2 速度监控
```javascript
// 监听速度变化
vehicleDataManager.onSpeedUpdate((speedData) => {
  const speedLimit = 25; // 电动车限速
  if (speedData.current > speedLimit) {
    voiceManager.speakElectricVehicleInfo('speedLimit', {
      limit: speedLimit,
      current: speedData.current
    });
  }
});
```

### 4. 用户界面实现

#### 4.1 语音设置面板
```xml
<!-- 语音设置模态框 -->
<view class="voice-settings-modal" wx:if="{{showVoiceSettings}}">
  <view class="modal-content">
    <!-- 音量设置 -->
    <slider value="{{voiceVolume}}" min="0" max="100" 
            bindchange="onVolumeChange" show-value/>
    
    <!-- 语速设置 -->
    <slider value="{{voiceRate}}" min="0.5" max="2.0" 
            bindchange="onRateChange" show-value/>
  </view>
</view>
```

#### 4.2 语音控制按钮
```xml
<!-- 语音开关按钮 -->
<button class="control-button" bindtap="toggleVoice">
  <text>{{voiceEnabled ? '🔊 语音开' : '🔇 语音关'}}</text>
</button>

<!-- 语音设置按钮 -->
<button class="control-button" bindtap="showVoiceSettings">
  <text>⚙️ 语音设置</text>
</button>
```

## 性能优化

### 1. 播报性能优化
- **重复播报控制**: 30秒内相同内容不重复播报
- **播报队列管理**: 避免播报冲突和重叠
- **智能播报时机**: 基于位置变化和导航状态

### 2. 内存和存储优化
- **配置本地存储**: 用户设置自动保存到本地存储
- **缓存管理**: 播报状态和配置信息缓存
- **资源释放**: 及时释放音频资源

### 3. 错误处理和重试
- **播报失败重试**: 最多3次重试机制
- **降级处理**: 语音播报失败时显示文字提示
- **异常恢复**: 自动恢复播报功能

## 兼容性说明

### 1. 微信小程序限制
- **TTS限制**: 微信小程序暂不支持原生TTS，使用模拟播报
- **音频播放**: 使用wx.playBackgroundAudio进行提示音播放
- **权限要求**: 需要用户授权音频播放权限

### 2. 设备兼容性
- **iOS设备**: 完全支持所有功能
- **Android设备**: 完全支持所有功能
- **低版本兼容**: 自动降级到文字提示

## 使用指南

### 1. 开发者使用
```javascript
// 初始化语音功能
const voiceManager = require('../../utils/voiceManager.js');
voiceManager.init();

// 播报导航指令
voiceManager.speakNavigation('前方100米左转', 100);

// 播报电动车信息
voiceManager.speakElectricVehicleInfo('lowBattery', { level: 15 });
```

### 2. 用户使用
1. 点击语音按钮开启/关闭语音播报
2. 点击语音设置按钮调整音量和语速
3. 在设置面板中选择需要的播报内容
4. 使用测试按钮验证语音设置

## 后续优化建议

1. **真实TTS集成**: 集成第三方TTS服务提供真实语音合成
2. **多语言支持**: 支持英语、粤语等多种语言播报
3. **个性化语音**: 支持不同音色和语音风格选择
4. **智能播报**: 基于AI的智能播报内容生成
5. **离线播报**: 支持离线语音包下载和播报
