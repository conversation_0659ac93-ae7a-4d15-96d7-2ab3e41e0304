# 连接问题修复指南

## 问题描述

小程序出现连接错误：
```
GET http://localhost:3000/api/vehicle/status net::ERR_CONNECTION_REFUSED
HTTP请求失败: {errMsg: "request:fail "}
```

## 解决方案

### ✅ 问题已解决

**原因**: 端口3000被占用，网关服务器无法启动

**解决方法**: 
1. 修改网关服务器端口为3001
2. 更新小程序API地址配置
3. 重新启动网关服务器

### 🚀 快速启动步骤

#### 1. 启动网关服务器
```bash
cd miniprogram-3/gateway
node server.js
```

#### 2. 验证服务器运行
```bash
curl http://localhost:3001/api/health
```

**期望响应**:
```json
{
  "success": true,
  "message": "网关服务正常",
  "status": {
    "mqtt": false,
    "database": true,
    "uptime": 67.5
  }
}
```

#### 3. 测试车辆数据接口
```bash
curl http://localhost:3001/api/vehicle/data
```

**期望响应**:
```json
{
  "success": true,
  "message": "获取车辆数据成功",
  "data": {
    "user": "admin",
    "gps": {
      "longitude": 110.45,
      "latitude": 43.923
    },
    "car": {
      "speed": 25,
      "power": 87,
      "mode": "youth",
      "connected": true
    }
  },
  "dataSource": "mock"
}
```

### 📱 小程序配置

#### 确认API地址
小程序中的API地址已更新为：`http://localhost:3001/api`

#### 开发工具设置
1. 打开微信开发者工具
2. 进入"详情" → "本地设置"
3. 确保勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

### 🔧 当前配置状态

#### 网关服务器
- **端口**: 3001 (已修改)
- **API地址**: http://localhost:3001/api
- **状态**: ✅ 运行正常

#### MQTT配置
- **服务器**: s3.v100.vip:33880
- **用户名**: mdfk
- **客户端ID**: mdfk124xfasrf
- **状态**: ⚠️ 未连接 (使用本地数据)

#### 数据库
- **类型**: 本地模拟数据库
- **状态**: ✅ 连接正常
- **数据**: 使用你提供的数据库结构

### 🧪 测试结果

所有API接口测试通过：

#### ✅ 健康检查
- 端点: `GET /api/health`
- 状态: 正常

#### ✅ 车辆数据获取
- 端点: `GET /api/vehicle/data`
- 数据源: mock (本地数据)
- GPS: 110.45, 43.923
- 车辆: 速度25, 电量87%, 青年模式

#### ✅ 车辆状态查询
- 端点: `GET /api/vehicle/status`
- MQTT状态: 未连接
- 数据库状态: 已连接

#### ✅ 模式切换
- 端点: `POST /api/vehicle/mode`
- 支持模式: youth(1), adult(2), elderly(3)
- 状态: 正常

#### ✅ 数据库操作
- GPS数据查询: 正常
- 车辆数据查询: 正常
- 数据更新: 正常

#### ✅ 错误处理
- 无效模式: 正确拒绝
- 无效数据表: 正确拒绝

### 📊 数据流程

#### 当前数据流
```
小程序 → HTTP API (3001) → 网关服务器 → 本地数据库模拟
```

#### 完整数据流 (MQTT连接后)
```
小程序 → HTTP API (3001) → 网关服务器 → MQTT (s3.v100.vip:33880) → 真实数据库
```

### 🔍 MQTT连接状态

**当前状态**: 未连接到MQTT服务器
**原因**: 可能的网络连接问题或MQTT服务器配置
**影响**: 使用本地模拟数据，功能正常

**测试MQTT连接**:
```bash
node test-mqtt.js
```

### 🎯 下一步操作

#### 1. 小程序测试
现在可以在小程序中测试连接：
- 确保网关服务器运行在3001端口
- 小程序应该能正常获取车辆数据
- 模式切换功能应该正常工作

#### 2. MQTT连接调试 (可选)
如果需要连接真实MQTT服务器：
```bash
node test-mqtt.js
```
查看连接日志，排查网络问题

#### 3. 生产部署 (未来)
- 部署到云服务器
- 配置HTTPS证书
- 添加域名白名单

### 🚨 故障排除

#### 如果小程序仍然连接失败
1. **检查网关服务器状态**:
   ```bash
   curl http://localhost:3001/api/health
   ```

2. **检查端口占用**:
   ```bash
   netstat -ano | findstr :3001
   ```

3. **重启网关服务器**:
   ```bash
   # 停止当前进程 (Ctrl+C)
   # 重新启动
   node server.js
   ```

4. **检查小程序开发工具设置**:
   - 确认"不校验合法域名"已勾选
   - 检查控制台网络请求日志

#### 如果需要使用其他端口
修改以下文件中的端口配置：
- `miniprogram-3/gateway/server.js` (第603行)
- `miniprogram-3/utils/httpDataManager.js` (第9行)

### 📝 总结

✅ **问题已解决**: 网关服务器成功运行在端口3001
✅ **API接口正常**: 所有测试通过
✅ **数据获取正常**: 使用本地数据库结构
✅ **模式切换正常**: 支持三种驾驶模式
⚠️ **MQTT待连接**: 可选，不影响基本功能

现在你的小程序应该可以正常连接并获取车辆数据了！
