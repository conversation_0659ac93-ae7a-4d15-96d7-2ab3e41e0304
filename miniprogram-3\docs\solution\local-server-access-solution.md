# 微信小程序访问本地服务器解决方案

## 问题分析

### 为什么小程序无法连接本地MQTT服务器？

#### 1. 微信小程序网络限制
- **域名白名单限制**: 只能访问在微信公众平台配置的域名
- **协议限制**: 只支持 HTTPS 和 WSS 协议
- **本地地址限制**: 无法访问 `localhost`、`127.0.0.1`、`192.168.x.x` 等本地地址
- **端口限制**: 只能使用标准端口（443 for HTTPS/WSS）

#### 2. 当前架构问题
```
❌ 无法工作的架构:
小程序 → wss://s3.v100.vip:33880/mqtt → 本地MQTT服务器
                ↑
            连接失败点
```

## 解决方案

### 方案1：HTTP API网关（推荐）⭐

#### 架构设计
```
✅ 推荐架构:
小程序 → HTTP API → 网关服务器 → 本地MQTT → 本地数据库
```

#### 优势
- ✅ 简单易实现
- ✅ 稳定可靠
- ✅ 适合生产环境
- ✅ 支持数据缓存和处理
- ✅ 可以添加认证和权限控制

#### 实现步骤

##### 1. 启动网关服务器
```bash
cd miniprogram-3/gateway
npm install
npm start
```

##### 2. 配置小程序
在小程序中使用HTTP API替代MQTT：
```javascript
// 使用新的HTTP数据管理器
const httpDataManager = require('./utils/httpDataManager.js');

// 初始化
httpDataManager.initialize()
  .then(result => {
    console.log('HTTP数据源初始化成功:', result);
  })
  .catch(error => {
    console.error('初始化失败:', error);
  });
```

##### 3. API接口说明
- `GET /api/vehicle/data` - 获取车辆实时数据
- `POST /api/vehicle/mode` - 设置车辆模式
- `GET /api/vehicle/status` - 获取车辆状态
- `GET /api/health` - 健康检查

### 方案2：内网穿透

#### 使用ngrok（临时方案）
```bash
# 安装ngrok
npm install -g ngrok

# 暴露本地3000端口
ngrok http 3000

# 获得公网HTTPS地址，如：
# https://abc123.ngrok.io
```

#### 使用frp（长期方案）
1. 在云服务器上部署frp服务端
2. 在本地运行frp客户端
3. 配置域名解析

### 方案3：云服务器中转

#### 架构设计
```
小程序 → 云MQTT服务器 ← 本地MQTT客户端 ← 本地数据库
```

#### 实现步骤
1. 在云服务器部署MQTT broker
2. 本地MQTT客户端连接到云服务器
3. 小程序连接云服务器MQTT

## 具体实现

### 网关服务器配置

#### 1. 环境要求
- Node.js >= 14.0.0
- 本地MQTT服务器运行在 `localhost:1883`
- 数据库连接正常

#### 2. 启动步骤
```bash
# 进入网关目录
cd miniprogram-3/gateway

# 安装依赖
npm install

# 启动服务器
npm start

# 开发模式（自动重启）
npm run dev
```

#### 3. 验证服务
```bash
# 健康检查
curl http://localhost:3000/api/health

# 获取车辆数据
curl http://localhost:3000/api/vehicle/data

# 设置车辆模式
curl -X POST http://localhost:3000/api/vehicle/mode \
  -H "Content-Type: application/json" \
  -d '{"mode": "youth"}'
```

### 小程序配置

#### 1. 开发环境配置
在微信开发者工具中：
1. 打开"详情" → "本地设置"
2. 勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

#### 2. 生产环境配置
1. 部署网关到云服务器
2. 配置HTTPS证书
3. 在微信公众平台添加域名白名单

### 数据流程

#### 1. 数据获取流程
```
车辆设备 → 本地MQTT → 网关服务器 → HTTP API → 小程序
```

#### 2. 控制指令流程
```
小程序 → HTTP API → 网关服务器 → 本地MQTT → 车辆设备
```

## 部署指南

### 开发环境部署

#### 1. 本地开发
```bash
# 启动网关服务器
cd miniprogram-3/gateway
npm start

# 小程序开发工具中启用"不校验合法域名"
# API地址: http://localhost:3000/api
```

#### 2. 测试验证
```javascript
// 在小程序中测试
const httpDataManager = require('./utils/httpDataManager.js');

httpDataManager.initialize()
  .then(result => {
    console.log('连接成功:', result);
    return httpDataManager.getVehicleData();
  })
  .then(data => {
    console.log('车辆数据:', data);
  });
```

### 生产环境部署

#### 1. 云服务器部署
```bash
# 在云服务器上
git clone your-repo
cd miniprogram-3/gateway
npm install --production
npm start
```

#### 2. 配置HTTPS
```bash
# 使用Let's Encrypt获取免费证书
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

#### 3. 配置Nginx反向代理
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    location /api/ {
        proxy_pass http://localhost:3000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 4. 微信公众平台配置
1. 登录微信公众平台
2. 进入"开发" → "开发设置"
3. 在"服务器域名"中添加：
   - request合法域名: `https://your-domain.com`

## 安全考虑

### 1. 认证授权
```javascript
// 添加JWT认证
const jwt = require('jsonwebtoken');

app.use('/api', (req, res, next) => {
  const token = req.headers.authorization;
  if (!token) {
    return res.status(401).json({ message: '缺少认证token' });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ message: '无效的token' });
  }
});
```

### 2. 访问频率限制
```javascript
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100 // 限制每个IP 15分钟内最多100个请求
});

app.use('/api', limiter);
```

### 3. 数据加密
```javascript
// 敏感数据加密
const crypto = require('crypto');

function encryptData(data) {
  const cipher = crypto.createCipher('aes192', process.env.ENCRYPT_KEY);
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}
```

## 故障排除

### 常见问题

#### 1. 网关服务器启动失败
```bash
# 检查端口占用
netstat -tulpn | grep :3000

# 检查Node.js版本
node --version

# 查看详细错误日志
npm start 2>&1 | tee gateway.log
```

#### 2. MQTT连接失败
```bash
# 检查MQTT服务器状态
mosquitto_pub -h localhost -p 1883 -t test -m "hello"

# 检查防火墙设置
sudo ufw status

# 查看MQTT日志
tail -f /var/log/mosquitto/mosquitto.log
```

#### 3. 小程序请求失败
- 检查开发工具中的"不校验合法域名"设置
- 确认API地址正确
- 查看控制台网络请求日志

### 监控和日志

#### 1. 服务器监控
```javascript
// 添加健康检查端点
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    mqtt: connectionStatus.mqtt,
    database: connectionStatus.database
  });
});
```

#### 2. 日志记录
```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

## 总结

通过HTTP API网关方案，你可以：

1. ✅ 解决小程序无法访问本地MQTT的问题
2. ✅ 保持现有的本地MQTT服务器和数据库
3. ✅ 提供稳定可靠的数据访问接口
4. ✅ 支持未来的功能扩展和优化

这个方案既解决了技术限制问题，又保持了架构的灵活性，是最适合你当前需求的解决方案。
