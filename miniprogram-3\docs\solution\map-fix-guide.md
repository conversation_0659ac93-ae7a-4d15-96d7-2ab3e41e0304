# 地图显示问题修复指南

## 问题描述
导航页面无法显示地图，显示空白区域。

## 已实施的修复方案

### 1. 添加地图初始化代码
- **文件**: `app.js`
- **修改**: 添加了地图API密钥的全局初始化
- **作用**: 确保地图API密钥在应用启动时正确设置

```javascript
// 初始化地图配置
initMapConfig() {
  wx.setStorageSync('AMAP_KEY', CONFIG.AMAP_KEY);
  wx.setStorageSync('AMAP_WEB_KEY', CONFIG.AMAP_WEB_KEY);
  console.log('地图API密钥已初始化');
}
```

### 2. 完善导航页面地图初始化
- **文件**: `pages/navigation/navigation.js`
- **修改**: 添加了`onReady`方法和地图上下文创建
- **作用**: 确保地图组件正确初始化

```javascript
onReady: function() {
  // 创建地图上下文
  this.mapContext = wx.createMapContext('navigationMap', this);
  
  // 验证地图API密钥
  const amapKey = wx.getStorageSync('AMAP_KEY');
  console.log('地图API密钥:', amapKey);
  
  // 设置地图初始状态
  this.setData({
    mapCenter: this.data.currentLocation || {
      longitude: CONFIG.MAP_CONFIG.DEFAULT_LONGITUDE,
      latitude: CONFIG.MAP_CONFIG.DEFAULT_LATITUDE
    },
    mapScale: 16
  });
}
```

### 3. 添加地图位置移动方法
- **文件**: `pages/navigation/navigation.js`
- **修改**: 添加了`moveToLocation`方法
- **作用**: 支持程序化移动地图到指定位置

```javascript
moveToLocation: function(location) {
  if (location && location.longitude && location.latitude) {
    this.setData({
      mapCenter: {
        longitude: location.longitude,
        latitude: location.latitude
      },
      mapScale: 16
    });
    
    // 如果地图上下文已创建，使用地图API移动
    if (this.mapContext) {
      this.mapContext.moveToLocation({
        longitude: location.longitude,
        latitude: location.latitude
      });
    }
  }
}
```

### 4. 创建地图测试页面
- **文件**: `pages/map-test/`
- **作用**: 独立测试地图功能，便于调试
- **功能**: 
  - 显示API密钥状态
  - 测试地图基本功能
  - 位置获取和标记添加
  - 缩放控制

## 测试步骤

### 1. 使用地图测试页面
1. 在主页面点击"地图测试"按钮
2. 查看API密钥是否正确显示
3. 测试地图是否能正常显示
4. 测试位置获取功能
5. 测试地图缩放和标记功能

### 2. 验证导航页面
1. 从主页面进入导航页面
2. 检查控制台是否有地图相关错误
3. 验证地图是否正常显示
4. 测试地图交互功能

## 可能的问题和解决方案

### 问题1: API密钥无效
**症状**: 地图显示空白，控制台有API密钥相关错误
**解决**: 检查`utils/config.js`中的`AMAP_KEY`是否正确

### 问题2: 网络权限问题
**症状**: 地图瓦片无法加载
**解决**: 检查`app.json`中的网络权限配置

### 问题3: 地图容器尺寸问题
**症状**: 地图显示异常或不完整
**解决**: 检查CSS样式，确保地图容器有正确的宽高

### 问题4: 位置权限问题
**症状**: 无法获取当前位置
**解决**: 检查`app.json`中的位置权限配置

## 调试信息

在控制台查看以下信息：
- `地图API密钥: [密钥值]`
- `地图上下文创建成功`
- `地图初始化完成，中心点: [坐标]`
- `获取当前位置成功: [位置信息]`

## 下一步优化

1. 添加地图加载状态指示器
2. 实现地图加载失败的重试机制
3. 优化地图性能和用户体验
4. 添加更多地图交互功能

## 联系支持

如果问题仍然存在，请提供：
1. 控制台错误信息
2. 地图测试页面的显示结果
3. 设备和微信版本信息
