# MQTT集成完整指南

## 概述

本指南详细说明如何将你的本地MQTT服务器和数据库与微信小程序集成，实现电动车数据的实时通信。

## 系统架构

```
电动车设备 ←→ 本地MQTT服务器 ←→ 网关服务器 ←→ HTTP API ←→ 微信小程序
                ↓
            本地数据库
```

## MQTT配置信息

### 服务器配置
- **主机**: s3.v100.vip
- **端口**: 33880
- **用户名**: mdfk
- **密码**: 1HUJIFDAHSOUF
- **客户端ID**: mdfk124xfasrf

### 数据库结构
```json
{
  "user": [
    {
      "id": 1,
      "role": "admin", 
      "username": "admin",
      "password": "admin123",
      "_id": 0
    }
  ],
  "gps_data": [
    {
      "id": 1,
      "longitude": 110.45,
      "latitude": 43.923,
      "_id": 0
    }
  ],
  "car_data": [
    {
      "id": 1,
      "speed": 25,
      "power": 87,
      "mod": 1,
      "_id": 0
    }
  ]
}
```

### 模式映射
- `mod: 1` → `youth` (青年模式)
- `mod: 2` → `adult` (成人模式)  
- `mod: 3` → `elderly` (老年模式)

## MQTT命令协议

### 数据库查询命令
**主题**: `database/query`
```json
{
  "cmd": "get",
  "id": 1,
  "db": "gps_data"
}
```

### 数据库更新命令
**主题**: `database/update`
```json
{
  "cmd": "update",
  "id": 1,
  "db": "gps_data",
  "type": "longitude",
  "value": 52014141414
}
```

### 车辆模式切换命令
**主题**: `vehicle/command`
```json
{
  "type": "setMode",
  "mode": "youth",
  "timestamp": "2023-12-21T10:30:00.000Z",
  "source": "miniprogram"
}
```

## 快速开始

### 1. 启动网关服务器

#### Windows用户
```bash
cd miniprogram-3/gateway
start.bat
```

#### Linux/Mac用户
```bash
cd miniprogram-3/gateway
chmod +x start.sh
./start.sh
```

#### 手动启动
```bash
cd miniprogram-3/gateway
npm install
npm start
```

### 2. 验证连接

#### 测试MQTT连接
```bash
node test-mqtt.js
```

#### 测试API接口
```bash
npm test
```

#### 手动测试
```bash
# 健康检查
curl http://localhost:3000/api/health

# 获取车辆数据
curl http://localhost:3000/api/vehicle/data

# 设置车辆模式
curl -X POST http://localhost:3000/api/vehicle/mode \
  -H "Content-Type: application/json" \
  -d '{"mode": "youth"}'
```

### 3. 配置小程序

#### 开发环境
1. 在微信开发者工具中打开项目
2. 进入"详情" → "本地设置"
3. 勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"
4. 确保小程序使用HTTP数据源

#### 生产环境
1. 将网关部署到云服务器
2. 配置HTTPS证书
3. 在微信公众平台添加域名白名单
4. 更新小程序API地址

## API接口文档

### 基础接口

#### 健康检查
```
GET /api/health
```

**响应示例:**
```json
{
  "success": true,
  "message": "网关服务正常",
  "status": {
    "mqtt": true,
    "database": true,
    "uptime": 3600
  }
}
```

#### 获取车辆数据
```
GET /api/vehicle/data
```

**响应示例:**
```json
{
  "success": true,
  "message": "获取车辆数据成功",
  "data": {
    "user": "admin",
    "gps": {
      "longitude": 110.45,
      "latitude": 43.923
    },
    "car": {
      "speed": 25,
      "power": 87,
      "mode": "youth",
      "connected": true
    },
    "_id": "1703123456789",
    "timestamp": "2023-12-21T10:30:00.000Z"
  },
  "dataSource": "database"
}
```

#### 设置车辆模式
```
POST /api/vehicle/mode
Content-Type: application/json
```

**请求体:**
```json
{
  "mode": "youth"
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "模式设置成功",
  "data": {
    "mode": "youth",
    "modeNumber": 1,
    "timestamp": "2023-12-21T10:30:00.000Z",
    "mqttSent": true
  }
}
```

### 数据库接口

#### 获取数据库表数据
```
GET /api/database/:table?id=1
```

**支持的表**: `gps_data`, `car_data`, `user`

#### 更新数据库表数据
```
PUT /api/database/:table
Content-Type: application/json
```

**请求体:**
```json
{
  "type": "speed",
  "value": 30,
  "id": 1
}
```

## 数据流程

### 1. 小程序获取数据流程
```
小程序 → HTTP API → 网关服务器 → MQTT查询 → 本地数据库 → 返回数据
```

### 2. 小程序控制车辆流程
```
小程序 → HTTP API → 网关服务器 → MQTT命令 → 车辆设备
```

### 3. 车辆数据上报流程
```
车辆设备 → MQTT消息 → 本地数据库 → 网关缓存 → 小程序获取
```

## 故障排除

### 常见问题

#### 1. MQTT连接失败
**症状**: 网关日志显示MQTT连接错误

**解决方案**:
- 检查网络连接到 s3.v100.vip:33880
- 验证用户名密码是否正确
- 检查防火墙设置
- 运行 `node test-mqtt.js` 测试连接

#### 2. 小程序无法获取数据
**症状**: 小程序显示"数据获取失败"

**解决方案**:
- 确认网关服务器正在运行 (http://localhost:3000/api/health)
- 检查小程序是否启用"不校验合法域名"
- 验证API地址配置是否正确
- 运行 `npm test` 测试API接口

#### 3. 数据不实时更新
**症状**: 数据更新延迟或不更新

**解决方案**:
- 检查MQTT消息是否正常发送和接收
- 验证数据库更新命令格式是否正确
- 检查网关服务器日志
- 确认车辆设备MQTT连接状态

#### 4. 模式切换失败
**症状**: 车辆模式切换不生效

**解决方案**:
- 检查MQTT命令是否发送成功
- 验证模式值是否正确 (youth/adult/elderly)
- 确认车辆设备是否接收到命令
- 检查数据库中mod字段是否更新

### 调试工具

#### 1. 查看网关日志
```bash
npm start
# 观察控制台输出
```

#### 2. 测试MQTT连接
```bash
node test-mqtt.js
# 查看连接状态和消息收发
```

#### 3. 测试API接口
```bash
npm test
# 验证所有API功能
```

#### 4. 手动发送MQTT命令
```bash
# 使用mosquitto客户端测试
mosquitto_pub -h s3.v100.vip -p 33880 -u mdfk -P 1HUJIFDAHSOUF \
  -t database/query -m '{"cmd":"get","id":1,"db":"gps_data"}'
```

## 生产部署

### 1. 云服务器部署
```bash
# 在云服务器上
git clone your-repo
cd miniprogram-3/gateway
npm install --production
npm start
```

### 2. 配置HTTPS
```bash
# 使用Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

### 3. 配置Nginx
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    location /api/ {
        proxy_pass http://localhost:3000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 4. 微信公众平台配置
1. 登录微信公众平台
2. 进入"开发" → "开发设置"
3. 在"服务器域名"中添加: `https://your-domain.com`

## 总结

通过本指南，你可以：

1. ✅ 成功连接到你的MQTT服务器 (s3.v100.vip:33880)
2. ✅ 实现小程序与本地数据库的数据交互
3. ✅ 支持车辆模式切换和实时数据获取
4. ✅ 提供完整的测试和调试工具
5. ✅ 支持开发和生产环境部署

这个解决方案既保持了你现有的MQTT基础设施，又解决了微信小程序的网络限制问题，是最适合你需求的集成方案。
