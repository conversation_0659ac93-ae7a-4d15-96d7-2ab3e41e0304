# 电动车小程序网关服务器

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动服务器
```bash
# 生产模式
npm start

# 开发模式（自动重启）
npm run dev
```

### 3. 验证服务
```bash
# 健康检查
curl http://localhost:3000/api/health

# 获取车辆数据
curl http://localhost:3000/api/vehicle/data

# 运行完整测试套件
npm test

# 测试MQTT连接
node test-mqtt.js
```

## API接口

### 基础信息
- **基础URL**: `http://localhost:3000/api`
- **端口**: 3000
- **协议**: HTTP (开发环境) / HTTPS (生产环境)

### 接口列表

#### 1. 健康检查
```
GET /api/health
```

**响应示例:**
```json
{
  "success": true,
  "message": "网关服务正常",
  "status": {
    "mqtt": true,
    "database": true,
    "uptime": 3600
  }
}
```

#### 2. 获取车辆数据
```
GET /api/vehicle/data
```

**响应示例:**
```json
{
  "success": true,
  "message": "获取车辆数据成功",
  "data": {
    "user": "admin",
    "gps": {
      "longitude": 116.397428,
      "latitude": 39.90923
    },
    "car": {
      "speed": 12.5,
      "power": 84,
      "mode": "adult",
      "connected": true
    },
    "_id": "1703123456789",
    "timestamp": "2023-12-21T10:30:00.000Z"
  },
  "dataSource": "mqtt"
}
```

#### 3. 设置车辆模式
```
POST /api/vehicle/mode
Content-Type: application/json
```

**请求体:**
```json
{
  "mode": "youth"
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "模式设置成功",
  "data": {
    "mode": "youth",
    "timestamp": "2023-12-21T10:30:00.000Z",
    "mqttSent": true
  }
}
```

#### 4. 获取车辆状态
```
GET /api/vehicle/status
```

**响应示例:**
```json
{
  "success": true,
  "message": "获取状态成功",
  "data": {
    "connected": true,
    "lastUpdate": "2023-12-21T10:30:00.000Z",
    "battery": 84,
    "mode": "adult",
    "location": {
      "longitude": 116.397428,
      "latitude": 39.90923
    },
    "dataSource": "mqtt"
  }
}
```

## 配置说明

### MQTT配置
```javascript
const MQTT_CONFIG = {
  host: 's3.v100.vip',   // MQTT服务器地址
  port: 33880,           // MQTT端口
  username: 'mdfk',      // 用户名
  password: '1HUJIFDAHSOUF', // 密码
  clientId: 'mdfk124xfasrf'  // 客户端ID
};
```

### 数据库结构
```json
{
  "user": [
    {
      "id": 1,
      "role": "admin",
      "username": "admin",
      "password": "admin123",
      "_id": 0
    }
  ],
  "gps_data": [
    {
      "id": 1,
      "longitude": 110.45,
      "latitude": 43.923,
      "_id": 0
    }
  ],
  "car_data": [
    {
      "id": 1,
      "speed": 25,
      "power": 87,
      "mod": 1,
      "_id": 0
    }
  ]
}
```

### MQTT命令格式

#### 数据库查询命令
```json
{
  "cmd": "get",
  "id": 1,
  "db": "gps_data"
}
```

#### 数据库更新命令
```json
{
  "cmd": "update",
  "id": 1,
  "db": "gps_data",
  "type": "longitude",
  "value": 52014141414
}
```

### 数据库配置
当前使用模拟数据库，你可以替换为实际的数据库连接：

```javascript
// 替换为实际数据库
const mysql = require('mysql2');
const database = mysql.createConnection({
  host: 'localhost',
  user: 'your_username',
  password: 'your_password',
  database: 'your_database'
});
```

## 部署指南

### 开发环境
1. 确保本地MQTT服务器运行在 `localhost:1883`
2. 启动网关服务器: `npm start`
3. 在小程序开发工具中启用"不校验合法域名"
4. 小程序API地址设置为: `http://localhost:3000/api`

### 生产环境
1. 部署到云服务器
2. 配置HTTPS证书
3. 在微信公众平台添加域名白名单
4. 更新小程序API地址为: `https://your-domain.com/api`

## 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
netstat -tulpn | grep :3000

# 杀死占用进程
kill -9 <PID>

# 或者修改端口
PORT=3001 npm start
```

#### 2. MQTT连接失败
```bash
# 检查MQTT服务器状态
mosquitto_pub -h localhost -p 1883 -t test -m "hello"

# 检查网关日志
npm start
```

#### 3. 跨域问题
确保CORS配置正确：
```javascript
app.use(cors({
  origin: '*', // 开发环境
  // origin: 'https://your-domain.com', // 生产环境
  methods: ['GET', 'POST', 'PUT', 'DELETE']
}));
```

### 日志查看
```bash
# 启动时查看详细日志
npm start 2>&1 | tee gateway.log

# 实时查看日志
tail -f gateway.log
```

## 安全建议

### 1. 生产环境安全
- 使用HTTPS协议
- 添加API认证
- 限制访问频率
- 配置防火墙

### 2. 认证示例
```javascript
// 添加简单的API密钥认证
app.use('/api', (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  if (apiKey !== process.env.API_KEY) {
    return res.status(401).json({ message: '无效的API密钥' });
  }
  next();
});
```

## 扩展功能

### 1. 添加数据库支持
```javascript
// 安装数据库驱动
npm install mysql2

// 修改database对象
const mysql = require('mysql2/promise');
const database = {
  connection: null,
  
  async connect() {
    this.connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'password',
      database: 'ev_data'
    });
  },
  
  async saveVehicleData(data) {
    const sql = 'INSERT INTO vehicle_data (data, timestamp) VALUES (?, ?)';
    await this.connection.execute(sql, [JSON.stringify(data), new Date()]);
  }
};
```

### 2. 添加WebSocket支持
```javascript
const WebSocket = require('ws');
const wss = new WebSocket.Server({ port: 8080 });

wss.on('connection', (ws) => {
  console.log('WebSocket客户端连接');
  
  // 发送实时数据
  const interval = setInterval(() => {
    if (latestVehicleData) {
      ws.send(JSON.stringify(latestVehicleData));
    }
  }, 1000);
  
  ws.on('close', () => {
    clearInterval(interval);
  });
});
```

## 联系支持

如果遇到问题，请：
1. 查看日志文件
2. 检查网络连接
3. 验证MQTT服务器状态
4. 联系技术支持
