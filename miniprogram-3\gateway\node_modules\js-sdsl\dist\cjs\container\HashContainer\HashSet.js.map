{"version": 3, "sources": ["container/HashContainer/HashSet.js", "../../src/container/HashContainer/HashSet.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_Base", "require", "_throwError", "HashSetIterator", "HashContainerIterator", "constructor", "node", "header", "container", "iteratorType", "super", "this", "pointer", "_node", "_header", "throwIteratorAccessError", "_key", "copy", "HashSet", "HashC<PERSON>r", "self", "for<PERSON>ach", "el", "insert", "begin", "_head", "end", "rBegin", "_tail", "rEnd", "front", "back", "key", "isObject", "_set", "undefined", "getElementByPos", "pos", "_length", "RangeError", "_next", "find", "_findElementNode", "callback", "index", "Symbol", "iterator", "bind", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACJvB,IAAAC,QAAAC,QAAA;;AAEA,IAAAC,cAAAD,QAAA;;AAEA,MAAME,wBAA2BC,MAAAA;IAE/BC,YACEC,GACAC,GACAC,GACAC;QAEAC,MAAMJ,GAAMC,GAAQE;QACpBE,KAAKH,YAAYA;ADPjB;ICSEI;QACF,IAAID,KAAKE,MAAUF,KAAKG,GAAS;aAC/B,GAAAC,YAAAA;ADPE;QCSJ,OAAOJ,KAAKE,EAAMG;ADPlB;ICSFC;QACE,OAAO,IAAId,gBAAmBQ,KAAKE,GAAOF,KAAKG,GAASH,KAAKH,WAAWG,KAAKF;ADP7E;;;ACeJ,MAAMS,gBAAmBC,MAAAA;IACvBd,YAAYG,IAA8B;QACxCE;QACA,MAAMU,IAAOT;QACbH,EAAUa,SAAQ,SAAUC;YAC1BF,EAAKG,OAAOD;ADZV;AACJ;ICcFE;QACE,OAAO,IAAIrB,gBAAmBQ,KAAKc,GAAOd,KAAKG,GAASH;ADZxD;ICcFe;QACE,OAAO,IAAIvB,gBAAmBQ,KAAKG,GAASH,KAAKG,GAASH;ADZ1D;ICcFgB;QACE,OAAO,IAAIxB,gBAAmBQ,KAAKiB,GAAOjB,KAAKG,GAASH,MAAI;ADZ5D;ICcFkB;QACE,OAAO,IAAI1B,gBAAmBQ,KAAKG,GAASH,KAAKG,GAASH,MAAI;ADZ9D;ICcFmB;QACE,OAAOnB,KAAKc,EAAMT;ADZlB;ICcFe;QACE,OAAOpB,KAAKiB,EAAMZ;ADZlB;ICqBFO,OAAOS,GAAQC;QACb,OAAOtB,KAAKuB,EAAKF,GAAKG,WAAWF;ADZjC;ICcFG,gBAAgBC;QDZV,ICasBA,IAAG,KAAHA,IAAQ1B,KAAK2B,IAAO,GA/DI;YAAE,MAAU,IAChEC;ADmDM;QCYJ,IAAIjC,IAAOK,KAAKc;QAChB,OAAOY,KAAO;YACZ/B,IAAOA,EAAKkC;ADVV;QCYJ,OAAOlC,EAAKU;ADVZ;ICmBFyB,KAAKT,GAAQC;QACX,MAAM3B,IAAOK,KAAK+B,EAAiBV,GAAKC;QACxC,OAAO,IAAI9B,gBAAmBG,GAAMK,KAAKG,GAASH;ADVlD;ICYFU,QAAQsB;QACN,IAAIC,IAAQ;QACZ,IAAItC,IAAOK,KAAKc;QAChB,OAAOnB,MAASK,KAAKG,GAAS;YAC5B6B,EAASrC,EAAKU,GAAM4B,KAASjC;YAC7BL,IAAOA,EAAKkC;ADVV;AACJ;ICYF,CAACK,OAAOC;QACN,OAAO;YACL,IAAIxC,IAAOK,KAAKc;YAChB,OAAOnB,MAASK,KAAKG,GAAS;sBACtBR,EAAKU;gBACXV,IAAOA,EAAKkC;ADVR;AACJ,UCWFO,KAAKpC,KANA;ADJP;;;ACYH,IAAAqC,WAEc9B;;AAAOrB,QAAAE,UAAAiD", "file": "HashSet.js", "sourcesContent": ["import { HashContainer, HashContainerIterator } from \"./Base\";\nimport $checkWithinAccessParams from \"../../utils/checkParams.macro\";\nimport { throwIteratorAccessError } from \"../../utils/throwError\";\nclass HashSetIterator extends HashContainerIterator {\n    constructor(node, header, container, iteratorType) {\n        super(node, header, iteratorType);\n        this.container = container;\n    }\n    get pointer() {\n        if (this._node === this._header) {\n            throwIteratorAccessError();\n        }\n        return this._node._key;\n    }\n    copy() {\n        return new HashSetIterator(this._node, this._header, this.container, this.iteratorType);\n    }\n}\nclass HashSet extends HashContainer {\n    constructor(container = []) {\n        super();\n        const self = this;\n        container.forEach(function (el) {\n            self.insert(el);\n        });\n    }\n    begin() {\n        return new HashSetIterator(this._head, this._header, this);\n    }\n    end() {\n        return new HashSetIterator(this._header, this._header, this);\n    }\n    rBegin() {\n        return new HashSetIterator(this._tail, this._header, this, 1 /* IteratorType.REVERSE */);\n    }\n    rEnd() {\n        return new HashSetIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    }\n    front() {\n        return this._head._key;\n    }\n    back() {\n        return this._tail._key;\n    }\n    /**\n     * @description Insert element to set.\n     * @param key - The key want to insert.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @returns The size of container after inserting.\n     */\n    insert(key, isObject) {\n        return this._set(key, undefined, isObject);\n    }\n    getElementByPos(pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        let node = this._head;\n        while (pos--) {\n            node = node._next;\n        }\n        return node._key;\n    }\n    /**\n     * @description Check key if exist in container.\n     * @param key - The element you want to search.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @returns An iterator pointing to the element if found, or super end if not found.\n     */\n    find(key, isObject) {\n        const node = this._findElementNode(key, isObject);\n        return new HashSetIterator(node, this._header, this);\n    }\n    forEach(callback) {\n        let index = 0;\n        let node = this._head;\n        while (node !== this._header) {\n            callback(node._key, index++, this);\n            node = node._next;\n        }\n    }\n    [Symbol.iterator]() {\n        return function* () {\n            let node = this._head;\n            while (node !== this._header) {\n                yield node._key;\n                node = node._next;\n            }\n        }.bind(this)();\n    }\n}\nexport default HashSet;\n", "import { initContainer, IteratorType } from '@/container/ContainerBase';\nimport { Hash<PERSON>ontainer, HashContainerIterator, HashLinkNode } from '@/container/HashContainer/Base';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nclass HashSetIterator<K> extends HashContainerIterator<K, undefined> {\n  readonly container: HashSet<K>;\n  constructor(\n    node: HashLinkNode<K, undefined>,\n    header: HashLinkNode<K, undefined>,\n    container: HashSet<K>,\n    iteratorType?: IteratorType\n  ) {\n    super(node, header, iteratorType);\n    this.container = container;\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    return this._node._key;\n  }\n  copy() {\n    return new HashSetIterator<K>(this._node, this._header, this.container, this.iteratorType);\n  }\n  // @ts-ignore\n  equals(iter: HashSetIterator<K>): boolean;\n}\n\nexport type { HashSetIterator };\n\nclass HashSet<K> extends HashContainer<K, undefined> {\n  constructor(container: initContainer<K> = []) {\n    super();\n    const self = this;\n    container.forEach(function (el) {\n      self.insert(el);\n    });\n  }\n  begin() {\n    return new HashSetIterator<K>(this._head, this._header, this);\n  }\n  end() {\n    return new HashSetIterator<K>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new HashSetIterator<K>(this._tail, this._header, this, IteratorType.REVERSE);\n  }\n  rEnd() {\n    return new HashSetIterator<K>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front(): K | undefined {\n    return this._head._key;\n  }\n  back(): K | undefined {\n    return this._tail._key;\n  }\n  /**\n   * @description Insert element to set.\n   * @param key - The key want to insert.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @returns The size of container after inserting.\n   */\n  insert(key: K, isObject?: boolean) {\n    return this._set(key, undefined, isObject);\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let node = this._head;\n    while (pos--) {\n      node = node._next;\n    }\n    return node._key;\n  }\n  /**\n   * @description Check key if exist in container.\n   * @param key - The element you want to search.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @returns An iterator pointing to the element if found, or super end if not found.\n   */\n  find(key: K, isObject?: boolean) {\n    const node = this._findElementNode(key, isObject);\n    return new HashSetIterator<K>(node, this._header, this);\n  }\n  forEach(callback: (element: K, index: number, container: HashSet<K>) => void) {\n    let index = 0;\n    let node = this._head;\n    while (node !== this._header) {\n      callback(node._key, index++, this);\n      node = node._next;\n    }\n  }\n  [Symbol.iterator]() {\n    return function * (this: HashSet<K>) {\n      let node = this._head;\n      while (node !== this._header) {\n        yield node._key;\n        node = node._next;\n      }\n    }.bind(this)();\n  }\n}\n\nexport default HashSet;\n"]}