{"version": 3, "sources": ["container/OtherContainer/Queue.js", "../../src/container/OtherContainer/Queue.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_ContainerBase", "require", "Queue", "Base", "constructor", "container", "super", "this", "_first", "_queue", "self", "for<PERSON>ach", "el", "push", "clear", "_length", "element", "capacity", "length", "i", "pop", "front", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACLvB,IAAAC,iBAAAC,QAAA;;AAOA,MAAMC,cAAiBC,eAAAA;IASrBC,YAAYC,IAA8B;QACxCC;QANMC,KAAAC,IAAS;QAITD,KAAAE,IAAc;QAGpB,MAAMC,IAAOH;QACbF,EAAUM,SAAQ,SAAUC;YAC1BF,EAAKG,KAAKD;ADLR;AACJ;ICOFE;QACEP,KAAKE,IAAS;QACdF,KAAKQ,IAAUR,KAAKC,IAAS;ADL7B;ICYFK,KAAKG;QACH,MAAMC,IAAWV,KAAKE,EAAOS;QAC7B,IACGX,KAAKC,IAASS,IAAS,MACvBV,KAAKC,IAASD,KAAKQ,KAAYE,KAChCA,IAAQ,MACR;YACA,MAAMC,IAASX,KAAKQ;YACpB,KAAK,IAAII,IAAI,GAAGA,IAAID,KAAUC,GAAG;gBAC/BZ,KAAKE,EAAOU,KAAKZ,KAAKE,EAAOF,KAAKC,IAASW;ADPvC;YCSNZ,KAAKC,IAAS;YACdD,KAAKE,EAAOF,KAAKQ,KAAWC;ADP1B,eCQGT,KAAKE,EAAOF,KAAKC,IAASD,KAAKQ,KAAWC;QACjD,SAAST,KAAKQ;ADLd;ICWFK;QACE,IAAIb,KAAKQ,MAAY,GAAG;QACxB,MAAMH,IAAKL,KAAKE,EAAOF,KAAKC;QAC5BD,KAAKQ,KAAW;QAChB,OAAOH;ADJP;ICUFS;QACE,IAAId,KAAKQ,MAAY,GAAG;QACxB,OAAOR,KAAKE,EAAOF,KAAKC;ADHxB;;;ACKH,IAAAc,WAEcpB;;AAAKL,QAAAE,UAAAuB", "file": "Queue.js", "sourcesContent": ["import { Base } from \"../ContainerBase\";\nclass Queue extends Base {\n    constructor(container = []) {\n        super();\n        /**\n         * @internal\n         */\n        this._first = 0;\n        /**\n         * @internal\n         */\n        this._queue = [];\n        const self = this;\n        container.forEach(function (el) {\n            self.push(el);\n        });\n    }\n    clear() {\n        this._queue = [];\n        this._length = this._first = 0;\n    }\n    /**\n     * @description Inserts element to queue's end.\n     * @param element - The element you want to push to the front.\n     * @returns The container length after pushing.\n     */\n    push(element) {\n        const capacity = this._queue.length;\n        if ((this._first / capacity) > 0.5 /* QUEUE_CONSTANT.ALLOCATE_SIGMA */ &&\n            (this._first + this._length) >= capacity &&\n            capacity > 4096 /* QUEUE_CONSTANT.MIN_ALLOCATE_SIZE */) {\n            const length = this._length;\n            for (let i = 0; i < length; ++i) {\n                this._queue[i] = this._queue[this._first + i];\n            }\n            this._first = 0;\n            this._queue[this._length] = element;\n        }\n        else\n            this._queue[this._first + this._length] = element;\n        return ++this._length;\n    }\n    /**\n     * @description Removes the first element.\n     * @returns The element you popped.\n     */\n    pop() {\n        if (this._length === 0)\n            return;\n        const el = this._queue[this._first++];\n        this._length -= 1;\n        return el;\n    }\n    /**\n     * @description Access the first element.\n     * @returns The first element.\n     */\n    front() {\n        if (this._length === 0)\n            return;\n        return this._queue[this._first];\n    }\n}\nexport default Queue;\n", "import { Base, initContainer } from '@/container/ContainerBase';\n\nconst enum QUEUE_CONSTANT {\n  ALLOCATE_SIGMA = 0.5,\n  MIN_ALLOCATE_SIZE = (1 << 12)\n}\n\nclass Queue<T> extends Base {\n  /**\n   * @internal\n   */\n  private _first = 0;\n  /**\n   * @internal\n   */\n  private _queue: T[] = [];\n  constructor(container: initContainer<T> = []) {\n    super();\n    const self = this;\n    container.forEach(function (el) {\n      self.push(el);\n    });\n  }\n  clear() {\n    this._queue = [];\n    this._length = this._first = 0;\n  }\n  /**\n   * @description Inserts element to queue's end.\n   * @param element - The element you want to push to the front.\n   * @returns The container length after pushing.\n   */\n  push(element: T) {\n    const capacity = this._queue.length;\n    if (\n      (this._first / capacity) > QUEUE_CONSTANT.ALLOCATE_SIGMA &&\n      (this._first + this._length) >= capacity &&\n      capacity > QUEUE_CONSTANT.MIN_ALLOCATE_SIZE\n    ) {\n      const length = this._length;\n      for (let i = 0; i < length; ++i) {\n        this._queue[i] = this._queue[this._first + i];\n      }\n      this._first = 0;\n      this._queue[this._length] = element;\n    } else this._queue[this._first + this._length] = element;\n    return ++this._length;\n  }\n  /**\n   * @description Removes the first element.\n   * @returns The element you popped.\n   */\n  pop() {\n    if (this._length === 0) return;\n    const el = this._queue[this._first++];\n    this._length -= 1;\n    return el;\n  }\n  /**\n   * @description Access the first element.\n   * @returns The first element.\n   */\n  front(): T | undefined {\n    if (this._length === 0) return;\n    return this._queue[this._first];\n  }\n}\n\nexport default Queue;\n"]}