{"version": 3, "sources": ["container/SequentialContainer/Base/RandomIterator.js", "../../src/container/SequentialContainer/Base/RandomIterator.ts"], "names": ["Object", "defineProperty", "exports", "value", "RandomIterator", "_ContainerBase", "require", "_throwError", "ContainerIterator", "constructor", "index", "iteratorType", "super", "this", "_node", "pre", "throwIteratorAccessError", "next", "container", "size", "pointer", "getElementByPos", "newValue", "setElementByPos"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,sBAAsB;;ACL9B,IAAAC,iBAAAC,QAAA;;AAEA,IAAAC,cAAAD,QAAA;;AAEM,MAAgBF,uBAA0BI,eAAAA;IAS9CC,YACEC,GACAC;QAEAC,MAAMD;QACNE,KAAKC,IAAQJ;QACb,IAAIG,KAAKF,iBAAY,GAA0B;YAC7CE,KAAKE,MAAM;gBACT,IAAIF,KAAKC,MAAU,GAAG;qBACpB,GAAAE,YAAAA;ADTM;gBCWRH,KAAKC,KAAS;gBACd,OAAOD;ADTH;YCWNA,KAAKI,OAAO;gBACV,IAAIJ,KAAKC,MAAUD,KAAKK,UAAUC,QAAQ;qBACxC,GAAAH,YAAAA;ADTM;gBCWRH,KAAKC,KAAS;gBACd,OAAOD;ADTH;AACJ,eCUG;YACLA,KAAKE,MAAM;gBACT,IAAIF,KAAKC,MAAUD,KAAKK,UAAUC,SAAS,GAAG;qBAC5C,GAAAH,YAAAA;ADRM;gBCURH,KAAKC,KAAS;gBACd,OAAOD;ADRH;YCUNA,KAAKI,OAAO;gBACV,IAAIJ,KAAKC,OAAW,GAAG;qBACrB,GAAAE,YAAAA;ADRM;gBCURH,KAAKC,KAAS;gBACd,OAAOD;ADRH;AACJ;AACJ;ICUEO;QACF,OAAOP,KAAKK,UAAUG,gBAAgBR,KAAKC;ADR3C;ICUEM,YAAQE;QACVT,KAAKK,UAAUK,gBAAgBV,KAAKC,GAAOQ;ADR3C;;;ACcHpB,QAAAE,iBAAAA", "file": "RandomIterator.js", "sourcesContent": ["import { ContainerIterator } from \"../../ContainerBase\";\nimport { throwIteratorAccessError } from \"../../../utils/throwError\";\nexport class RandomIterator extends ContainerIterator {\n    /**\n     * @internal\n     */\n    constructor(index, iteratorType) {\n        super(iteratorType);\n        this._node = index;\n        if (this.iteratorType === 0 /* IteratorType.NORMAL */) {\n            this.pre = function () {\n                if (this._node === 0) {\n                    throwIteratorAccessError();\n                }\n                this._node -= 1;\n                return this;\n            };\n            this.next = function () {\n                if (this._node === this.container.size()) {\n                    throwIteratorAccessError();\n                }\n                this._node += 1;\n                return this;\n            };\n        }\n        else {\n            this.pre = function () {\n                if (this._node === this.container.size() - 1) {\n                    throwIteratorAccessError();\n                }\n                this._node += 1;\n                return this;\n            };\n            this.next = function () {\n                if (this._node === -1) {\n                    throwIteratorAccessError();\n                }\n                this._node -= 1;\n                return this;\n            };\n        }\n    }\n    get pointer() {\n        return this.container.getElementByPos(this._node);\n    }\n    set pointer(newValue) {\n        this.container.setElementByPos(this._node, newValue);\n    }\n}\n", "import { ContainerIterator, IteratorType } from '@/container/ContainerBase';\nimport SequentialContainer from '@/container/SequentialContainer/Base/index';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nexport abstract class RandomIterator<T> extends ContainerIterator<T> {\n  abstract readonly container: SequentialContainer<T>;\n  /**\n   * @internal\n   */\n  _node: number;\n  /**\n   * @internal\n   */\n  protected constructor(\n    index: number,\n    iteratorType?: IteratorType\n  ) {\n    super(iteratorType);\n    this._node = index;\n    if (this.iteratorType === IteratorType.NORMAL) {\n      this.pre = function () {\n        if (this._node === 0) {\n          throwIteratorAccessError();\n        }\n        this._node -= 1;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this.container.size()) {\n          throwIteratorAccessError();\n        }\n        this._node += 1;\n        return this;\n      };\n    } else {\n      this.pre = function () {\n        if (this._node === this.container.size() - 1) {\n          throwIteratorAccessError();\n        }\n        this._node += 1;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === -1) {\n          throwIteratorAccessError();\n        }\n        this._node -= 1;\n        return this;\n      };\n    }\n  }\n  get pointer() {\n    return this.container.getElementByPos(this._node);\n  }\n  set pointer(newValue: T) {\n    this.container.setElementByPos(this._node, newValue);\n  }\n  // @ts-ignore\n  pre(): this;\n  // @ts-ignore\n  next(): this;\n}\n"]}