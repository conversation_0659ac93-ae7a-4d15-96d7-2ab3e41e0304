{"version": 3, "sources": ["container/SequentialContainer/Base/index.js", "../../src/container/SequentialContainer/Base/index.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_ContainerBase", "require", "SequentialContainer", "Container", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACLvB,IAAAC,iBAAAC,QAAA;;AAEA,MAAeC,4BAA+BC,eAAAA;;AAgE7C,IAAAC,WAEcF;;AAAmBL,QAAAE,UAAAK", "file": "index.js", "sourcesContent": [null, "import { Container } from '@/container/ContainerBase';\n\nabstract class SequentialContainer<T> extends Container<T> {\n  /**\n   * @description Push the element to the back.\n   * @param element - The element you want to push.\n   * @returns The size of container after pushing.\n   */\n  abstract pushBack(element: T): number;\n  /**\n   * @description Removes the last element.\n   * @returns The element you popped.\n   */\n  abstract popBack(): T | undefined;\n  /**\n   * @description Sets element by position.\n   * @param pos - The position you want to change.\n   * @param element - The element's value you want to update.\n   * @example\n   * container.setElementByPos(-1, 1); // throw a RangeError\n   */\n  abstract setElementByPos(pos: number, element: T): void;\n  /**\n   * @description Removes the elements of the specified value.\n   * @param value - The value you want to remove.\n   * @returns The size of container after erasing.\n   * @example\n   * container.eraseElementByValue(-1);\n   */\n  abstract eraseElementByValue(value: T): number;\n  /**\n   * @description Insert several elements after the specified position.\n   * @param pos - The position you want to insert.\n   * @param element - The element you want to insert.\n   * @param num - The number of elements you want to insert (default 1).\n   * @returns The size of container after inserting.\n   * @example\n   * const container = new Vector([1, 2, 3]);\n   * container.insert(1, 4);  // [1, 4, 2, 3]\n   * container.insert(1, 5, 3); // [1, 5, 5, 5, 4, 2, 3]\n   */\n  abstract insert(pos: number, element: T, num?: number): number;\n  /**\n   * @description Reverses the container.\n   * @example\n   * const container = new Vector([1, 2, 3]);\n   * container.reverse(); // [3, 2, 1]\n   */\n  abstract reverse(): void;\n  /**\n   * @description Removes the duplication of elements in the container.\n   * @returns The size of container after inserting.\n   * @example\n   * const container = new Vector([1, 1, 3, 2, 2, 5, 5, 2]);\n   * container.unique(); // [1, 3, 2, 5, 2]\n   */\n  abstract unique(): number;\n  /**\n   * @description Sort the container.\n   * @param cmp - Comparison function to sort.\n   * @example\n   * const container = new Vector([3, 1, 10]);\n   * container.sort();  // [1, 10, 3]\n   * container.sort((x, y) => x - y); // [1, 3, 10]\n   */\n  abstract sort(cmp?: (x: T, y: T) => number): void;\n}\n\nexport default SequentialContainer;\n"]}