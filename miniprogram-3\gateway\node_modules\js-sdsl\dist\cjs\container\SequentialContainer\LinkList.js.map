{"version": 3, "sources": ["container/SequentialContainer/LinkList.js", "../../src/container/SequentialContainer/LinkList.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_Base", "_interopRequireDefault", "require", "_ContainerBase", "_throwError", "obj", "__esModule", "LinkListIterator", "ContainerIterator", "constructor", "_node", "_header", "container", "iteratorType", "super", "this", "pre", "_pre", "throwIteratorAccessError", "next", "_next", "pointer", "_value", "newValue", "copy", "LinkList", "SequentialContainer", "_head", "_tail", "self", "for<PERSON>ach", "el", "pushBack", "_eraseNode", "node", "L", "B", "_length", "_insertNode", "clear", "begin", "end", "rBegin", "rEnd", "front", "back", "getElementByPos", "pos", "RangeError", "curNode", "eraseElementByPos", "eraseElementByValue", "eraseElementByIterator", "iter", "element", "popBack", "pushFront", "popFront", "setElementByPos", "insert", "num", "i", "find", "reverse", "pHead", "pTail", "cnt", "tmp", "unique", "tmpNode", "sort", "cmp", "arr", "push", "merge", "list", "callback", "index", "Symbol", "iterator", "bind", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACLvB,IAAAC,QAAAC,uBAAAC,QAAA;;AACA,IAAAC,iBAAAD,QAAA;;AAEA,IAAAE,cAAAF,QAAA;;AAA8D,SAAAD,uBAAAI;IAAA,OAAAA,KAAAA,EAAAC,IAAAD,IAAA;QAAAN,SAAAM;;AAAA;;AAQ9D,MAAME,yBAA4BC,eAAAA;IAahCC,YACEC,GACAC,GACAC,GACAC;QAEAC,MAAMD;QACNE,KAAKL,IAAQA;QACbK,KAAKJ,IAAUA;QACfI,KAAKH,YAAYA;QACjB,IAAIG,KAAKF,iBAAY,GAA0B;YAC7CE,KAAKC,MAAM;gBACT,IAAID,KAAKL,EAAMO,MAASF,KAAKJ,GAAS;qBACpC,GAAAO,YAAAA;ADpBM;gBCsBRH,KAAKL,IAAQK,KAAKL,EAAMO;gBACxB,OAAOF;ADpBH;YCsBNA,KAAKI,OAAO;gBACV,IAAIJ,KAAKL,MAAUK,KAAKJ,GAAS;qBAC/B,GAAAO,YAAAA;ADpBM;gBCsBRH,KAAKL,IAAQK,KAAKL,EAAMU;gBACxB,OAAOL;ADpBH;AACJ,eCqBG;YACLA,KAAKC,MAAM;gBACT,IAAID,KAAKL,EAAMU,MAAUL,KAAKJ,GAAS;qBACrC,GAAAO,YAAAA;ADnBM;gBCqBRH,KAAKL,IAAQK,KAAKL,EAAMU;gBACxB,OAAOL;ADnBH;YCqBNA,KAAKI,OAAO;gBACV,IAAIJ,KAAKL,MAAUK,KAAKJ,GAAS;qBAC/B,GAAAO,YAAAA;ADnBM;gBCqBRH,KAAKL,IAAQK,KAAKL,EAAMO;gBACxB,OAAOF;ADnBH;AACJ;AACJ;ICqBEM;QACF,IAAIN,KAAKL,MAAUK,KAAKJ,GAAS;aAC/B,GAAAO,YAAAA;ADnBE;QCqBJ,OAAOH,KAAKL,EAAMY;ADnBlB;ICqBED,YAAQE;QACV,IAAIR,KAAKL,MAAUK,KAAKJ,GAAS;aAC/B,GAAAO,YAAAA;ADnBE;QCqBJH,KAAKL,EAAMY,IAASC;ADnBpB;ICqBFC;QACE,OAAO,IAAIjB,iBAAoBQ,KAAKL,GAAOK,KAAKJ,GAASI,KAAKH,WAAWG,KAAKF;ADnB9E;;;AC+BJ,MAAMY,iBAAoBC,MAAAA;IAaxBjB,YAAYG,IAA8B;QACxCE;QACAC,KAAKJ,IAAuB,CAAA;QAC5BI,KAAKY,IAAQZ,KAAKa,IAAQb,KAAKJ,EAAQM,IAAOF,KAAKJ,EAAQS,IAAQL,KAAKJ;QACxE,MAAMkB,IAAOd;QACbH,EAAUkB,SAAQ,SAAUC;YAC1BF,EAAKG,SAASD;ADxCZ;AACJ;IC6CME,EAAWC;QACjB,OAAMC,GAAElB,GAAImB,GAAEhB,KAAUc;QACxBjB,EAAKG,IAAQA;QACbA,EAAMH,IAAOA;QACb,IAAIiB,MAASnB,KAAKY,GAAO;YACvBZ,KAAKY,IAAQP;ADxCX;QC0CJ,IAAIc,MAASnB,KAAKa,GAAO;YACvBb,KAAKa,IAAQX;ADxCX;QC0CJF,KAAKsB,KAAW;ADxChB;IC6CMC,EAAYxC,GAAUkB;QAC5B,MAAMG,IAAOH,EAAII;QACjB,MAAMc,IAAO;YACXZ,GAAQxB;YACRmB,GAAMD;YACNI,GAAOD;;QAETH,EAAII,IAAQc;QACZf,EAAKF,IAAOiB;QACZ,IAAIlB,MAAQD,KAAKJ,GAAS;YACxBI,KAAKY,IAAQO;ADxCX;QC0CJ,IAAIf,MAASJ,KAAKJ,GAAS;YACzBI,KAAKa,IAAQM;ADxCX;QC0CJnB,KAAKsB,KAAW;ADxChB;IC0CFE;QACExB,KAAKsB,IAAU;QACftB,KAAKY,IAAQZ,KAAKa,IAAQb,KAAKJ,EAAQM,IAAOF,KAAKJ,EAAQS,IAAQL,KAAKJ;ADxCxE;IC0CF6B;QACE,OAAO,IAAIjC,iBAAoBQ,KAAKY,GAAOZ,KAAKJ,GAASI;ADxCzD;IC0CF0B;QACE,OAAO,IAAIlC,iBAAoBQ,KAAKJ,GAASI,KAAKJ,GAASI;ADxC3D;IC0CF2B;QACE,OAAO,IAAInC,iBAAoBQ,KAAKa,GAAOb,KAAKJ,GAASI,MAAI;ADxC7D;IC0CF4B;QACE,OAAO,IAAIpC,iBAAoBQ,KAAKJ,GAASI,KAAKJ,GAASI,MAAI;ADxC/D;IC0CF6B;QACE,OAAO7B,KAAKY,EAAML;ADxClB;IC0CFuB;QACE,OAAO9B,KAAKa,EAAMN;ADxClB;IC0CFwB,gBAAgBC;QDxCV,ICyCsBA,IAAG,KAAHA,IAAQhC,KAAKsB,IAAO,GAhKpC;YAAE,MAAU,IAAIW;ADyHtB;QCwCJ,IAAIC,IAAUlC,KAAKY;QACnB,OAAOoB,KAAO;YACZE,IAAUA,EAAQ7B;ADtChB;QCwCJ,OAAO6B,EAAQ3B;ADtCf;ICwCF4B,kBAAkBH;QDtCZ,ICuCsBA,IAAG,KAAHA,IAAQhC,KAAKsB,IAAO,GAxKpC;YAAE,MAAU,IAAIW;ADmItB;QCsCJ,IAAIC,IAAUlC,KAAKY;QACnB,OAAOoB,KAAO;YACZE,IAAUA,EAAQ7B;ADpChB;QCsCJL,KAAKkB,EAAWgB;QAChB,OAAOlC,KAAKsB;ADpCZ;ICsCFc,oBAAoB7B;QAClB,IAAI2B,IAAUlC,KAAKY;QACnB,OAAOsB,MAAYlC,KAAKJ,GAAS;YAC/B,IAAIsC,EAAQ3B,MAAWA,GAAQ;gBAC7BP,KAAKkB,EAAWgB;ADpCZ;YCsCNA,IAAUA,EAAQ7B;ADpChB;QCsCJ,OAAOL,KAAKsB;ADpCZ;ICsCFe,uBAAuBC;QACrB,MAAMnB,IAAOmB,EAAK3C;QAClB,IAAIwB,MAASnB,KAAKJ,GAAS;aACzB,GAAAO,YAAAA;ADpCE;QCsCJmC,IAAOA,EAAKlC;QACZJ,KAAKkB,EAAWC;QAChB,OAAOmB;ADpCP;ICsCFrB,SAASsB;QACPvC,KAAKuB,EAAYgB,GAASvC,KAAKa;QAC/B,OAAOb,KAAKsB;ADpCZ;ICsCFkB;QACE,IAAIxC,KAAKsB,MAAY,GAAG;QACxB,MAAMvC,IAAQiB,KAAKa,EAAMN;QACzBP,KAAKkB,EAAWlB,KAAKa;QACrB,OAAO9B;ADnCP;IC0CF0D,UAAUF;QACRvC,KAAKuB,EAAYgB,GAASvC,KAAKJ;QAC/B,OAAOI,KAAKsB;ADnCZ;ICyCFoB;QACE,IAAI1C,KAAKsB,MAAY,GAAG;QACxB,MAAMvC,IAAQiB,KAAKY,EAAML;QACzBP,KAAKkB,EAAWlB,KAAKY;QACrB,OAAO7B;ADlCP;ICoCF4D,gBAAgBX,GAAaO;QDlCvB,ICmCsBP,IAAG,KAAHA,IAAQhC,KAAKsB,IAAO,GAjOpC;YAAE,MAAU,IAAIW;ADgMtB;QCkCJ,IAAIC,IAAUlC,KAAKY;QACnB,OAAOoB,KAAO;YACZE,IAAUA,EAAQ7B;ADhChB;QCkCJ6B,EAAQ3B,IAASgC;ADhCjB;ICkCFK,OAAOZ,GAAaO,GAAYM,IAAM;QDhChC,ICiCsBb,IAAG,KAAHA,IAAQhC,KAAKsB,GAzO7B;YAAE,MAAU,IAAIW;AD0MtB;QCgCJ,IAAIY,KAAO,GAAG,OAAO7C,KAAKsB;QAC1B,IAAIU,MAAQ,GAAG;YACb,OAAOa,KAAO7C,KAAKyC,UAAUF;AD5B3B,eC6BG,IAAIP,MAAQhC,KAAKsB,GAAS;YAC/B,OAAOuB,KAAO7C,KAAKiB,SAASsB;AD1B1B,eC2BG;YACL,IAAIL,IAAUlC,KAAKY;YACnB,KAAK,IAAIkC,IAAI,GAAGA,IAAId,KAAOc,GAAG;gBAC5BZ,IAAUA,EAAQ7B;ADzBd;YC2BN,MAAMD,IAAO8B,EAAQ7B;YACrBL,KAAKsB,KAAWuB;YAChB,OAAOA,KAAO;gBACZX,EAAQ7B,IAAqB;oBAC3BE,GAAQgC;oBACRrC,GAAMgC;;gBAERA,EAAQ7B,EAAMH,IAAOgC;gBACrBA,IAAUA,EAAQ7B;ADzBd;YC2BN6B,EAAQ7B,IAAQD;YAChBA,EAAKF,IAAOgC;ADzBV;QC2BJ,OAAOlC,KAAKsB;ADzBZ;IC2BFyB,KAAKR;QACH,IAAIL,IAAUlC,KAAKY;QACnB,OAAOsB,MAAYlC,KAAKJ,GAAS;YAC/B,IAAIsC,EAAQ3B,MAAWgC,GAAS;gBAC9B,OAAO,IAAI/C,iBAAoB0C,GAASlC,KAAKJ,GAASI;ADzBlD;YC2BNkC,IAAUA,EAAQ7B;ADzBhB;QC2BJ,OAAOL,KAAK0B;ADzBZ;IC2BFsB;QACE,IAAIhD,KAAKsB,KAAW,GAAG;QACvB,IAAI2B,IAAQjD,KAAKY;QACjB,IAAIsC,IAAQlD,KAAKa;QACjB,IAAIsC,IAAM;QACV,OAAQA,KAAO,IAAKnD,KAAKsB,GAAS;YAChC,MAAM8B,IAAMH,EAAM1C;YAClB0C,EAAM1C,IAAS2C,EAAM3C;YACrB2C,EAAM3C,IAAS6C;YACfH,IAAQA,EAAM5C;YACd6C,IAAQA,EAAMhD;YACdiD,KAAO;ADxBL;AACJ;IC0BFE;QACE,IAAIrD,KAAKsB,KAAW,GAAG;YACrB,OAAOtB,KAAKsB;ADxBV;QC0BJ,IAAIY,IAAUlC,KAAKY;QACnB,OAAOsB,MAAYlC,KAAKJ,GAAS;YAC/B,IAAI0D,IAAUpB;YACd,OACEoB,EAAQjD,MAAUL,KAAKJ,KACvB0D,EAAQ/C,MAAW+C,EAAQjD,EAAME,GACjC;gBACA+C,IAAUA,EAAQjD;gBAClBL,KAAKsB,KAAW;AD1BZ;YC4BNY,EAAQ7B,IAAQiD,EAAQjD;YACxB6B,EAAQ7B,EAAMH,IAAOgC;YACrBA,IAAUA,EAAQ7B;AD1BhB;QC4BJ,OAAOL,KAAKsB;AD1BZ;IC4BFiC,KAAKC;QACH,IAAIxD,KAAKsB,KAAW,GAAG;QACvB,MAAMmC,IAAW;QACjBzD,KAAKe,SAAQ,SAAUC;YACrByC,EAAIC,KAAK1C;ADzBP;QC2BJyC,EAAIF,KAAKC;QACT,IAAItB,IAAuBlC,KAAKY;QAChC6C,EAAI1C,SAAQ,SAAUwB;YACpBL,EAAQ3B,IAASgC;YACjBL,IAAUA,EAAQ7B;ADzBhB;AACJ;ICoCFsD,MAAMC;QACJ,MAAM9C,IAAOd;QACb,IAAIA,KAAKsB,MAAY,GAAG;YACtBsC,EAAK7C,SAAQ,SAAUC;gBACrBF,EAAKG,SAASD;ADzBV;AACJ,eC0BG;YACL,IAAIkB,IAAUlC,KAAKY;YACnBgD,EAAK7C,SAAQ,SAAUC;gBACrB,OACEkB,MAAYpB,EAAKlB,KACjBsC,EAAQ3B,KAAUS,GAClB;oBACAkB,IAAUA,EAAQ7B;AD1BZ;gBC4BRS,EAAKS,EAAYP,GAAIkB,EAAQhC;AD1BzB;AACJ;QC4BJ,OAAOF,KAAKsB;AD1BZ;IC4BFP,QAAQ8C;QACN,IAAI3B,IAAUlC,KAAKY;QACnB,IAAIkD,IAAQ;QACZ,OAAO5B,MAAYlC,KAAKJ,GAAS;YAC/BiE,EAAS3B,EAAQ3B,GAAQuD,KAAS9D;YAClCkC,IAAUA,EAAQ7B;AD1BhB;AACJ;IC4BF,CAAC0D,OAAOC;QACN,OAAO;YACL,IAAIhE,KAAKsB,MAAY,GAAG;YACxB,IAAIY,IAAUlC,KAAKY;YACnB,OAAOsB,MAAYlC,KAAKJ,GAAS;sBACzBsC,EAAQ3B;gBACd2B,IAAUA,EAAQ7B;ADzBd;AACJ,UC0BF4D,KAAKjE,KAPA;ADlBP;;;AC2BH,IAAAkE,WAEcxD;;AAAQ5B,QAAAE,UAAAkF", "file": "LinkList.js", "sourcesContent": ["import SequentialContainer from './Base';\nimport { ContainerIterator } from \"../ContainerBase\";\nimport $checkWithinAccessParams from \"../../utils/checkParams.macro\";\nimport { throwIteratorAccessError } from \"../../utils/throwError\";\nclass LinkListIterator extends ContainerIterator {\n    /**\n     * @internal\n     */\n    constructor(_node, _header, container, iteratorType) {\n        super(iteratorType);\n        this._node = _node;\n        this._header = _header;\n        this.container = container;\n        if (this.iteratorType === 0 /* IteratorType.NORMAL */) {\n            this.pre = function () {\n                if (this._node._pre === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre;\n                return this;\n            };\n            this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next;\n                return this;\n            };\n        }\n        else {\n            this.pre = function () {\n                if (this._node._next === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next;\n                return this;\n            };\n            this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre;\n                return this;\n            };\n        }\n    }\n    get pointer() {\n        if (this._node === this._header) {\n            throwIteratorAccessError();\n        }\n        return this._node._value;\n    }\n    set pointer(newValue) {\n        if (this._node === this._header) {\n            throwIteratorAccessError();\n        }\n        this._node._value = newValue;\n    }\n    copy() {\n        return new LinkListIterator(this._node, this._header, this.container, this.iteratorType);\n    }\n}\nclass LinkList extends SequentialContainer {\n    constructor(container = []) {\n        super();\n        this._header = {};\n        this._head = this._tail = this._header._pre = this._header._next = this._header;\n        const self = this;\n        container.forEach(function (el) {\n            self.pushBack(el);\n        });\n    }\n    /**\n     * @internal\n     */\n    _eraseNode(node) {\n        const { _pre, _next } = node;\n        _pre._next = _next;\n        _next._pre = _pre;\n        if (node === this._head) {\n            this._head = _next;\n        }\n        if (node === this._tail) {\n            this._tail = _pre;\n        }\n        this._length -= 1;\n    }\n    /**\n     * @internal\n     */\n    _insertNode(value, pre) {\n        const next = pre._next;\n        const node = {\n            _value: value,\n            _pre: pre,\n            _next: next\n        };\n        pre._next = node;\n        next._pre = node;\n        if (pre === this._header) {\n            this._head = node;\n        }\n        if (next === this._header) {\n            this._tail = node;\n        }\n        this._length += 1;\n    }\n    clear() {\n        this._length = 0;\n        this._head = this._tail = this._header._pre = this._header._next = this._header;\n    }\n    begin() {\n        return new LinkListIterator(this._head, this._header, this);\n    }\n    end() {\n        return new LinkListIterator(this._header, this._header, this);\n    }\n    rBegin() {\n        return new LinkListIterator(this._tail, this._header, this, 1 /* IteratorType.REVERSE */);\n    }\n    rEnd() {\n        return new LinkListIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    }\n    front() {\n        return this._head._value;\n    }\n    back() {\n        return this._tail._value;\n    }\n    getElementByPos(pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        let curNode = this._head;\n        while (pos--) {\n            curNode = curNode._next;\n        }\n        return curNode._value;\n    }\n    eraseElementByPos(pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        let curNode = this._head;\n        while (pos--) {\n            curNode = curNode._next;\n        }\n        this._eraseNode(curNode);\n        return this._length;\n    }\n    eraseElementByValue(_value) {\n        let curNode = this._head;\n        while (curNode !== this._header) {\n            if (curNode._value === _value) {\n                this._eraseNode(curNode);\n            }\n            curNode = curNode._next;\n        }\n        return this._length;\n    }\n    eraseElementByIterator(iter) {\n        const node = iter._node;\n        if (node === this._header) {\n            throwIteratorAccessError();\n        }\n        iter = iter.next();\n        this._eraseNode(node);\n        return iter;\n    }\n    pushBack(element) {\n        this._insertNode(element, this._tail);\n        return this._length;\n    }\n    popBack() {\n        if (this._length === 0)\n            return;\n        const value = this._tail._value;\n        this._eraseNode(this._tail);\n        return value;\n    }\n    /**\n     * @description Push an element to the front.\n     * @param element - The element you want to push.\n     * @returns The size of queue after pushing.\n     */\n    pushFront(element) {\n        this._insertNode(element, this._header);\n        return this._length;\n    }\n    /**\n     * @description Removes the first element.\n     * @returns The element you popped.\n     */\n    popFront() {\n        if (this._length === 0)\n            return;\n        const value = this._head._value;\n        this._eraseNode(this._head);\n        return value;\n    }\n    setElementByPos(pos, element) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        let curNode = this._head;\n        while (pos--) {\n            curNode = curNode._next;\n        }\n        curNode._value = element;\n    }\n    insert(pos, element, num = 1) {\n        if (pos < 0 || pos > this._length) {\n            throw new RangeError();\n        }\n        if (num <= 0)\n            return this._length;\n        if (pos === 0) {\n            while (num--)\n                this.pushFront(element);\n        }\n        else if (pos === this._length) {\n            while (num--)\n                this.pushBack(element);\n        }\n        else {\n            let curNode = this._head;\n            for (let i = 1; i < pos; ++i) {\n                curNode = curNode._next;\n            }\n            const next = curNode._next;\n            this._length += num;\n            while (num--) {\n                curNode._next = {\n                    _value: element,\n                    _pre: curNode\n                };\n                curNode._next._pre = curNode;\n                curNode = curNode._next;\n            }\n            curNode._next = next;\n            next._pre = curNode;\n        }\n        return this._length;\n    }\n    find(element) {\n        let curNode = this._head;\n        while (curNode !== this._header) {\n            if (curNode._value === element) {\n                return new LinkListIterator(curNode, this._header, this);\n            }\n            curNode = curNode._next;\n        }\n        return this.end();\n    }\n    reverse() {\n        if (this._length <= 1)\n            return;\n        let pHead = this._head;\n        let pTail = this._tail;\n        let cnt = 0;\n        while ((cnt << 1) < this._length) {\n            const tmp = pHead._value;\n            pHead._value = pTail._value;\n            pTail._value = tmp;\n            pHead = pHead._next;\n            pTail = pTail._pre;\n            cnt += 1;\n        }\n    }\n    unique() {\n        if (this._length <= 1) {\n            return this._length;\n        }\n        let curNode = this._head;\n        while (curNode !== this._header) {\n            let tmpNode = curNode;\n            while (tmpNode._next !== this._header &&\n                tmpNode._value === tmpNode._next._value) {\n                tmpNode = tmpNode._next;\n                this._length -= 1;\n            }\n            curNode._next = tmpNode._next;\n            curNode._next._pre = curNode;\n            curNode = curNode._next;\n        }\n        return this._length;\n    }\n    sort(cmp) {\n        if (this._length <= 1)\n            return;\n        const arr = [];\n        this.forEach(function (el) {\n            arr.push(el);\n        });\n        arr.sort(cmp);\n        let curNode = this._head;\n        arr.forEach(function (element) {\n            curNode._value = element;\n            curNode = curNode._next;\n        });\n    }\n    /**\n     * @description Merges two sorted lists.\n     * @param list - The other list you want to merge (must be sorted).\n     * @returns The size of list after merging.\n     * @example\n     * const linkA = new LinkList([1, 3, 5]);\n     * const linkB = new LinkList([2, 4, 6]);\n     * linkA.merge(linkB);  // [1, 2, 3, 4, 5];\n     */\n    merge(list) {\n        const self = this;\n        if (this._length === 0) {\n            list.forEach(function (el) {\n                self.pushBack(el);\n            });\n        }\n        else {\n            let curNode = this._head;\n            list.forEach(function (el) {\n                while (curNode !== self._header &&\n                    curNode._value <= el) {\n                    curNode = curNode._next;\n                }\n                self._insertNode(el, curNode._pre);\n            });\n        }\n        return this._length;\n    }\n    forEach(callback) {\n        let curNode = this._head;\n        let index = 0;\n        while (curNode !== this._header) {\n            callback(curNode._value, index++, this);\n            curNode = curNode._next;\n        }\n    }\n    [Symbol.iterator]() {\n        return function* () {\n            if (this._length === 0)\n                return;\n            let curNode = this._head;\n            while (curNode !== this._header) {\n                yield curNode._value;\n                curNode = curNode._next;\n            }\n        }.bind(this)();\n    }\n}\nexport default LinkList;\n", "import SequentialContainer from './Base';\nimport { ContainerIterator, initContainer, IteratorType } from '@/container/ContainerBase';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\ntype LinkNode<T> = {\n  _value: T;\n  _pre: LinkNode<T>;\n  _next: LinkNode<T>;\n}\n\nclass LinkListIterator<T> extends ContainerIterator<T> {\n  readonly container: LinkList<T>;\n  /**\n   * @internal\n   */\n  _node: LinkNode<T>;\n  /**\n   * @internal\n   */\n  private readonly _header: LinkNode<T>;\n  /**\n   * @internal\n   */\n  constructor(\n    _node: LinkNode<T>,\n    _header: LinkNode<T>,\n    container: LinkList<T>,\n    iteratorType?: IteratorType\n  ) {\n    super(iteratorType);\n    this._node = _node;\n    this._header = _header;\n    this.container = container;\n    if (this.iteratorType === IteratorType.NORMAL) {\n      this.pre = function () {\n        if (this._node._pre === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next;\n        return this;\n      };\n    } else {\n      this.pre = function () {\n        if (this._node._next === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre;\n        return this;\n      };\n    }\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    return this._node._value;\n  }\n  set pointer(newValue: T) {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    this._node._value = newValue;\n  }\n  copy() {\n    return new LinkListIterator<T>(this._node, this._header, this.container, this.iteratorType);\n  }\n  // @ts-ignore\n  equals(iter: LinkListIterator<T>): boolean;\n  // @ts-ignore\n  pre(): this;\n  // @ts-ignore\n  next(): this;\n}\n\nexport type { LinkListIterator };\n\nclass LinkList<T> extends SequentialContainer<T> {\n  /**\n   * @internal\n   */\n  private _head: LinkNode<T>;\n  /**\n   * @internal\n   */\n  private _tail: LinkNode<T>;\n  /**\n   * @internal\n   */\n  private readonly _header: LinkNode<T>;\n  constructor(container: initContainer<T> = []) {\n    super();\n    this._header = <LinkNode<T>>{};\n    this._head = this._tail = this._header._pre = this._header._next = this._header;\n    const self = this;\n    container.forEach(function (el) {\n      self.pushBack(el);\n    });\n  }\n  /**\n   * @internal\n   */\n  private _eraseNode(node: LinkNode<T>) {\n    const { _pre, _next } = node;\n    _pre._next = _next;\n    _next._pre = _pre;\n    if (node === this._head) {\n      this._head = _next;\n    }\n    if (node === this._tail) {\n      this._tail = _pre;\n    }\n    this._length -= 1;\n  }\n  /**\n   * @internal\n   */\n  private _insertNode(value: T, pre: LinkNode<T>) {\n    const next = pre._next;\n    const node = {\n      _value: value,\n      _pre: pre,\n      _next: next\n    };\n    pre._next = node;\n    next._pre = node;\n    if (pre === this._header) {\n      this._head = node;\n    }\n    if (next === this._header) {\n      this._tail = node;\n    }\n    this._length += 1;\n  }\n  clear() {\n    this._length = 0;\n    this._head = this._tail = this._header._pre = this._header._next = this._header;\n  }\n  begin() {\n    return new LinkListIterator<T>(this._head, this._header, this);\n  }\n  end() {\n    return new LinkListIterator<T>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new LinkListIterator<T>(this._tail, this._header, this, IteratorType.REVERSE);\n  }\n  rEnd() {\n    return new LinkListIterator<T>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front(): T | undefined {\n    return this._head._value;\n  }\n  back(): T | undefined {\n    return this._tail._value;\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let curNode = this._head;\n    while (pos--) {\n      curNode = curNode._next;\n    }\n    return curNode._value;\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let curNode = this._head;\n    while (pos--) {\n      curNode = curNode._next;\n    }\n    this._eraseNode(curNode);\n    return this._length;\n  }\n  eraseElementByValue(_value: T) {\n    let curNode = this._head;\n    while (curNode !== this._header) {\n      if (curNode._value === _value) {\n        this._eraseNode(curNode);\n      }\n      curNode = curNode._next;\n    }\n    return this._length;\n  }\n  eraseElementByIterator(iter: LinkListIterator<T>) {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    iter = iter.next();\n    this._eraseNode(node);\n    return iter;\n  }\n  pushBack(element: T) {\n    this._insertNode(element, this._tail);\n    return this._length;\n  }\n  popBack() {\n    if (this._length === 0) return;\n    const value = this._tail._value;\n    this._eraseNode(this._tail);\n    return value;\n  }\n  /**\n   * @description Push an element to the front.\n   * @param element - The element you want to push.\n   * @returns The size of queue after pushing.\n   */\n  pushFront(element: T) {\n    this._insertNode(element, this._header);\n    return this._length;\n  }\n  /**\n   * @description Removes the first element.\n   * @returns The element you popped.\n   */\n  popFront() {\n    if (this._length === 0) return;\n    const value = this._head._value;\n    this._eraseNode(this._head);\n    return value;\n  }\n  setElementByPos(pos: number, element: T) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let curNode = this._head;\n    while (pos--) {\n      curNode = curNode._next;\n    }\n    curNode._value = element;\n  }\n  insert(pos: number, element: T, num = 1) {\n    $checkWithinAccessParams!(pos, 0, this._length);\n    if (num <= 0) return this._length;\n    if (pos === 0) {\n      while (num--) this.pushFront(element);\n    } else if (pos === this._length) {\n      while (num--) this.pushBack(element);\n    } else {\n      let curNode = this._head;\n      for (let i = 1; i < pos; ++i) {\n        curNode = curNode._next;\n      }\n      const next = curNode._next;\n      this._length += num;\n      while (num--) {\n        curNode._next = <LinkNode<T>>{\n          _value: element,\n          _pre: curNode\n        };\n        curNode._next._pre = curNode;\n        curNode = curNode._next;\n      }\n      curNode._next = next;\n      next._pre = curNode;\n    }\n    return this._length;\n  }\n  find(element: T) {\n    let curNode = this._head;\n    while (curNode !== this._header) {\n      if (curNode._value === element) {\n        return new LinkListIterator<T>(curNode, this._header, this);\n      }\n      curNode = curNode._next;\n    }\n    return this.end();\n  }\n  reverse() {\n    if (this._length <= 1) return;\n    let pHead = this._head;\n    let pTail = this._tail;\n    let cnt = 0;\n    while ((cnt << 1) < this._length) {\n      const tmp = pHead._value;\n      pHead._value = pTail._value;\n      pTail._value = tmp;\n      pHead = pHead._next;\n      pTail = pTail._pre;\n      cnt += 1;\n    }\n  }\n  unique() {\n    if (this._length <= 1) {\n      return this._length;\n    }\n    let curNode = this._head;\n    while (curNode !== this._header) {\n      let tmpNode = curNode;\n      while (\n        tmpNode._next !== this._header &&\n        tmpNode._value === tmpNode._next._value\n      ) {\n        tmpNode = tmpNode._next;\n        this._length -= 1;\n      }\n      curNode._next = tmpNode._next;\n      curNode._next._pre = curNode;\n      curNode = curNode._next;\n    }\n    return this._length;\n  }\n  sort(cmp?: (x: T, y: T) => number) {\n    if (this._length <= 1) return;\n    const arr: T[] = [];\n    this.forEach(function (el) {\n      arr.push(el);\n    });\n    arr.sort(cmp);\n    let curNode: LinkNode<T> = this._head;\n    arr.forEach(function (element) {\n      curNode._value = element;\n      curNode = curNode._next;\n    });\n  }\n  /**\n   * @description Merges two sorted lists.\n   * @param list - The other list you want to merge (must be sorted).\n   * @returns The size of list after merging.\n   * @example\n   * const linkA = new LinkList([1, 3, 5]);\n   * const linkB = new LinkList([2, 4, 6]);\n   * linkA.merge(linkB);  // [1, 2, 3, 4, 5];\n   */\n  merge(list: LinkList<T>) {\n    const self = this;\n    if (this._length === 0) {\n      list.forEach(function (el) {\n        self.pushBack(el);\n      });\n    } else {\n      let curNode = this._head;\n      list.forEach(function (el) {\n        while (\n          curNode !== self._header &&\n          curNode._value <= el\n        ) {\n          curNode = curNode._next;\n        }\n        self._insertNode(el, curNode._pre);\n      });\n    }\n    return this._length;\n  }\n  forEach(callback: (element: T, index: number, list: LinkList<T>) => void) {\n    let curNode = this._head;\n    let index = 0;\n    while (curNode !== this._header) {\n      callback(curNode._value, index++, this);\n      curNode = curNode._next;\n    }\n  }\n  [Symbol.iterator]() {\n    return function * (this: LinkList<T>) {\n      if (this._length === 0) return;\n      let curNode = this._head;\n      while (curNode !== this._header) {\n        yield curNode._value;\n        curNode = curNode._next;\n      }\n    }.bind(this)();\n  }\n}\n\nexport default LinkList;\n"]}