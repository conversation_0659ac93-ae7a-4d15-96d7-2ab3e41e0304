{"version": 3, "sources": ["container/TreeContainer/OrderedSet.js", "../../src/container/TreeContainer/OrderedSet.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_Base", "_interopRequireDefault", "require", "_TreeIterator", "_throwError", "obj", "__esModule", "OrderedSetIterator", "TreeIterator", "constructor", "node", "header", "container", "iteratorType", "super", "this", "pointer", "_node", "_header", "throwIteratorAccessError", "_key", "copy", "OrderedSet", "TreeC<PERSON>r", "cmp", "enableIndex", "self", "for<PERSON>ach", "el", "insert", "K", "curNode", "undefined", "_iterationFunc", "_left", "_right", "begin", "end", "rBegin", "rEnd", "front", "back", "key", "hint", "_set", "find", "element", "resNode", "_findElementNode", "_root", "lowerBound", "_lowerBound", "upperBound", "_upperBound", "reverseLowerBound", "_reverseLowerBound", "reverseUpperBound", "_reverseUpperBound", "union", "other", "_length", "Symbol", "iterator", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACLvB,IAAAC,QAAAC,uBAAAC,QAAA;;AACA,IAAAC,gBAAAF,uBAAAC,QAAA;;AAGA,IAAAE,cAAAF,QAAA;;AAA8D,SAAAD,uBAAAI;IAAA,OAAAA,KAAAA,EAAAC,IAAAD,IAAA;QAAAN,SAAAM;;AAAA;;AAE9D,MAAME,2BAA8BC,cAAAA;IAElCC,YACEC,GACAC,GACAC,GACAC;QAEAC,MAAMJ,GAAMC,GAAQE;QACpBE,KAAKH,YAAYA;ADRjB;ICUEI;QACF,IAAID,KAAKE,MAAUF,KAAKG,GAAS;aAC/B,GAAAC,YAAAA;ADRE;QCUJ,OAAOJ,KAAKE,EAAMG;ADRlB;ICUFC;QACE,OAAO,IAAId,mBACTQ,KAAKE,GACLF,KAAKG,GACLH,KAAKH,WACLG,KAAKF;ADZP;;;ACqBJ,MAAMS,mBAAsBC,MAAAA;IAW1Bd,YACEG,IAA8B,IAC9BY,GACAC;QAEAX,MAAMU,GAAKC;QACX,MAAMC,IAAOX;QACbH,EAAUe,SAAQ,SAAUC;YAC1BF,EAAKG,OAAOD;ADtBV;AACJ;IC2BME,IACNC;QAEA,IAAIA,MAAYC,WAAW;eACnBjB,KAAKkB,EAAeF,EAAQG;cAC9BH,EAAQX;eACNL,KAAKkB,EAAeF,EAAQI;ADvBpC;ICyBFC;QACE,OAAO,IAAI7B,mBACTQ,KAAKG,EAAQgB,KAASnB,KAAKG,GAC3BH,KAAKG,GACLH;AD1BF;IC6BFsB;QACE,OAAO,IAAI9B,mBAAsBQ,KAAKG,GAASH,KAAKG,GAASH;AD3B7D;IC6BFuB;QACE,OAAO,IAAI/B,mBACTQ,KAAKG,EAAQiB,KAAUpB,KAAKG,GAC5BH,KAAKG,GACLH,MAAI;AD9BN;ICkCFwB;QACE,OAAO,IAAIhC,mBAAsBQ,KAAKG,GAASH,KAAKG,GAASH,MAAI;ADhCjE;ICkCFyB;QACE,OAAOzB,KAAKG,EAAQgB,IAAQnB,KAAKG,EAAQgB,EAAMd,IAAOY;ADhCtD;ICkCFS;QACE,OAAO1B,KAAKG,EAAQiB,IAASpB,KAAKG,EAAQiB,EAAOf,IAAOY;ADhCxD;IC6CFH,OAAOa,GAAQC;QACb,OAAO5B,KAAK6B,EAAKF,GAAKV,WAAWW;ADhCjC;ICkCFE,KAAKC;QACH,MAAMC,IAAUhC,KAAKiC,EAAiBjC,KAAKkC,GAAOH;QAClD,OAAO,IAAIvC,mBAAsBwC,GAAShC,KAAKG,GAASH;ADhCxD;ICkCFmC,WAAWR;QACT,MAAMK,IAAUhC,KAAKoC,EAAYpC,KAAKkC,GAAOP;QAC7C,OAAO,IAAInC,mBAAsBwC,GAAShC,KAAKG,GAASH;ADhCxD;ICkCFqC,WAAWV;QACT,MAAMK,IAAUhC,KAAKsC,EAAYtC,KAAKkC,GAAOP;QAC7C,OAAO,IAAInC,mBAAsBwC,GAAShC,KAAKG,GAASH;ADhCxD;ICkCFuC,kBAAkBZ;QAChB,MAAMK,IAAUhC,KAAKwC,EAAmBxC,KAAKkC,GAAOP;QACpD,OAAO,IAAInC,mBAAsBwC,GAAShC,KAAKG,GAASH;ADhCxD;ICkCFyC,kBAAkBd;QAChB,MAAMK,IAAUhC,KAAK0C,GAAmB1C,KAAKkC,GAAOP;QACpD,OAAO,IAAInC,mBAAsBwC,GAAShC,KAAKG,GAASH;ADhCxD;ICkCF2C,MAAMC;QACJ,MAAMjC,IAAOX;QACb4C,EAAMhC,SAAQ,SAAUC;YACtBF,EAAKG,OAAOD;ADhCV;QCkCJ,OAAOb,KAAK6C;ADhCZ;ICkCF,CAACC,OAAOC;QACN,OAAO/C,KAAKkB,EAAelB,KAAKkC;ADhChC;;;ACwCH,IAAAc,WAEczC;;AAAUzB,QAAAE,UAAAgE", "file": "OrderedSet.js", "sourcesContent": ["import TreeContainer from './Base';\nimport TreeIterator from './Base/TreeIterator';\nimport { throwIteratorAccessError } from \"../../utils/throwError\";\nclass OrderedSetIterator extends TreeIterator {\n    constructor(node, header, container, iteratorType) {\n        super(node, header, iteratorType);\n        this.container = container;\n    }\n    get pointer() {\n        if (this._node === this._header) {\n            throwIteratorAccessError();\n        }\n        return this._node._key;\n    }\n    copy() {\n        return new OrderedSetIterator(this._node, this._header, this.container, this.iteratorType);\n    }\n}\nclass OrderedSet extends TreeContainer {\n    /**\n     * @param container - The initialization container.\n     * @param cmp - The compare function.\n     * @param enableIndex - Whether to enable iterator indexing function.\n     * @example\n     * new OrderedSet();\n     * new OrderedSet([0, 1, 2]);\n     * new OrderedSet([0, 1, 2], (x, y) => x - y);\n     * new OrderedSet([0, 1, 2], (x, y) => x - y, true);\n     */\n    constructor(container = [], cmp, enableIndex) {\n        super(cmp, enableIndex);\n        const self = this;\n        container.forEach(function (el) {\n            self.insert(el);\n        });\n    }\n    /**\n     * @internal\n     */\n    *_iterationFunc(curNode) {\n        if (curNode === undefined)\n            return;\n        yield* this._iterationFunc(curNode._left);\n        yield curNode._key;\n        yield* this._iterationFunc(curNode._right);\n    }\n    begin() {\n        return new OrderedSetIterator(this._header._left || this._header, this._header, this);\n    }\n    end() {\n        return new OrderedSetIterator(this._header, this._header, this);\n    }\n    rBegin() {\n        return new OrderedSetIterator(this._header._right || this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    }\n    rEnd() {\n        return new OrderedSetIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    }\n    front() {\n        return this._header._left ? this._header._left._key : undefined;\n    }\n    back() {\n        return this._header._right ? this._header._right._key : undefined;\n    }\n    /**\n     * @description Insert element to set.\n     * @param key - The key want to insert.\n     * @param hint - You can give an iterator hint to improve insertion efficiency.\n     * @return The size of container after setting.\n     * @example\n     * const st = new OrderedSet([2, 4, 5]);\n     * const iter = st.begin();\n     * st.insert(1);\n     * st.insert(3, iter);  // give a hint will be faster.\n     */\n    insert(key, hint) {\n        return this._set(key, undefined, hint);\n    }\n    find(element) {\n        const resNode = this._findElementNode(this._root, element);\n        return new OrderedSetIterator(resNode, this._header, this);\n    }\n    lowerBound(key) {\n        const resNode = this._lowerBound(this._root, key);\n        return new OrderedSetIterator(resNode, this._header, this);\n    }\n    upperBound(key) {\n        const resNode = this._upperBound(this._root, key);\n        return new OrderedSetIterator(resNode, this._header, this);\n    }\n    reverseLowerBound(key) {\n        const resNode = this._reverseLowerBound(this._root, key);\n        return new OrderedSetIterator(resNode, this._header, this);\n    }\n    reverseUpperBound(key) {\n        const resNode = this._reverseUpperBound(this._root, key);\n        return new OrderedSetIterator(resNode, this._header, this);\n    }\n    union(other) {\n        const self = this;\n        other.forEach(function (el) {\n            self.insert(el);\n        });\n        return this._length;\n    }\n    [Symbol.iterator]() {\n        return this._iterationFunc(this._root);\n    }\n}\nexport default OrderedSet;\n", "import TreeContainer from './Base';\nimport TreeIterator from './Base/TreeIterator';\nimport { TreeNode } from './Base/TreeNode';\nimport { initContainer, IteratorType } from '@/container/ContainerBase';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nclass OrderedSetIterator<K> extends TreeIterator<K, undefined> {\n  container: OrderedSet<K>;\n  constructor(\n    node: TreeNode<K, undefined>,\n    header: TreeNode<K, undefined>,\n    container: OrderedSet<K>,\n    iteratorType?: IteratorType\n  ) {\n    super(node, header, iteratorType);\n    this.container = container;\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    return this._node._key!;\n  }\n  copy() {\n    return new OrderedSetIterator<K>(\n      this._node,\n      this._header,\n      this.container,\n      this.iteratorType\n    );\n  }\n  // @ts-ignore\n  equals(iter: OrderedSetIterator<K>): boolean;\n}\n\nexport type { OrderedSetIterator };\n\nclass OrderedSet<K> extends <PERSON>Container<K, undefined> {\n  /**\n   * @param container - The initialization container.\n   * @param cmp - The compare function.\n   * @param enableIndex - Whether to enable iterator indexing function.\n   * @example\n   * new OrderedSet();\n   * new OrderedSet([0, 1, 2]);\n   * new OrderedSet([0, 1, 2], (x, y) => x - y);\n   * new OrderedSet([0, 1, 2], (x, y) => x - y, true);\n   */\n  constructor(\n    container: initContainer<K> = [],\n    cmp?: (x: K, y: K) => number,\n    enableIndex?: boolean\n  ) {\n    super(cmp, enableIndex);\n    const self = this;\n    container.forEach(function (el) {\n      self.insert(el);\n    });\n  }\n  /**\n   * @internal\n   */\n  private * _iterationFunc(\n    curNode: TreeNode<K, undefined> | undefined\n  ): Generator<K, void> {\n    if (curNode === undefined) return;\n    yield * this._iterationFunc(curNode._left);\n    yield curNode._key!;\n    yield * this._iterationFunc(curNode._right);\n  }\n  begin() {\n    return new OrderedSetIterator<K>(\n      this._header._left || this._header,\n      this._header,\n      this\n    );\n  }\n  end() {\n    return new OrderedSetIterator<K>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new OrderedSetIterator<K>(\n      this._header._right || this._header,\n      this._header,\n      this,\n      IteratorType.REVERSE\n    );\n  }\n  rEnd() {\n    return new OrderedSetIterator<K>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front() {\n    return this._header._left ? this._header._left._key : undefined;\n  }\n  back() {\n    return this._header._right ? this._header._right._key : undefined;\n  }\n  /**\n   * @description Insert element to set.\n   * @param key - The key want to insert.\n   * @param hint - You can give an iterator hint to improve insertion efficiency.\n   * @return The size of container after setting.\n   * @example\n   * const st = new OrderedSet([2, 4, 5]);\n   * const iter = st.begin();\n   * st.insert(1);\n   * st.insert(3, iter);  // give a hint will be faster.\n   */\n  insert(key: K, hint?: OrderedSetIterator<K>) {\n    return this._set(key, undefined, hint);\n  }\n  find(element: K) {\n    const resNode = this._findElementNode(this._root, element);\n    return new OrderedSetIterator<K>(resNode, this._header, this);\n  }\n  lowerBound(key: K) {\n    const resNode = this._lowerBound(this._root, key);\n    return new OrderedSetIterator<K>(resNode, this._header, this);\n  }\n  upperBound(key: K) {\n    const resNode = this._upperBound(this._root, key);\n    return new OrderedSetIterator<K>(resNode, this._header, this);\n  }\n  reverseLowerBound(key: K) {\n    const resNode = this._reverseLowerBound(this._root, key);\n    return new OrderedSetIterator<K>(resNode, this._header, this);\n  }\n  reverseUpperBound(key: K) {\n    const resNode = this._reverseUpperBound(this._root, key);\n    return new OrderedSetIterator<K>(resNode, this._header, this);\n  }\n  union(other: OrderedSet<K>) {\n    const self = this;\n    other.forEach(function (el) {\n      self.insert(el);\n    });\n    return this._length;\n  }\n  [Symbol.iterator]() {\n    return this._iterationFunc(this._root);\n  }\n  // @ts-ignore\n  eraseElementByIterator(iter: OrderedSetIterator<K>): OrderedSetIterator<K>;\n  // @ts-ignore\n  forEach(callback: (element: K, index: number, tree: OrderedSet<K>) => void): void;\n  // @ts-ignore\n  getElementByPos(pos: number): K;\n}\n\nexport default OrderedSet;\n"]}