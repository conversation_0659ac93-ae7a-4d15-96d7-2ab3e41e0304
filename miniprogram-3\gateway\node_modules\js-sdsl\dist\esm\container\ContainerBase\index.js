var __extends = this && this.t || function() {
    var extendStatics = function(n, t) {
        extendStatics = Object.setPrototypeOf || {
            __proto__: []
        } instanceof Array && function(n, t) {
            n.__proto__ = t;
        } || function(n, t) {
            for (var r in t) if (Object.prototype.hasOwnProperty.call(t, r)) n[r] = t[r];
        };
        return extendStatics(n, t);
    };
    return function(n, t) {
        if (typeof t !== "function" && t !== null) throw new TypeError("Class extends value " + String(t) + " is not a constructor or null");
        extendStatics(n, t);
        function __() {
            this.constructor = n;
        }
        n.prototype = t === null ? Object.create(t) : (__.prototype = t.prototype, new __);
    };
}();

var ContainerIterator = function() {
    function ContainerIterator(n) {
        if (n === void 0) {
            n = 0;
        }
        this.iteratorType = n;
    }
    ContainerIterator.prototype.equals = function(n) {
        return this.o === n.o;
    };
    return ContainerIterator;
}();

export { ContainerIterator };

var Base = function() {
    function Base() {
        this.M = 0;
    }
    Object.defineProperty(Base.prototype, "length", {
        get: function() {
            return this.M;
        },
        enumerable: false,
        configurable: true
    });
    Base.prototype.size = function() {
        return this.M;
    };
    Base.prototype.empty = function() {
        return this.M === 0;
    };
    return Base;
}();

export { Base };

var Container = function(n) {
    __extends(Container, n);
    function Container() {
        return n !== null && n.apply(this, arguments) || this;
    }
    return Container;
}(Base);

export { Container };
//# sourceMappingURL=index.js.map
