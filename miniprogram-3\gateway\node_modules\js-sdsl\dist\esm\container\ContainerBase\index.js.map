{"version": 3, "sources": ["container/ContainerBase/index.js", "../../src/container/ContainerBase/index.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "ContainerIterator", "iteratorType", "equals", "iter", "_node", "Base", "_length", "defineProperty", "get", "enumerable", "configurable", "size", "empty", "Container", "_super", "apply", "arguments"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;ACQ7C,IAAAG,oBAAA;IAkBE,SAAAA,kBAAsBC;QAAA,IAAAA,WAAA,GAAA;YAAAA,IAAA;AAAkC;QACtDlB,KAAKkB,eAAeA;ADLpB;ICaFD,kBAAAR,UAAAU,SAAA,SAAOC;QACL,OAAOpB,KAAKqB,MAAUD,EAAKC;ADL3B;ICsDJ,OAAAJ;AAAA,CA7EA;;SD0BSA;;ACqDT,IAAAK,OAAA;IAAA,SAAAA;QAKYtB,KAAAuB,IAAU;AAmCtB;IA5BEnB,OAAAoB,eAAIF,KAAAb,WAAA,UAAM;QDjDJgB,KCiDN;YACE,OAAOzB,KAAKuB;ADhDR;QACAG,YAAY;QACZC,cAAc;;ICsDpBL,KAAAb,UAAAmB,OAAA;QACE,OAAO5B,KAAKuB;AD7CZ;ICqDFD,KAAAb,UAAAoB,QAAA;QACE,OAAO7B,KAAKuB,MAAY;AD7CxB;ICsDJ,OAAAD;AAAA,CAxCA;;SDXSA;;ACqDT,IAAAQ,YAAA,SAAAC;IAA2ChC,UAAA+B,WAAAC;IAA3C,SAAAD;QDjDQ,OAAOC,MAAW,QAAQA,EAAOC,MAAMhC,MAAMiC,cAAcjC;ACiJnE;IAAA,OAAA8B;AAAA,CAhGA,CAA2CR;;SD7ClCQ", "file": "index.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar ContainerIterator = /** @class */ (function () {\n    /**\n     * @internal\n     */\n    function ContainerIterator(iteratorType) {\n        if (iteratorType === void 0) { iteratorType = 0 /* IteratorType.NORMAL */; }\n        this.iteratorType = iteratorType;\n    }\n    /**\n     * @param iter - The other iterator you want to compare.\n     * @returns Whether this equals to obj.\n     * @example\n     * container.find(1).equals(container.end());\n     */\n    ContainerIterator.prototype.equals = function (iter) {\n        return this._node === iter._node;\n    };\n    return ContainerIterator;\n}());\nexport { ContainerIterator };\nvar Base = /** @class */ (function () {\n    function Base() {\n        /**\n         * @description Container's size.\n         * @internal\n         */\n        this._length = 0;\n    }\n    Object.defineProperty(Base.prototype, \"length\", {\n        /**\n         * @returns The size of the container.\n         * @example\n         * const container = new Vector([1, 2]);\n         * console.log(container.length); // 2\n         */\n        get: function () {\n            return this._length;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * @returns The size of the container.\n     * @example\n     * const container = new Vector([1, 2]);\n     * console.log(container.size()); // 2\n     */\n    Base.prototype.size = function () {\n        return this._length;\n    };\n    /**\n     * @returns Whether the container is empty.\n     * @example\n     * container.clear();\n     * console.log(container.empty());  // true\n     */\n    Base.prototype.empty = function () {\n        return this._length === 0;\n    };\n    return Base;\n}());\nexport { Base };\nvar Container = /** @class */ (function (_super) {\n    __extends(Container, _super);\n    function Container() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return Container;\n}(Base));\nexport { Container };\n", "/**\n * @description The iterator type including `NORMAL` and `REVERSE`.\n */\nexport const enum IteratorType {\n  NORMAL = 0,\n  REVERSE = 1\n}\n\nexport abstract class ContainerIterator<T> {\n  /**\n   * @description The container pointed to by the iterator.\n   */\n  abstract readonly container: Container<T>;\n  /**\n   * @internal\n   */\n  abstract _node: unknown;\n  /**\n   * @description Iterator's type.\n   * @example\n   * console.log(container.end().iteratorType === IteratorType.NORMAL);  // true\n   */\n  readonly iteratorType: IteratorType;\n  /**\n   * @internal\n   */\n  protected constructor(iteratorType = IteratorType.NORMAL) {\n    this.iteratorType = iteratorType;\n  }\n  /**\n   * @param iter - The other iterator you want to compare.\n   * @returns Whether this equals to obj.\n   * @example\n   * container.find(1).equals(container.end());\n   */\n  equals(iter: ContainerIterator<T>) {\n    return this._node === iter._node;\n  }\n  /**\n   * @description Pointers to element.\n   * @returns The value of the pointer's element.\n   * @example\n   * const val = container.begin().pointer;\n   */\n  abstract get pointer(): T;\n  /**\n   * @description Set pointer's value (some containers are unavailable).\n   * @param newValue - The new value you want to set.\n   * @example\n   * (<LinkList<number>>container).begin().pointer = 1;\n   */\n  abstract set pointer(newValue: T);\n  /**\n   * @description Move `this` iterator to pre.\n   * @returns The iterator's self.\n   * @example\n   * const iter = container.find(1);  // container = [0, 1]\n   * const pre = iter.pre();\n   * console.log(pre === iter);  // true\n   * console.log(pre.equals(iter));  // true\n   * console.log(pre.pointer, iter.pointer); // 0, 0\n   */\n  abstract pre(): this;\n  /**\n   * @description Move `this` iterator to next.\n   * @returns The iterator's self.\n   * @example\n   * const iter = container.find(1);  // container = [1, 2]\n   * const next = iter.next();\n   * console.log(next === iter);  // true\n   * console.log(next.equals(iter));  // true\n   * console.log(next.pointer, iter.pointer); // 2, 2\n   */\n  abstract next(): this;\n  /**\n   * @description Get a copy of itself.\n   * @returns The copy of self.\n   * @example\n   * const iter = container.find(1);  // container = [1, 2]\n   * const next = iter.copy().next();\n   * console.log(next === iter);  // false\n   * console.log(next.equals(iter));  // false\n   * console.log(next.pointer, iter.pointer); // 2, 1\n   */\n  abstract copy(): ContainerIterator<T>;\n}\n\nexport abstract class Base {\n  /**\n   * @description Container's size.\n   * @internal\n   */\n  protected _length = 0;\n  /**\n   * @returns The size of the container.\n   * @example\n   * const container = new Vector([1, 2]);\n   * console.log(container.length); // 2\n   */\n  get length() {\n    return this._length;\n  }\n  /**\n   * @returns The size of the container.\n   * @example\n   * const container = new Vector([1, 2]);\n   * console.log(container.size()); // 2\n   */\n  size() {\n    return this._length;\n  }\n  /**\n   * @returns Whether the container is empty.\n   * @example\n   * container.clear();\n   * console.log(container.empty());  // true\n   */\n  empty() {\n    return this._length === 0;\n  }\n  /**\n   * @description Clear the container.\n   * @example\n   * container.clear();\n   * console.log(container.empty());  // true\n   */\n  abstract clear(): void;\n}\n\nexport abstract class Container<T> extends Base {\n  /**\n   * @returns Iterator pointing to the beginning element.\n   * @example\n   * const begin = container.begin();\n   * const end = container.end();\n   * for (const it = begin; !it.equals(end); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract begin(): ContainerIterator<T>;\n  /**\n   * @returns Iterator pointing to the super end like c++.\n   * @example\n   * const begin = container.begin();\n   * const end = container.end();\n   * for (const it = begin; !it.equals(end); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract end(): ContainerIterator<T>;\n  /**\n   * @returns Iterator pointing to the end element.\n   * @example\n   * const rBegin = container.rBegin();\n   * const rEnd = container.rEnd();\n   * for (const it = rBegin; !it.equals(rEnd); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract rBegin(): ContainerIterator<T>;\n  /**\n   * @returns Iterator pointing to the super begin like c++.\n   * @example\n   * const rBegin = container.rBegin();\n   * const rEnd = container.rEnd();\n   * for (const it = rBegin; !it.equals(rEnd); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract rEnd(): ContainerIterator<T>;\n  /**\n   * @returns The first element of the container.\n   */\n  abstract front(): T | undefined;\n  /**\n   * @returns The last element of the container.\n   */\n  abstract back(): T | undefined;\n  /**\n   * @param element - The element you want to find.\n   * @returns An iterator pointing to the element if found, or super end if not found.\n   * @example\n   * container.find(1).equals(container.end());\n   */\n  abstract find(element: T): ContainerIterator<T>;\n  /**\n   * @description Iterate over all elements in the container.\n   * @param callback - Callback function like Array.forEach.\n   * @example\n   * container.forEach((element, index) => console.log(element, index));\n   */\n  abstract forEach(callback: (element: T, index: number, container: Container<T>) => void): void;\n  /**\n   * @description Gets the value of the element at the specified position.\n   * @example\n   * const val = container.getElementByPos(-1); // throw a RangeError\n   */\n  abstract getElementByPos(pos: number): T;\n  /**\n   * @description Removes the element at the specified position.\n   * @param pos - The element's position you want to remove.\n   * @returns The container length after erasing.\n   * @example\n   * container.eraseElementByPos(-1); // throw a RangeError\n   */\n  abstract eraseElementByPos(pos: number): number;\n  /**\n   * @description Removes element by iterator and move `iter` to next.\n   * @param iter - The iterator you want to erase.\n   * @returns The next iterator.\n   * @example\n   * container.eraseElementByIterator(container.begin());\n   * container.eraseElementByIterator(container.end()); // throw a RangeError\n   */\n  abstract eraseElementByIterator(\n    iter: ContainerIterator<T>\n  ): ContainerIterator<T>;\n  /**\n   * @description Using for `for...of` syntax like Array.\n   * @example\n   * for (const element of container) {\n   *   console.log(element);\n   * }\n   */\n  abstract [Symbol.iterator](): Generator<T, void>;\n}\n\n/**\n * @description The initial data type passed in when initializing the container.\n */\nexport type initContainer<T> = {\n  size?: number | (() => number);\n  length?: number;\n  forEach: (callback: (el: T) => void) => void;\n}\n"]}