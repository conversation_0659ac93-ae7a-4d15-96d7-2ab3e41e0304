{"version": 3, "sources": ["container/HashContainer/HashSet.js", "../../src/container/HashContainer/HashSet.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "__generator", "thisArg", "body", "_", "label", "sent", "t", "trys", "ops", "f", "y", "g", "next", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "step", "op", "done", "value", "pop", "length", "push", "e", "HashC<PERSON>r", "HashContainerIterator", "throwIteratorAccessError", "HashSetIterator", "_super", "node", "header", "container", "iteratorType", "_this", "defineProperty", "get", "_node", "_header", "_key", "enumerable", "configurable", "copy", "HashSet", "self", "for<PERSON>ach", "el", "insert", "begin", "_head", "end", "rBegin", "_tail", "rEnd", "front", "back", "key", "isObject", "_set", "undefined", "getElementByPos", "pos", "_length", "RangeError", "_next", "find", "_findElementNode", "callback", "index", "_a", "bind"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;AAe7C,IAAIG,cAAejB,QAAQA,KAAKiB,KAAgB,SAAUC,GAASC;IAC/D,IAAIC,IAAI;QAAEC,OAAO;QAAGC,MAAM;YAAa,IAAIC,EAAE,KAAK,GAAG,MAAMA,EAAE;YAAI,OAAOA,EAAE;AAAI;QAAGC,MAAM;QAAIC,KAAK;OAAMC,GAAGC,GAAGJ,GAAGK;IAC/G,OAAOA,IAAI;QAAEC,MAAMC,KAAK;QAAIC,OAASD,KAAK;QAAIE,QAAUF,KAAK;cAAaG,WAAW,eAAeL,EAAEK,OAAOC,YAAY;QAAa,OAAOlC;AAAM,QAAI4B;IACvJ,SAASE,KAAKK;QAAK,OAAO,SAAUC;YAAK,OAAOC,KAAK,EAACF,GAAGC;AAAK;AAAG;IACjE,SAASC,KAAKC;QACV,IAAIZ,GAAG,MAAM,IAAId,UAAU;QAC3B,OAAOQ;YACH,IAAIM,IAAI,GAAGC,MAAMJ,IAAIe,EAAG,KAAK,IAAIX,EAAE,YAAYW,EAAG,KAAKX,EAAE,cAAcJ,IAAII,EAAE,cAAcJ,EAAEZ,KAAKgB;YAAI,KAAKA,EAAEE,WAAWN,IAAIA,EAAEZ,KAAKgB,GAAGW,EAAG,KAAKC,MAAM,OAAOhB;YAC3J,IAAII,IAAI,GAAGJ,GAAGe,IAAK,EAACA,EAAG,KAAK,GAAGf,EAAEiB;YACjC,QAAQF,EAAG;cACP,KAAK;cAAG,KAAK;gBAAGf,IAAIe;gBAAI;;cACxB,KAAK;gBAAGlB,EAAEC;gBAAS,OAAO;oBAAEmB,OAAOF,EAAG;oBAAIC,MAAM;;;cAChD,KAAK;gBAAGnB,EAAEC;gBAASM,IAAIW,EAAG;gBAAIA,IAAK,EAAC;gBAAI;;cACxC,KAAK;gBAAGA,IAAKlB,EAAEK,IAAIgB;gBAAOrB,EAAEI,KAAKiB;gBAAO;;cACxC;gBACI,MAAMlB,IAAIH,EAAEI,MAAMD,IAAIA,EAAEmB,SAAS,KAAKnB,EAAEA,EAAEmB,SAAS,QAAQJ,EAAG,OAAO,KAAKA,EAAG,OAAO,IAAI;oBAAElB,IAAI;oBAAG;AAAU;gBAC3G,IAAIkB,EAAG,OAAO,OAAOf,KAAMe,EAAG,KAAKf,EAAE,MAAMe,EAAG,KAAKf,EAAE,KAAM;oBAAEH,EAAEC,QAAQiB,EAAG;oBAAI;AAAO;gBACrF,IAAIA,EAAG,OAAO,KAAKlB,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIA,IAAIe;oBAAI;AAAO;gBACpE,IAAIf,KAAKH,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIH,EAAEK,IAAIkB,KAAKL;oBAAK;AAAO;gBAClE,IAAIf,EAAE,IAAIH,EAAEK,IAAIgB;gBAChBrB,EAAEI,KAAKiB;gBAAO;;YAEtBH,IAAKnB,EAAKR,KAAKO,GAASE;UAC1B,OAAOwB;YAAKN,IAAK,EAAC,GAAGM;YAAIjB,IAAI;AAAG,UAAC;YAAWD,IAAIH,IAAI;AAAG;QACzD,IAAIe,EAAG,KAAK,GAAG,MAAMA,EAAG;QAAI,OAAO;YAAEE,OAAOF,EAAG,KAAKA,EAAG,UAAU;YAAGC,MAAM;;AAC9E;AACJ;;SCxCSM,eAAeC,6BAAqC;;SAEpDC,gCAA0B;;AAEnC,IAAAC,kBAAA,SAAAC;IAAiClD,UAAAiD,iBAAAC;IAE/B,SAAAD,gBACEE,GACAC,GACAC,GACAC;QAJF,IAAAC,IAMEL,EAAAtC,KAAAX,MAAMkD,GAAMC,GAAQE,MAAarD;QACjCsD,EAAKF,YAAYA;QDoCb,OAAOE;AACX;ICnCFlD,OAAAmD,eAAIP,gBAAAvC,WAAA,WAAO;QDqCL+C,KCrCN;YACE,IAAIxD,KAAKyD,MAAUzD,KAAK0D,GAAS;gBAC/BX;ADsCM;YCpCR,OAAO/C,KAAKyD,EAAME;ADsCd;QACAC,YAAY;QACZC,cAAc;;ICtCpBb,gBAAAvC,UAAAqD,OAAA;QACE,OAAO,IAAId,gBAAmBhD,KAAKyD,GAAOzD,KAAK0D,GAAS1D,KAAKoD,WAAWpD,KAAKqD;ADyC7E;ICrCJ,OAAAL;AAAA,CAtBA,CAAiCF;;AA0BjC,IAAAiB,UAAA,SAAAd;IAAyBlD,UAAAgE,SAAAd;IACvB,SAAAc,QAAYX;QAAA,IAAAA,WAAA,GAAA;YAAAA,IAAA;AAAgC;QAA5C,IAAAE,IACEL,EAAAtC,KAAAX,SAAOA;QACP,IAAMgE,IAAOV;QACbF,EAAUa,SAAQ,SAAUC;YAC1BF,EAAKG,OAAOD;ADuCV;QACA,OAAOZ;AACX;ICtCFS,QAAAtD,UAAA2D,QAAA;QACE,OAAO,IAAIpB,gBAAmBhD,KAAKqE,GAAOrE,KAAK0D,GAAS1D;ADwCxD;ICtCF+D,QAAAtD,UAAA6D,MAAA;QACE,OAAO,IAAItB,gBAAmBhD,KAAK0D,GAAS1D,KAAK0D,GAAS1D;ADwC1D;ICtCF+D,QAAAtD,UAAA8D,SAAA;QACE,OAAO,IAAIvB,gBAAmBhD,KAAKwE,GAAOxE,KAAK0D,GAAS1D,MAAI;ADwC5D;ICtCF+D,QAAAtD,UAAAgE,OAAA;QACE,OAAO,IAAIzB,gBAAmBhD,KAAK0D,GAAS1D,KAAK0D,GAAS1D,MAAI;ADwC9D;ICtCF+D,QAAAtD,UAAAiE,QAAA;QACE,OAAO1E,KAAKqE,EAAMV;ADwClB;ICtCFI,QAAAtD,UAAAkE,OAAA;QACE,OAAO3E,KAAKwE,EAAMb;ADwClB;IC/BFI,QAAAtD,UAAA0D,SAAA,SAAOS,GAAQC;QACb,OAAO7E,KAAK8E,EAAKF,GAAKG,WAAWF;ADwCjC;ICtCFd,QAAAtD,UAAAuE,kBAAA,SAAgBC;QDwCV,ICvCsBA,IAAG,KAAHA,IAAQjF,KAAKkF,IAAO,GA/DI;YAAE,MAAU,IAChEC;ADuGM;QCxCJ,IAAIjC,IAAOlD,KAAKqE;QAChB,OAAOY,KAAO;YACZ/B,IAAOA,EAAKkC;AD0CV;QCxCJ,OAAOlC,EAAKS;AD0CZ;ICjCFI,QAAAtD,UAAA4E,OAAA,SAAKT,GAAQC;QACX,IAAM3B,IAAOlD,KAAKsF,EAAiBV,GAAKC;QACxC,OAAO,IAAI7B,gBAAmBE,GAAMlD,KAAK0D,GAAS1D;AD0ClD;ICxCF+D,QAAAtD,UAAAwD,UAAA,SAAQsB;QACN,IAAIC,IAAQ;QACZ,IAAItC,IAAOlD,KAAKqE;QAChB,OAAOnB,MAASlD,KAAK0D,GAAS;YAC5B6B,EAASrC,EAAKS,GAAM6B,KAASxF;YAC7BkD,IAAOA,EAAKkC;AD0CV;AACJ;ICxCFrB,QAAAtD,UAACwB,OAAOC,YAAR;QACE,OAAO;YD0CC,IAAIgB;YACJ,OAAOjC,YAAYjB,OAAM,SAAUyF;gBAC/B,QAAQA,EAAGpE;kBACP,KAAK;oBC5Cf6B,IAAOlD,KAAKqE;oBD8CEoB,EAAGpE,QAAQ;;kBACf,KAAK;oBACD,MC/CX6B,MAASlD,KAAK0D,IAAO,OAAA,EAAA,GAAA;oBAC1B,OAAA,EAAA,GAAMR,EAAKS;;kBDgDC,KAAK;oBChDjB8B,EAAAnE;oBACA4B,IAAOA,EAAKkC;oBDkDI,OAAO,EAAC,GAAa;;kBACzB,KAAK;oBAAG,OAAO,EAAC;;AAExB;AACJ,UCpDFM,KAAK1F,KANA;AD2DP;ICnDJ,OAAA+D;AAAA,CAxEA,CAAyBlB;;eA0EVkB", "file": "HashSet.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport { HashContainer, HashContainerIterator } from \"./Base\";\nimport $checkWithinAccessParams from \"../../utils/checkParams.macro\";\nimport { throwIteratorAccessError } from \"../../utils/throwError\";\nvar HashSetIterator = /** @class */ (function (_super) {\n    __extends(HashSetIterator, _super);\n    function HashSetIterator(node, header, container, iteratorType) {\n        var _this = _super.call(this, node, header, iteratorType) || this;\n        _this.container = container;\n        return _this;\n    }\n    Object.defineProperty(HashSetIterator.prototype, \"pointer\", {\n        get: function () {\n            if (this._node === this._header) {\n                throwIteratorAccessError();\n            }\n            return this._node._key;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    HashSetIterator.prototype.copy = function () {\n        return new HashSetIterator(this._node, this._header, this.container, this.iteratorType);\n    };\n    return HashSetIterator;\n}(HashContainerIterator));\nvar HashSet = /** @class */ (function (_super) {\n    __extends(HashSet, _super);\n    function HashSet(container) {\n        if (container === void 0) { container = []; }\n        var _this = _super.call(this) || this;\n        var self = _this;\n        container.forEach(function (el) {\n            self.insert(el);\n        });\n        return _this;\n    }\n    HashSet.prototype.begin = function () {\n        return new HashSetIterator(this._head, this._header, this);\n    };\n    HashSet.prototype.end = function () {\n        return new HashSetIterator(this._header, this._header, this);\n    };\n    HashSet.prototype.rBegin = function () {\n        return new HashSetIterator(this._tail, this._header, this, 1 /* IteratorType.REVERSE */);\n    };\n    HashSet.prototype.rEnd = function () {\n        return new HashSetIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    };\n    HashSet.prototype.front = function () {\n        return this._head._key;\n    };\n    HashSet.prototype.back = function () {\n        return this._tail._key;\n    };\n    /**\n     * @description Insert element to set.\n     * @param key - The key want to insert.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @returns The size of container after inserting.\n     */\n    HashSet.prototype.insert = function (key, isObject) {\n        return this._set(key, undefined, isObject);\n    };\n    HashSet.prototype.getElementByPos = function (pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        var node = this._head;\n        while (pos--) {\n            node = node._next;\n        }\n        return node._key;\n    };\n    /**\n     * @description Check key if exist in container.\n     * @param key - The element you want to search.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @returns An iterator pointing to the element if found, or super end if not found.\n     */\n    HashSet.prototype.find = function (key, isObject) {\n        var node = this._findElementNode(key, isObject);\n        return new HashSetIterator(node, this._header, this);\n    };\n    HashSet.prototype.forEach = function (callback) {\n        var index = 0;\n        var node = this._head;\n        while (node !== this._header) {\n            callback(node._key, index++, this);\n            node = node._next;\n        }\n    };\n    HashSet.prototype[Symbol.iterator] = function () {\n        return function () {\n            var node;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        node = this._head;\n                        _a.label = 1;\n                    case 1:\n                        if (!(node !== this._header)) return [3 /*break*/, 3];\n                        return [4 /*yield*/, node._key];\n                    case 2:\n                        _a.sent();\n                        node = node._next;\n                        return [3 /*break*/, 1];\n                    case 3: return [2 /*return*/];\n                }\n            });\n        }.bind(this)();\n    };\n    return HashSet;\n}(HashContainer));\nexport default HashSet;\n", "import { initContainer, IteratorType } from '@/container/ContainerBase';\nimport { Hash<PERSON>ontainer, HashContainerIterator, HashLinkNode } from '@/container/HashContainer/Base';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nclass HashSetIterator<K> extends HashContainerIterator<K, undefined> {\n  readonly container: HashSet<K>;\n  constructor(\n    node: HashLinkNode<K, undefined>,\n    header: HashLinkNode<K, undefined>,\n    container: HashSet<K>,\n    iteratorType?: IteratorType\n  ) {\n    super(node, header, iteratorType);\n    this.container = container;\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    return this._node._key;\n  }\n  copy() {\n    return new HashSetIterator<K>(this._node, this._header, this.container, this.iteratorType);\n  }\n  // @ts-ignore\n  equals(iter: HashSetIterator<K>): boolean;\n}\n\nexport type { HashSetIterator };\n\nclass HashSet<K> extends HashContainer<K, undefined> {\n  constructor(container: initContainer<K> = []) {\n    super();\n    const self = this;\n    container.forEach(function (el) {\n      self.insert(el);\n    });\n  }\n  begin() {\n    return new HashSetIterator<K>(this._head, this._header, this);\n  }\n  end() {\n    return new HashSetIterator<K>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new HashSetIterator<K>(this._tail, this._header, this, IteratorType.REVERSE);\n  }\n  rEnd() {\n    return new HashSetIterator<K>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front(): K | undefined {\n    return this._head._key;\n  }\n  back(): K | undefined {\n    return this._tail._key;\n  }\n  /**\n   * @description Insert element to set.\n   * @param key - The key want to insert.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @returns The size of container after inserting.\n   */\n  insert(key: K, isObject?: boolean) {\n    return this._set(key, undefined, isObject);\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let node = this._head;\n    while (pos--) {\n      node = node._next;\n    }\n    return node._key;\n  }\n  /**\n   * @description Check key if exist in container.\n   * @param key - The element you want to search.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @returns An iterator pointing to the element if found, or super end if not found.\n   */\n  find(key: K, isObject?: boolean) {\n    const node = this._findElementNode(key, isObject);\n    return new HashSetIterator<K>(node, this._header, this);\n  }\n  forEach(callback: (element: K, index: number, container: HashSet<K>) => void) {\n    let index = 0;\n    let node = this._head;\n    while (node !== this._header) {\n      callback(node._key, index++, this);\n      node = node._next;\n    }\n  }\n  [Symbol.iterator]() {\n    return function * (this: HashSet<K>) {\n      let node = this._head;\n      while (node !== this._header) {\n        yield node._key;\n        node = node._next;\n      }\n    }.bind(this)();\n  }\n}\n\nexport default HashSet;\n"]}