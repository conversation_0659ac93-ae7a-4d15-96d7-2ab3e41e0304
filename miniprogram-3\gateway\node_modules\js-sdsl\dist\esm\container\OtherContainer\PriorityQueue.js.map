{"version": 3, "sources": ["container/OtherContainer/PriorityQueue.js", "../../src/container/OtherContainer/PriorityQueue.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "__read", "o", "n", "m", "Symbol", "iterator", "i", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "l", "slice", "concat", "Base", "PriorityQueue", "_super", "container", "cmp", "copy", "x", "y", "_this", "_cmp", "isArray", "_priorityQueue", "self_1", "for<PERSON>ach", "el", "_length", "<PERSON><PERSON><PERSON><PERSON>", "parent_1", "_pushDown", "_pushUp", "pos", "item", "parent_2", "parentItem", "left", "right", "minItem", "clear", "pop", "last", "top", "find", "indexOf", "remove", "index", "splice", "updateItem", "toArray"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;AAe7C,IAAIG,SAAUjB,QAAQA,KAAKiB,KAAW,SAAUC,GAAGC;IAC/C,IAAIC,WAAWC,WAAW,cAAcH,EAAEG,OAAOC;IACjD,KAAKF,GAAG,OAAOF;IACf,IAAIK,IAAIH,EAAET,KAAKO,IAAIM,GAAGC,IAAK,IAAIC;IAC/B;QACI,QAAQP,WAAW,KAAKA,MAAM,QAAQK,IAAID,EAAEI,QAAQC,MAAMH,EAAGI,KAAKL,EAAEM;AAQxE,MANA,OAAOC;QAASL,IAAI;YAAEK,OAAOA;;AAAS,MAAC;QAEnC;YACI,IAAIP,MAAMA,EAAEI,SAASR,IAAIG,EAAE,YAAYH,EAAET,KAAKY;AAElB,UAD/B;YACS,IAAIG,GAAG,MAAMA,EAAEK;AAAO;AACpC;IACA,OAAON;AACX;;AACA,IAAIO,gBAAiBhC,QAAQA,KAAKgC,KAAkB,SAAUC,GAAIC,GAAMC;IACpE,IAAIA,KAAQC,UAAUC,WAAW,GAAG,KAAK,IAAId,IAAI,GAAGe,IAAIJ,EAAKG,QAAQZ,GAAIF,IAAIe,GAAGf,KAAK;QACjF,IAAIE,OAAQF,KAAKW,IAAO;YACpB,KAAKT,GAAIA,IAAKlB,MAAME,UAAU8B,MAAM5B,KAAKuB,GAAM,GAAGX;YAClDE,EAAGF,KAAKW,EAAKX;AACjB;AACJ;IACA,OAAOU,EAAGO,OAAOf,KAAMlB,MAAME,UAAU8B,MAAM5B,KAAKuB;AACtD;;SCvCSO,YAAqB;;AAE9B,IAAAC,gBAAA,SAAAC;IAA+B5C,UAAA2C,eAAAC;IAqB7B,SAAAD,cACEE,GACAC,GAMAC;QAPA,IAAAF,WAAA,GAAA;YAAAA,IAAA;AAAgC;QAChC,IAAAC,WAAA,GAAA;YAAAA,IAAA,SACUE,GAAMC;gBACd,IAAID,IAAIC,GAAG,QAAQ;gBACnB,IAAID,IAAIC,GAAG,OAAO;gBAClB,OAAO;ADkCL;ACjCH;QACD,IAAAF,WAAA,GAAA;YAAAA,IAAA;AAAW;QARb,IAAAG,IAUEN,EAAAhC,KAAAX,SAAOA;QACPiD,EAAKC,IAAOL;QACZ,IAAItC,MAAM4C,QAAQP,IAAY;YAC5BK,EAAKG,KAAiBN,IAAMd,cAAA,IAAAf,OAAK2B,IAAS,SAAIA;ADiC5C,eChCG;YACLK,EAAKG,KAAiB;YACtB,IAAMC,IAAOJ;YACbL,EAAUU,SAAQ,SAAUC;gBAC1BF,EAAKD,GAAevB,KAAK0B;ADkCrB;AACJ;QChCJN,EAAKO,IAAUP,EAAKG,GAAef;QACnC,IAAMoB,IAAaR,EAAKO,KAAW;QACnC,KAAK,IAAIE,IAAUT,EAAKO,IAAU,KAAM,GAAGE,KAAU,KAAKA,GAAQ;YAChET,EAAKU,GAAUD,GAAQD;ADkCrB;QACA,OAAOR;AACX;IC9BMP,cAAAjC,UAAAmD,KAAR,SAAgBC;QACd,IAAMC,IAAO9D,KAAKoD,GAAeS;QACjC,OAAOA,IAAM,GAAG;YACd,IAAME,IAAUF,IAAM,KAAM;YAC5B,IAAMG,IAAahE,KAAKoD,GAAeW;YACvC,IAAI/D,KAAKkD,EAAKc,GAAYF,MAAS,GAAG;YACtC9D,KAAKoD,GAAeS,KAAOG;YAC3BH,IAAME;ADoCJ;QClCJ/D,KAAKoD,GAAeS,KAAOC;ADoC3B;IC/BMpB,cAAAjC,UAAAkD,KAAR,SAAkBE,GAAaJ;QAC7B,IAAMK,IAAO9D,KAAKoD,GAAeS;QACjC,OAAOA,IAAMJ,GAAY;YACvB,IAAIQ,IAAOJ,KAAO,IAAI;YACtB,IAAMK,IAAQD,IAAO;YACrB,IAAIE,IAAUnE,KAAKoD,GAAea;YAClC,IACEC,IAAQlE,KAAKwD,KACbxD,KAAKkD,EAAKiB,GAASnE,KAAKoD,GAAec,MAAU,GACjD;gBACAD,IAAOC;gBACPC,IAAUnE,KAAKoD,GAAec;ADkC1B;YChCN,IAAIlE,KAAKkD,EAAKiB,GAASL,MAAS,GAAG;YACnC9D,KAAKoD,GAAeS,KAAOM;YAC3BN,IAAMI;ADmCJ;QCjCJjE,KAAKoD,GAAeS,KAAOC;ADmC3B;ICjCFpB,cAAAjC,UAAA2D,QAAA;QACEpE,KAAKwD,IAAU;QACfxD,KAAKoD,GAAef,SAAS;ADmC7B;IC1BFK,cAAAjC,UAAAoB,OAAA,SAAKiC;QACH9D,KAAKoD,GAAevB,KAAKiC;QACzB9D,KAAK4D,GAAQ5D,KAAKwD;QAClBxD,KAAKwD,KAAW;ADmChB;IC3BFd,cAAAjC,UAAA4D,MAAA;QACE,IAAIrE,KAAKwD,MAAY,GAAG;QACxB,IAAM1B,IAAQ9B,KAAKoD,GAAe;QAClC,IAAMkB,IAAOtE,KAAKoD,GAAeiB;QACjCrE,KAAKwD,KAAW;QAChB,IAAIxD,KAAKwD,GAAS;YAChBxD,KAAKoD,GAAe,KAAKkB;YACzBtE,KAAK2D,GAAU,GAAG3D,KAAKwD,KAAW;ADoChC;QClCJ,OAAO1B;ADoCP;IC7BFY,cAAAjC,UAAA8D,MAAA;QACE,OAAOvE,KAAKoD,GAAe;ADoC3B;ICxBFV,cAAAjC,UAAA+D,OAAA,SAAKV;QACH,OAAO9D,KAAKoD,GAAeqB,QAAQX,MAAS;ADoC5C;ICxBFpB,cAAAjC,UAAAiE,SAAA,SAAOZ;QACL,IAAMa,IAAQ3E,KAAKoD,GAAeqB,QAAQX;QAC1C,IAAIa,IAAQ,GAAG,OAAO;QACtB,IAAIA,MAAU,GAAG;YACf3E,KAAKqE;ADqCH,eCpCG,IAAIM,MAAU3E,KAAKwD,IAAU,GAAG;YACrCxD,KAAKoD,GAAeiB;YACpBrE,KAAKwD,KAAW;ADsCd,eCrCG;YACLxD,KAAKoD,GAAewB,OAAOD,GAAO,GAAG3E,KAAKoD,GAAeiB;YACzDrE,KAAKwD,KAAW;YAChBxD,KAAK4D,GAAQe;YACb3E,KAAK2D,GAAUgB,GAAO3E,KAAKwD,KAAW;ADuCpC;QCrCJ,OAAO;ADuCP;IC1BFd,cAAAjC,UAAAoE,aAAA,SAAWf;QACT,IAAMa,IAAQ3E,KAAKoD,GAAeqB,QAAQX;QAC1C,IAAIa,IAAQ,GAAG,OAAO;QACtB3E,KAAK4D,GAAQe;QACb3E,KAAK2D,GAAUgB,GAAO3E,KAAKwD,KAAW;QACtC,OAAO;ADwCP;ICjCFd,cAAAjC,UAAAqE,UAAA;QACE,OAAA9C,cAAA,IAAAf,OAAWjB,KAAKoD,KAAc;ADwC9B;ICtCJ,OAAAV;AAAA,CA9LA,CAA+BD;;eAgMhBC", "file": "PriorityQueue.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { Base } from \"../ContainerBase\";\nvar PriorityQueue = /** @class */ (function (_super) {\n    __extends(PriorityQueue, _super);\n    /**\n     * @description PriorityQueue's constructor.\n     * @param container - Initialize container, must have a forEach function.\n     * @param cmp - Compare function.\n     * @param copy - When the container is an array, you can choose to directly operate on the original object of\n     *               the array or perform a shallow copy. The default is shallow copy.\n     * @example\n     * new PriorityQueue();\n     * new PriorityQueue([1, 2, 3]);\n     * new PriorityQueue([1, 2, 3], (x, y) => x - y);\n     * new PriorityQueue([1, 2, 3], (x, y) => x - y, false);\n     */\n    function PriorityQueue(container, cmp, copy) {\n        if (container === void 0) { container = []; }\n        if (cmp === void 0) { cmp = function (x, y) {\n            if (x > y)\n                return -1;\n            if (x < y)\n                return 1;\n            return 0;\n        }; }\n        if (copy === void 0) { copy = true; }\n        var _this = _super.call(this) || this;\n        _this._cmp = cmp;\n        if (Array.isArray(container)) {\n            _this._priorityQueue = copy ? __spreadArray([], __read(container), false) : container;\n        }\n        else {\n            _this._priorityQueue = [];\n            var self_1 = _this;\n            container.forEach(function (el) {\n                self_1._priorityQueue.push(el);\n            });\n        }\n        _this._length = _this._priorityQueue.length;\n        var halfLength = _this._length >> 1;\n        for (var parent_1 = (_this._length - 1) >> 1; parent_1 >= 0; --parent_1) {\n            _this._pushDown(parent_1, halfLength);\n        }\n        return _this;\n    }\n    /**\n     * @internal\n     */\n    PriorityQueue.prototype._pushUp = function (pos) {\n        var item = this._priorityQueue[pos];\n        while (pos > 0) {\n            var parent_2 = (pos - 1) >> 1;\n            var parentItem = this._priorityQueue[parent_2];\n            if (this._cmp(parentItem, item) <= 0)\n                break;\n            this._priorityQueue[pos] = parentItem;\n            pos = parent_2;\n        }\n        this._priorityQueue[pos] = item;\n    };\n    /**\n     * @internal\n     */\n    PriorityQueue.prototype._pushDown = function (pos, halfLength) {\n        var item = this._priorityQueue[pos];\n        while (pos < halfLength) {\n            var left = pos << 1 | 1;\n            var right = left + 1;\n            var minItem = this._priorityQueue[left];\n            if (right < this._length &&\n                this._cmp(minItem, this._priorityQueue[right]) > 0) {\n                left = right;\n                minItem = this._priorityQueue[right];\n            }\n            if (this._cmp(minItem, item) >= 0)\n                break;\n            this._priorityQueue[pos] = minItem;\n            pos = left;\n        }\n        this._priorityQueue[pos] = item;\n    };\n    PriorityQueue.prototype.clear = function () {\n        this._length = 0;\n        this._priorityQueue.length = 0;\n    };\n    /**\n     * @description Push element into a container in order.\n     * @param item - The element you want to push.\n     * @returns The size of heap after pushing.\n     * @example\n     * queue.push(1);\n     */\n    PriorityQueue.prototype.push = function (item) {\n        this._priorityQueue.push(item);\n        this._pushUp(this._length);\n        this._length += 1;\n    };\n    /**\n     * @description Removes the top element.\n     * @returns The element you popped.\n     * @example\n     * queue.pop();\n     */\n    PriorityQueue.prototype.pop = function () {\n        if (this._length === 0)\n            return;\n        var value = this._priorityQueue[0];\n        var last = this._priorityQueue.pop();\n        this._length -= 1;\n        if (this._length) {\n            this._priorityQueue[0] = last;\n            this._pushDown(0, this._length >> 1);\n        }\n        return value;\n    };\n    /**\n     * @description Accesses the top element.\n     * @example\n     * const top = queue.top();\n     */\n    PriorityQueue.prototype.top = function () {\n        return this._priorityQueue[0];\n    };\n    /**\n     * @description Check if element is in heap.\n     * @param item - The item want to find.\n     * @returns Whether element is in heap.\n     * @example\n     * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n     * const obj = { id: 1 };\n     * que.push(obj);\n     * console.log(que.find(obj));  // true\n     */\n    PriorityQueue.prototype.find = function (item) {\n        return this._priorityQueue.indexOf(item) >= 0;\n    };\n    /**\n     * @description Remove specified item from heap.\n     * @param item - The item want to remove.\n     * @returns Whether remove success.\n     * @example\n     * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n     * const obj = { id: 1 };\n     * que.push(obj);\n     * que.remove(obj);\n     */\n    PriorityQueue.prototype.remove = function (item) {\n        var index = this._priorityQueue.indexOf(item);\n        if (index < 0)\n            return false;\n        if (index === 0) {\n            this.pop();\n        }\n        else if (index === this._length - 1) {\n            this._priorityQueue.pop();\n            this._length -= 1;\n        }\n        else {\n            this._priorityQueue.splice(index, 1, this._priorityQueue.pop());\n            this._length -= 1;\n            this._pushUp(index);\n            this._pushDown(index, this._length >> 1);\n        }\n        return true;\n    };\n    /**\n     * @description Update item and it's pos in the heap.\n     * @param item - The item want to update.\n     * @returns Whether update success.\n     * @example\n     * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n     * const obj = { id: 1 };\n     * que.push(obj);\n     * obj.id = 2;\n     * que.updateItem(obj);\n     */\n    PriorityQueue.prototype.updateItem = function (item) {\n        var index = this._priorityQueue.indexOf(item);\n        if (index < 0)\n            return false;\n        this._pushUp(index);\n        this._pushDown(index, this._length >> 1);\n        return true;\n    };\n    /**\n     * @returns Return a copy array of heap.\n     * @example\n     * const arr = queue.toArray();\n     */\n    PriorityQueue.prototype.toArray = function () {\n        return __spreadArray([], __read(this._priorityQueue), false);\n    };\n    return PriorityQueue;\n}(Base));\nexport default PriorityQueue;\n", "import { Base, initContainer } from '@/container/ContainerBase';\n\nclass PriorityQueue<T> extends Base {\n  /**\n   * @internal\n   */\n  private readonly _priorityQueue: T[];\n  /**\n   * @internal\n   */\n  private readonly _cmp: (x: T, y: T) => number;\n  /**\n   * @description PriorityQueue's constructor.\n   * @param container - Initialize container, must have a forEach function.\n   * @param cmp - Compare function.\n   * @param copy - When the container is an array, you can choose to directly operate on the original object of\n   *               the array or perform a shallow copy. The default is shallow copy.\n   * @example\n   * new PriorityQueue();\n   * new PriorityQueue([1, 2, 3]);\n   * new PriorityQueue([1, 2, 3], (x, y) => x - y);\n   * new PriorityQueue([1, 2, 3], (x, y) => x - y, false);\n   */\n  constructor(\n    container: initContainer<T> = [],\n    cmp: (x: T, y: T) => number =\n    function (x: T, y: T) {\n      if (x > y) return -1;\n      if (x < y) return 1;\n      return 0;\n    },\n    copy = true\n  ) {\n    super();\n    this._cmp = cmp;\n    if (Array.isArray(container)) {\n      this._priorityQueue = copy ? [...container] : container;\n    } else {\n      this._priorityQueue = [];\n      const self = this;\n      container.forEach(function (el) {\n        self._priorityQueue.push(el);\n      });\n    }\n    this._length = this._priorityQueue.length;\n    const halfLength = this._length >> 1;\n    for (let parent = (this._length - 1) >> 1; parent >= 0; --parent) {\n      this._pushDown(parent, halfLength);\n    }\n  }\n  /**\n   * @internal\n   */\n  private _pushUp(pos: number) {\n    const item = this._priorityQueue[pos];\n    while (pos > 0) {\n      const parent = (pos - 1) >> 1;\n      const parentItem = this._priorityQueue[parent];\n      if (this._cmp(parentItem, item) <= 0) break;\n      this._priorityQueue[pos] = parentItem;\n      pos = parent;\n    }\n    this._priorityQueue[pos] = item;\n  }\n  /**\n   * @internal\n   */\n  private _pushDown(pos: number, halfLength: number) {\n    const item = this._priorityQueue[pos];\n    while (pos < halfLength) {\n      let left = pos << 1 | 1;\n      const right = left + 1;\n      let minItem = this._priorityQueue[left];\n      if (\n        right < this._length &&\n        this._cmp(minItem, this._priorityQueue[right]) > 0\n      ) {\n        left = right;\n        minItem = this._priorityQueue[right];\n      }\n      if (this._cmp(minItem, item) >= 0) break;\n      this._priorityQueue[pos] = minItem;\n      pos = left;\n    }\n    this._priorityQueue[pos] = item;\n  }\n  clear() {\n    this._length = 0;\n    this._priorityQueue.length = 0;\n  }\n  /**\n   * @description Push element into a container in order.\n   * @param item - The element you want to push.\n   * @returns The size of heap after pushing.\n   * @example\n   * queue.push(1);\n   */\n  push(item: T) {\n    this._priorityQueue.push(item);\n    this._pushUp(this._length);\n    this._length += 1;\n  }\n  /**\n   * @description Removes the top element.\n   * @returns The element you popped.\n   * @example\n   * queue.pop();\n   */\n  pop() {\n    if (this._length === 0) return;\n    const value = this._priorityQueue[0];\n    const last = this._priorityQueue.pop()!;\n    this._length -= 1;\n    if (this._length) {\n      this._priorityQueue[0] = last;\n      this._pushDown(0, this._length >> 1);\n    }\n    return value;\n  }\n  /**\n   * @description Accesses the top element.\n   * @example\n   * const top = queue.top();\n   */\n  top(): T | undefined {\n    return this._priorityQueue[0];\n  }\n  /**\n   * @description Check if element is in heap.\n   * @param item - The item want to find.\n   * @returns Whether element is in heap.\n   * @example\n   * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n   * const obj = { id: 1 };\n   * que.push(obj);\n   * console.log(que.find(obj));  // true\n   */\n  find(item: T) {\n    return this._priorityQueue.indexOf(item) >= 0;\n  }\n  /**\n   * @description Remove specified item from heap.\n   * @param item - The item want to remove.\n   * @returns Whether remove success.\n   * @example\n   * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n   * const obj = { id: 1 };\n   * que.push(obj);\n   * que.remove(obj);\n   */\n  remove(item: T) {\n    const index = this._priorityQueue.indexOf(item);\n    if (index < 0) return false;\n    if (index === 0) {\n      this.pop();\n    } else if (index === this._length - 1) {\n      this._priorityQueue.pop();\n      this._length -= 1;\n    } else {\n      this._priorityQueue.splice(index, 1, this._priorityQueue.pop()!);\n      this._length -= 1;\n      this._pushUp(index);\n      this._pushDown(index, this._length >> 1);\n    }\n    return true;\n  }\n  /**\n   * @description Update item and it's pos in the heap.\n   * @param item - The item want to update.\n   * @returns Whether update success.\n   * @example\n   * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n   * const obj = { id: 1 };\n   * que.push(obj);\n   * obj.id = 2;\n   * que.updateItem(obj);\n   */\n  updateItem(item: T) {\n    const index = this._priorityQueue.indexOf(item);\n    if (index < 0) return false;\n    this._pushUp(index);\n    this._pushDown(index, this._length >> 1);\n    return true;\n  }\n  /**\n   * @returns Return a copy array of heap.\n   * @example\n   * const arr = queue.toArray();\n   */\n  toArray() {\n    return [...this._priorityQueue];\n  }\n}\n\nexport default PriorityQueue;\n"]}