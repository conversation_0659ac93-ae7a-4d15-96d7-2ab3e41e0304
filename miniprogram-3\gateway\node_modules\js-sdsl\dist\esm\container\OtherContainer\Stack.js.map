{"version": 3, "sources": ["container/OtherContainer/Stack.js", "../../src/container/OtherContainer/Stack.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "Base", "<PERSON><PERSON>", "_super", "container", "_this", "_stack", "self", "for<PERSON>ach", "el", "push", "clear", "_length", "element", "pop", "top"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;SCApCG,YAAqB;;AAE9B,IAAAC,QAAA,SAAAC;IAAuBpB,UAAAmB,OAAAC;IAKrB,SAAAD,MAAYE;QAAA,IAAAA,WAAA,GAAA;YAAAA,IAAA;AAAgC;QAA5C,IAAAC,IACEF,EAAAR,KAAAX,SAAOA;QAFDqB,EAAAC,KAAc;QAGpB,IAAMC,IAAOF;QACbD,EAAUI,SAAQ,SAAUC;YAC1BF,EAAKG,KAAKD;ADiBR;QACA,OAAOJ;AACX;IChBFH,MAAAT,UAAAkB,QAAA;QACE3B,KAAK4B,IAAU;QACf5B,KAAKsB,KAAS;ADkBd;ICXFJ,MAAAT,UAAAiB,OAAA,SAAKG;QACH7B,KAAKsB,GAAOI,KAAKG;QACjB7B,KAAK4B,KAAW;QAChB,OAAO5B,KAAK4B;ADkBZ;ICZFV,MAAAT,UAAAqB,MAAA;QACE,IAAI9B,KAAK4B,MAAY,GAAG;QACxB5B,KAAK4B,KAAW;QAChB,OAAO5B,KAAKsB,GAAOQ;ADmBnB;ICbFZ,MAAAT,UAAAsB,MAAA;QACE,OAAO/B,KAAKsB,GAAOtB,KAAK4B,IAAU;ADmBlC;ICjBJ,OAAAV;AAAA,CA1CA,CAAuBD;;eA4CRC", "file": "Stack.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { Base } from \"../ContainerBase\";\nvar Stack = /** @class */ (function (_super) {\n    __extends(Stack, _super);\n    function Stack(container) {\n        if (container === void 0) { container = []; }\n        var _this = _super.call(this) || this;\n        /**\n         * @internal\n         */\n        _this._stack = [];\n        var self = _this;\n        container.forEach(function (el) {\n            self.push(el);\n        });\n        return _this;\n    }\n    Stack.prototype.clear = function () {\n        this._length = 0;\n        this._stack = [];\n    };\n    /**\n     * @description Insert element to stack's end.\n     * @description The element you want to push to the back.\n     * @returns The container length after erasing.\n     */\n    Stack.prototype.push = function (element) {\n        this._stack.push(element);\n        this._length += 1;\n        return this._length;\n    };\n    /**\n     * @description Removes the end element.\n     * @returns The element you popped.\n     */\n    Stack.prototype.pop = function () {\n        if (this._length === 0)\n            return;\n        this._length -= 1;\n        return this._stack.pop();\n    };\n    /**\n     * @description Accesses the end element.\n     * @returns The last element.\n     */\n    Stack.prototype.top = function () {\n        return this._stack[this._length - 1];\n    };\n    return Stack;\n}(Base));\nexport default Stack;\n", "import { Base, initContainer } from '@/container/ContainerBase';\n\nclass Stack<T> extends Base {\n  /**\n   * @internal\n   */\n  private _stack: T[] = [];\n  constructor(container: initContainer<T> = []) {\n    super();\n    const self = this;\n    container.forEach(function (el) {\n      self.push(el);\n    });\n  }\n  clear() {\n    this._length = 0;\n    this._stack = [];\n  }\n  /**\n   * @description Insert element to stack's end.\n   * @description The element you want to push to the back.\n   * @returns The container length after erasing.\n   */\n  push(element: T) {\n    this._stack.push(element);\n    this._length += 1;\n    return this._length;\n  }\n  /**\n   * @description Removes the end element.\n   * @returns The element you popped.\n   */\n  pop() {\n    if (this._length === 0) return;\n    this._length -= 1;\n    return this._stack.pop();\n  }\n  /**\n   * @description Accesses the end element.\n   * @returns The last element.\n   */\n  top(): T | undefined {\n    return this._stack[this._length - 1];\n  }\n}\n\nexport default Stack;\n"]}