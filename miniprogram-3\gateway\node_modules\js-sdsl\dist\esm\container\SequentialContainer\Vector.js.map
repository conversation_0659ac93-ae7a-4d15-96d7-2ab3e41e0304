{"version": 3, "sources": ["container/SequentialContainer/Vector.js", "../../src/container/SequentialContainer/Vector.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "__generator", "thisArg", "body", "_", "label", "sent", "t", "trys", "ops", "f", "y", "g", "next", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "step", "op", "done", "value", "pop", "length", "push", "e", "__read", "o", "m", "i", "r", "ar", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "l", "slice", "concat", "__values", "s", "SequentialContainer", "RandomIterator", "VectorIterator", "_super", "node", "container", "iteratorType", "_this", "copy", "_node", "Vector", "isArray", "_vector", "_length", "self_1", "for<PERSON>ach", "el", "pushBack", "clear", "begin", "end", "rBegin", "rEnd", "front", "back", "getElementByPos", "pos", "RangeError", "eraseElementByPos", "splice", "eraseElementByValue", "index", "eraseElementByIterator", "iter", "element", "popBack", "setElementByPos", "insert", "num", "_a", "apply", "fill", "find", "reverse", "unique", "sort", "cmp", "callback", "bind"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;AAe7C,IAAIG,cAAejB,QAAQA,KAAKiB,KAAgB,SAAUC,GAASC;IAC/D,IAAIC,IAAI;QAAEC,OAAO;QAAGC,MAAM;YAAa,IAAIC,EAAE,KAAK,GAAG,MAAMA,EAAE;YAAI,OAAOA,EAAE;AAAI;QAAGC,MAAM;QAAIC,KAAK;OAAMC,GAAGC,GAAGJ,GAAGK;IAC/G,OAAOA,IAAI;QAAEC,MAAMC,KAAK;QAAIC,OAASD,KAAK;QAAIE,QAAUF,KAAK;cAAaG,WAAW,eAAeL,EAAEK,OAAOC,YAAY;QAAa,OAAOlC;AAAM,QAAI4B;IACvJ,SAASE,KAAKK;QAAK,OAAO,SAAUC;YAAK,OAAOC,KAAK,EAACF,GAAGC;AAAK;AAAG;IACjE,SAASC,KAAKC;QACV,IAAIZ,GAAG,MAAM,IAAId,UAAU;QAC3B,OAAOQ;YACH,IAAIM,IAAI,GAAGC,MAAMJ,IAAIe,EAAG,KAAK,IAAIX,EAAE,YAAYW,EAAG,KAAKX,EAAE,cAAcJ,IAAII,EAAE,cAAcJ,EAAEZ,KAAKgB;YAAI,KAAKA,EAAEE,WAAWN,IAAIA,EAAEZ,KAAKgB,GAAGW,EAAG,KAAKC,MAAM,OAAOhB;YAC3J,IAAII,IAAI,GAAGJ,GAAGe,IAAK,EAACA,EAAG,KAAK,GAAGf,EAAEiB;YACjC,QAAQF,EAAG;cACP,KAAK;cAAG,KAAK;gBAAGf,IAAIe;gBAAI;;cACxB,KAAK;gBAAGlB,EAAEC;gBAAS,OAAO;oBAAEmB,OAAOF,EAAG;oBAAIC,MAAM;;;cAChD,KAAK;gBAAGnB,EAAEC;gBAASM,IAAIW,EAAG;gBAAIA,IAAK,EAAC;gBAAI;;cACxC,KAAK;gBAAGA,IAAKlB,EAAEK,IAAIgB;gBAAOrB,EAAEI,KAAKiB;gBAAO;;cACxC;gBACI,MAAMlB,IAAIH,EAAEI,MAAMD,IAAIA,EAAEmB,SAAS,KAAKnB,EAAEA,EAAEmB,SAAS,QAAQJ,EAAG,OAAO,KAAKA,EAAG,OAAO,IAAI;oBAAElB,IAAI;oBAAG;AAAU;gBAC3G,IAAIkB,EAAG,OAAO,OAAOf,KAAMe,EAAG,KAAKf,EAAE,MAAMe,EAAG,KAAKf,EAAE,KAAM;oBAAEH,EAAEC,QAAQiB,EAAG;oBAAI;AAAO;gBACrF,IAAIA,EAAG,OAAO,KAAKlB,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIA,IAAIe;oBAAI;AAAO;gBACpE,IAAIf,KAAKH,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIH,EAAEK,IAAIkB,KAAKL;oBAAK;AAAO;gBAClE,IAAIf,EAAE,IAAIH,EAAEK,IAAIgB;gBAChBrB,EAAEI,KAAKiB;gBAAO;;YAEtBH,IAAKnB,EAAKR,KAAKO,GAASE;UAC1B,OAAOwB;YAAKN,IAAK,EAAC,GAAGM;YAAIjB,IAAI;AAAG,UAAC;YAAWD,IAAIH,IAAI;AAAG;QACzD,IAAIe,EAAG,KAAK,GAAG,MAAMA,EAAG;QAAI,OAAO;YAAEE,OAAOF,EAAG,KAAKA,EAAG,UAAU;YAAGC,MAAM;;AAC9E;AACJ;;AACA,IAAIM,SAAU7C,QAAQA,KAAK6C,KAAW,SAAUC,GAAGX;IAC/C,IAAIY,WAAWd,WAAW,cAAca,EAAEb,OAAOC;IACjD,KAAKa,GAAG,OAAOD;IACf,IAAIE,IAAID,EAAEpC,KAAKmC,IAAIG,GAAGC,IAAK,IAAIN;IAC/B;QACI,QAAQT,WAAW,KAAKA,MAAM,QAAQc,IAAID,EAAEnB,QAAQU,MAAMW,EAAGP,KAAKM,EAAET;AAQxE,MANA,OAAOW;QAASP,IAAI;YAAEO,OAAOA;;AAAS,MAAC;QAEnC;YACI,IAAIF,MAAMA,EAAEV,SAASQ,IAAIC,EAAE,YAAYD,EAAEpC,KAAKqC;AAElB,UAD/B;YACS,IAAIJ,GAAG,MAAMA,EAAEO;AAAO;AACpC;IACA,OAAOD;AACX;;AACA,IAAIE,gBAAiBpD,QAAQA,KAAKoD,KAAkB,SAAUC,GAAIC,GAAMC;IACpE,IAAIA,KAAQC,UAAUd,WAAW,GAAG,KAAK,IAAIM,IAAI,GAAGS,IAAIH,EAAKZ,QAAQQ,GAAIF,IAAIS,GAAGT,KAAK;QACjF,IAAIE,OAAQF,KAAKM,IAAO;YACpB,KAAKJ,GAAIA,IAAK3C,MAAME,UAAUiD,MAAM/C,KAAK2C,GAAM,GAAGN;YAClDE,EAAGF,KAAKM,EAAKN;AACjB;AACJ;IACA,OAAOK,EAAGM,OAAOT,KAAM3C,MAAME,UAAUiD,MAAM/C,KAAK2C;AACtD;;AACA,IAAIM,WAAY5D,QAAQA,KAAK4D,KAAa,SAASd;IAC/C,IAAIe,WAAW5B,WAAW,cAAcA,OAAOC,UAAUa,IAAIc,KAAKf,EAAEe,IAAIb,IAAI;IAC5E,IAAID,GAAG,OAAOA,EAAEpC,KAAKmC;IACrB,IAAIA,YAAYA,EAAEJ,WAAW,UAAU,OAAO;QAC1Cb,MAAM;YACF,IAAIiB,KAAKE,KAAKF,EAAEJ,QAAQI,SAAS;YACjC,OAAO;gBAAEN,OAAOM,KAAKA,EAAEE;gBAAMT,OAAOO;;AACxC;;IAEJ,MAAM,IAAIlC,UAAUiD,IAAI,4BAA4B;AACxD;;OC7EOC,yBAAyB;;SAEvBC,sBAAgB;;AAGzB,IAAAC,iBAAA,SAAAC;IAAgClE,UAAAiE,gBAAAC;IAE9B,SAAAD,eAAYE,GAAcC,GAAsBC;QAAhD,IAAAC,IACEJ,EAAAtD,KAAAX,MAAMkE,GAAME,MAAapE;QACzBqE,EAAKF,YAAYA;QD6Eb,OAAOE;AACX;IC5EFL,eAAAvD,UAAA6D,OAAA;QACE,OAAO,IAAIN,eAAkBhE,KAAKuE,GAAOvE,KAAKmE,WAAWnE,KAAKoE;AD8E9D;IC1EJ,OAAAJ;AAAA,CAXA,CAAgCD;;AAehC,IAAAS,SAAA,SAAAP;IAAwBlE,UAAAyE,QAAAP;IAUtB,SAAAO,OAAYL,GAAkCG;QAAlC,IAAAH,WAAA,GAAA;YAAAA,IAAA;AAAgC;QAAE,IAAAG,WAAA,GAAA;YAAAA,IAAA;AAAW;QAAzD,IAAAD,IACEJ,EAAAtD,KAAAX,SAAOA;QACP,IAAIO,MAAMkE,QAAQN,IAAY;YAC5BE,EAAKK,IAAUJ,IAAMlB,cAAA,IAAAP,OAAKsB,IAAS,SAAIA;YACvCE,EAAKM,IAAUR,EAAUzB;ADyEvB,eCxEG;YACL2B,EAAKK,IAAU;YACf,IAAME,IAAOP;YACbF,EAAUU,SAAQ,SAAUC;gBAC1BF,EAAKG,SAASD;AD0EV;AACJ;QACA,OAAOT;AACX;ICzEFG,OAAA/D,UAAAuE,QAAA;QACEhF,KAAK2E,IAAU;QACf3E,KAAK0E,EAAQhC,SAAS;AD2EtB;ICzEF8B,OAAA/D,UAAAwE,QAAA;QACE,OAAO,IAAIjB,eAAkB,GAAGhE;AD2EhC;ICzEFwE,OAAA/D,UAAAyE,MAAA;QACE,OAAO,IAAIlB,eAAkBhE,KAAK2E,GAAS3E;AD2E3C;ICzEFwE,OAAA/D,UAAA0E,SAAA;QACE,OAAO,IAAInB,eAAkBhE,KAAK2E,IAAU,GAAG3E,MAAI;AD2EnD;ICzEFwE,OAAA/D,UAAA2E,OAAA;QACE,OAAO,IAAIpB,gBAAmB,GAAGhE,MAAI;AD2ErC;ICzEFwE,OAAA/D,UAAA4E,QAAA;QACE,OAAOrF,KAAK0E,EAAQ;AD2EpB;ICzEFF,OAAA/D,UAAA6E,OAAA;QACE,OAAOtF,KAAK0E,EAAQ1E,KAAK2E,IAAU;AD2EnC;ICzEFH,OAAA/D,UAAA8E,kBAAA,SAAgBC;QD2EV,IC1EsBA,IAAG,KAAHA,IAAQxF,KAAK2E,IAAO,GA3DpC;YAAE,MAAU,IAAIc;ADuItB;QC3EJ,OAAOzF,KAAK0E,EAAQc;AD6EpB;IC3EFhB,OAAA/D,UAAAiF,oBAAA,SAAkBF;QD6EZ,IC5EsBA,IAAG,KAAHA,IAAQxF,KAAK2E,IAAO,GA/DpC;YAAE,MAAU,IAAIc;AD6ItB;QC7EJzF,KAAK0E,EAAQiB,OAAOH,GAAK;QACzBxF,KAAK2E,KAAW;QAChB,OAAO3E,KAAK2E;AD+EZ;IC7EFH,OAAA/D,UAAAmF,sBAAA,SAAoBpD;QAClB,IAAIqD,IAAQ;QACZ,KAAK,IAAI7C,IAAI,GAAGA,IAAIhD,KAAK2E,KAAW3B,GAAG;YACrC,IAAIhD,KAAK0E,EAAQ1B,OAAOR,GAAO;gBAC7BxC,KAAK0E,EAAQmB,OAAW7F,KAAK0E,EAAQ1B;AD+EjC;AACJ;QC7EJhD,KAAK2E,IAAU3E,KAAK0E,EAAQhC,SAASmD;QACrC,OAAO7F,KAAK2E;AD+EZ;IC7EFH,OAAA/D,UAAAqF,yBAAA,SAAuBC;QACrB,IAAMxB,IAAQwB,EAAKxB;QACnBwB,IAAOA,EAAKlE;QACZ7B,KAAK0F,kBAAkBnB;QACvB,OAAOwB;AD+EP;IC7EFvB,OAAA/D,UAAAsE,WAAA,SAASiB;QACPhG,KAAK0E,EAAQ/B,KAAKqD;QAClBhG,KAAK2E,KAAW;QAChB,OAAO3E,KAAK2E;AD+EZ;IC7EFH,OAAA/D,UAAAwF,UAAA;QACE,IAAIjG,KAAK2E,MAAY,GAAG;QACxB3E,KAAK2E,KAAW;QAChB,OAAO3E,KAAK0E,EAAQjC;ADgFpB;IC9EF+B,OAAA/D,UAAAyF,kBAAA,SAAgBV,GAAaQ;QDgFvB,IC/EsBR,IAAG,KAAHA,IAAQxF,KAAK2E,IAAO,GA/FpC;YAAE,MAAU,IAAIc;ADgLtB;QChFJzF,KAAK0E,EAAQc,KAAOQ;ADkFpB;IChFFxB,OAAA/D,UAAA0F,SAAA,SAAOX,GAAaQ,GAAYI;QDkF1B,IAAIC;QClFsB,IAAAD,WAAA,GAAA;YAAAA,IAAA;AAAO;QDoFjC,ICnFsBZ,IAAG,KAAHA,IAAQxF,KAAK2E,GAnG7B;YAAE,MAAU,IAAIc;ADwLtB;SCpFJY,IAAArG,KAAK0E,GAAQiB,OAAMW,MAAAD,GAAAjD,cAAA,EAACoC,GAAK,KAAC3C,OAAK,IAAItC,MAAS6F,GAAKG,KAAKP,KAAQ;QAC9DhG,KAAK2E,KAAWyB;QAChB,OAAOpG,KAAK2E;ADsFZ;ICpFFH,OAAA/D,UAAA+F,OAAA,SAAKR;QACH,KAAK,IAAIhD,IAAI,GAAGA,IAAIhD,KAAK2E,KAAW3B,GAAG;YACrC,IAAIhD,KAAK0E,EAAQ1B,OAAOgD,GAAS;gBAC/B,OAAO,IAAIhC,eAAkBhB,GAAGhD;ADsF5B;AACJ;QCpFJ,OAAOA,KAAKkF;ADsFZ;ICpFFV,OAAA/D,UAAAgG,UAAA;QACEzG,KAAK0E,EAAQ+B;ADsFb;ICpFFjC,OAAA/D,UAAAiG,SAAA;QACE,IAAIb,IAAQ;QACZ,KAAK,IAAI7C,IAAI,GAAGA,IAAIhD,KAAK2E,KAAW3B,GAAG;YACrC,IAAIhD,KAAK0E,EAAQ1B,OAAOhD,KAAK0E,EAAQ1B,IAAI,IAAI;gBAC3ChD,KAAK0E,EAAQmB,OAAW7F,KAAK0E,EAAQ1B;ADsFjC;AACJ;QCpFJhD,KAAK2E,IAAU3E,KAAK0E,EAAQhC,SAASmD;QACrC,OAAO7F,KAAK2E;ADsFZ;ICpFFH,OAAA/D,UAAAkG,OAAA,SAAKC;QACH5G,KAAK0E,EAAQiC,KAAKC;ADsFlB;ICpFFpC,OAAA/D,UAAAoE,UAAA,SAAQgC;QACN,KAAK,IAAI7D,IAAI,GAAGA,IAAIhD,KAAK2E,KAAW3B,GAAG;YACrC6D,EAAS7G,KAAK0E,EAAQ1B,IAAIA,GAAGhD;ADsF3B;AACJ;ICpFFwE,OAAA/D,UAACwB,OAAOC,YAAR;QACE,OAAO;YDsFC,OAAOjB,YAAYjB,OAAM,SAAUqG;gBAC/B,QAAQA,EAAGhF;kBACP,KAAK;oBCvFnB,OAAA,EAAA,GAAAuC,SAAQ5D,KAAK0E;;kBDwFC,KAAK;oBCxFnB2B,EAAA/E;oBD0FkB,OAAO,EAAC;;AAEpB;AACJ,UC5FFwF,KAAK9G,KAFA;AD+FP;IC3FJ,OAAAwE;AAAA,CA7HA,CAAwBV;;eA+HTU", "file": "Vector.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport SequentialContainer from './Base';\nimport { RandomIterator } from \"./Base/RandomIterator\";\nimport $checkWithinAccessParams from \"../../utils/checkParams.macro\";\nvar VectorIterator = /** @class */ (function (_super) {\n    __extends(VectorIterator, _super);\n    function VectorIterator(node, container, iteratorType) {\n        var _this = _super.call(this, node, iteratorType) || this;\n        _this.container = container;\n        return _this;\n    }\n    VectorIterator.prototype.copy = function () {\n        return new VectorIterator(this._node, this.container, this.iteratorType);\n    };\n    return VectorIterator;\n}(RandomIterator));\nvar Vector = /** @class */ (function (_super) {\n    __extends(Vector, _super);\n    /**\n     * @param container - Initialize container, must have a forEach function.\n     * @param copy - When the container is an array, you can choose to directly operate on the original object of\n     *               the array or perform a shallow copy. The default is shallow copy.\n     */\n    function Vector(container, copy) {\n        if (container === void 0) { container = []; }\n        if (copy === void 0) { copy = true; }\n        var _this = _super.call(this) || this;\n        if (Array.isArray(container)) {\n            _this._vector = copy ? __spreadArray([], __read(container), false) : container;\n            _this._length = container.length;\n        }\n        else {\n            _this._vector = [];\n            var self_1 = _this;\n            container.forEach(function (el) {\n                self_1.pushBack(el);\n            });\n        }\n        return _this;\n    }\n    Vector.prototype.clear = function () {\n        this._length = 0;\n        this._vector.length = 0;\n    };\n    Vector.prototype.begin = function () {\n        return new VectorIterator(0, this);\n    };\n    Vector.prototype.end = function () {\n        return new VectorIterator(this._length, this);\n    };\n    Vector.prototype.rBegin = function () {\n        return new VectorIterator(this._length - 1, this, 1 /* IteratorType.REVERSE */);\n    };\n    Vector.prototype.rEnd = function () {\n        return new VectorIterator(-1, this, 1 /* IteratorType.REVERSE */);\n    };\n    Vector.prototype.front = function () {\n        return this._vector[0];\n    };\n    Vector.prototype.back = function () {\n        return this._vector[this._length - 1];\n    };\n    Vector.prototype.getElementByPos = function (pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        return this._vector[pos];\n    };\n    Vector.prototype.eraseElementByPos = function (pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        this._vector.splice(pos, 1);\n        this._length -= 1;\n        return this._length;\n    };\n    Vector.prototype.eraseElementByValue = function (value) {\n        var index = 0;\n        for (var i = 0; i < this._length; ++i) {\n            if (this._vector[i] !== value) {\n                this._vector[index++] = this._vector[i];\n            }\n        }\n        this._length = this._vector.length = index;\n        return this._length;\n    };\n    Vector.prototype.eraseElementByIterator = function (iter) {\n        var _node = iter._node;\n        iter = iter.next();\n        this.eraseElementByPos(_node);\n        return iter;\n    };\n    Vector.prototype.pushBack = function (element) {\n        this._vector.push(element);\n        this._length += 1;\n        return this._length;\n    };\n    Vector.prototype.popBack = function () {\n        if (this._length === 0)\n            return;\n        this._length -= 1;\n        return this._vector.pop();\n    };\n    Vector.prototype.setElementByPos = function (pos, element) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        this._vector[pos] = element;\n    };\n    Vector.prototype.insert = function (pos, element, num) {\n        var _a;\n        if (num === void 0) { num = 1; }\n        if (pos < 0 || pos > this._length) {\n            throw new RangeError();\n        }\n        (_a = this._vector).splice.apply(_a, __spreadArray([pos, 0], __read(new Array(num).fill(element)), false));\n        this._length += num;\n        return this._length;\n    };\n    Vector.prototype.find = function (element) {\n        for (var i = 0; i < this._length; ++i) {\n            if (this._vector[i] === element) {\n                return new VectorIterator(i, this);\n            }\n        }\n        return this.end();\n    };\n    Vector.prototype.reverse = function () {\n        this._vector.reverse();\n    };\n    Vector.prototype.unique = function () {\n        var index = 1;\n        for (var i = 1; i < this._length; ++i) {\n            if (this._vector[i] !== this._vector[i - 1]) {\n                this._vector[index++] = this._vector[i];\n            }\n        }\n        this._length = this._vector.length = index;\n        return this._length;\n    };\n    Vector.prototype.sort = function (cmp) {\n        this._vector.sort(cmp);\n    };\n    Vector.prototype.forEach = function (callback) {\n        for (var i = 0; i < this._length; ++i) {\n            callback(this._vector[i], i, this);\n        }\n    };\n    Vector.prototype[Symbol.iterator] = function () {\n        return function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [5 /*yield**/, __values(this._vector)];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        }.bind(this)();\n    };\n    return Vector;\n}(SequentialContainer));\nexport default Vector;\n", "import SequentialContainer from './Base';\nimport { initContainer, IteratorType } from '@/container/ContainerBase';\nimport { RandomIterator } from '@/container/SequentialContainer/Base/RandomIterator';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\n\nclass VectorIterator<T> extends RandomIterator<T> {\n  container: Vector<T>;\n  constructor(node: number, container: Vector<T>, iteratorType?: IteratorType) {\n    super(node, iteratorType);\n    this.container = container;\n  }\n  copy() {\n    return new VectorIterator<T>(this._node, this.container, this.iteratorType);\n  }\n  // @ts-ignore\n  equals(iter: VectorIterator<T>): boolean;\n}\n\nexport type { VectorIterator };\n\nclass Vector<T> extends SequentialContainer<T> {\n  /**\n   * @internal\n   */\n  private readonly _vector: T[];\n  /**\n   * @param container - Initialize container, must have a forEach function.\n   * @param copy - When the container is an array, you can choose to directly operate on the original object of\n   *               the array or perform a shallow copy. The default is shallow copy.\n   */\n  constructor(container: initContainer<T> = [], copy = true) {\n    super();\n    if (Array.isArray(container)) {\n      this._vector = copy ? [...container] : container;\n      this._length = container.length;\n    } else {\n      this._vector = [];\n      const self = this;\n      container.forEach(function (el) {\n        self.pushBack(el);\n      });\n    }\n  }\n  clear() {\n    this._length = 0;\n    this._vector.length = 0;\n  }\n  begin() {\n    return new VectorIterator<T>(0, this);\n  }\n  end() {\n    return new VectorIterator<T>(this._length, this);\n  }\n  rBegin() {\n    return new VectorIterator<T>(this._length - 1, this, IteratorType.REVERSE);\n  }\n  rEnd() {\n    return new VectorIterator<T>(-1, this, IteratorType.REVERSE);\n  }\n  front(): T | undefined {\n    return this._vector[0];\n  }\n  back(): T | undefined {\n    return this._vector[this._length - 1];\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    return this._vector[pos];\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    this._vector.splice(pos, 1);\n    this._length -= 1;\n    return this._length;\n  }\n  eraseElementByValue(value: T) {\n    let index = 0;\n    for (let i = 0; i < this._length; ++i) {\n      if (this._vector[i] !== value) {\n        this._vector[index++] = this._vector[i];\n      }\n    }\n    this._length = this._vector.length = index;\n    return this._length;\n  }\n  eraseElementByIterator(iter: VectorIterator<T>) {\n    const _node = iter._node;\n    iter = iter.next();\n    this.eraseElementByPos(_node);\n    return iter;\n  }\n  pushBack(element: T) {\n    this._vector.push(element);\n    this._length += 1;\n    return this._length;\n  }\n  popBack() {\n    if (this._length === 0) return;\n    this._length -= 1;\n    return this._vector.pop();\n  }\n  setElementByPos(pos: number, element: T) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    this._vector[pos] = element;\n  }\n  insert(pos: number, element: T, num = 1) {\n    $checkWithinAccessParams!(pos, 0, this._length);\n    this._vector.splice(pos, 0, ...new Array<T>(num).fill(element));\n    this._length += num;\n    return this._length;\n  }\n  find(element: T) {\n    for (let i = 0; i < this._length; ++i) {\n      if (this._vector[i] === element) {\n        return new VectorIterator<T>(i, this);\n      }\n    }\n    return this.end();\n  }\n  reverse() {\n    this._vector.reverse();\n  }\n  unique() {\n    let index = 1;\n    for (let i = 1; i < this._length; ++i) {\n      if (this._vector[i] !== this._vector[i - 1]) {\n        this._vector[index++] = this._vector[i];\n      }\n    }\n    this._length = this._vector.length = index;\n    return this._length;\n  }\n  sort(cmp?: (x: T, y: T) => number) {\n    this._vector.sort(cmp);\n  }\n  forEach(callback: (element: T, index: number, vector: Vector<T>) => void) {\n    for (let i = 0; i < this._length; ++i) {\n      callback(this._vector[i], i, this);\n    }\n  }\n  [Symbol.iterator]() {\n    return function * (this: Vector<T>) {\n      yield * this._vector;\n    }.bind(this)();\n  }\n}\n\nexport default Vector;\n"]}