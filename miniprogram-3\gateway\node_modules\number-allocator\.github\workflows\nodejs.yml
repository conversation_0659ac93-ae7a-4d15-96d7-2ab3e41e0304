name: number-allocator C<PERSON>

on:
  pull_request:
    types: [opened, synchronize]
  push:
    branches:
    - main
    tags:
    - '*'

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [14.x, 15.x, 16.x, 18.x, 19.x]
      fail-fast: false

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v1
      with:
        node-version: ${{ matrix.node-version }}
    - run: npm install
    - run: npm run typescript-test
    - run: npm test && npm run codecov
      env:
        CI: true
        DEBUG: "number-allocator*"
