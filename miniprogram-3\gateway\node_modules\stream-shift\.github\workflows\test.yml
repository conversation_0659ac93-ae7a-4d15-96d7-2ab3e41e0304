name: Build Status
on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
jobs:
  build:
    strategy:
      matrix:
        node-version: [lts/*]
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
    - run: npm install
    - run: npm test
