{"name": "ev-gateway-server", "version": "1.0.0", "description": "电动车小程序网关服务器 - 连接本地MQTT和数据库", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-gateway.js", "test:watch": "nodemon test-gateway.js"}, "keywords": ["electric-vehicle", "mqtt", "gateway", "api", "wechat-miniprogram"], "author": "Aurora", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "mqtt": "^4.3.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}