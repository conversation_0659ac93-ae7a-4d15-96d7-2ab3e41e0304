// gateway/server.js - 网关服务器
// 提供HTTP API接口，连接本地MQTT服务器和数据库

const express = require('express');
const cors = require('cors');
const mqtt = require('mqtt');
const app = express();

// 中间件配置
app.use(cors({
  origin: '*', // 开发环境允许所有来源，生产环境需要限制
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// MQTT客户端配置
const MQTT_CONFIG = {
  host: 'localhost',
  port: 1883,
  username: 'mdfk',
  password: '1HUJIFDAHSOUF',
  clientId: 'gateway_' + Date.now()
};

// 数据存储
let mqttClient = null;
let latestVehicleData = null;
let connectionStatus = {
  mqtt: false,
  database: false
};

// 模拟数据库连接（替换为实际数据库）
const database = {
  connected: false,
  
  connect() {
    console.log('连接到本地数据库...');
    this.connected = true;
    connectionStatus.database = true;
    return Promise.resolve();
  },
  
  // 保存车辆数据
  saveVehicleData(data) {
    console.log('保存车辆数据到数据库:', data);
    // 这里应该是实际的数据库保存逻辑
    return Promise.resolve({ success: true, id: Date.now() });
  },
  
  // 获取历史数据
  getHistoryData(limit = 10, offset = 0) {
    console.log(`获取历史数据: limit=${limit}, offset=${offset}`);
    // 这里应该是实际的数据库查询逻辑
    return Promise.resolve([]);
  }
};

/**
 * 初始化MQTT连接
 */
function initializeMqtt() {
  console.log('连接到本地MQTT服务器...');
  
  mqttClient = mqtt.connect(`mqtt://${MQTT_CONFIG.host}:${MQTT_CONFIG.port}`, {
    username: MQTT_CONFIG.username,
    password: MQTT_CONFIG.password,
    clientId: MQTT_CONFIG.clientId,
    keepalive: 60,
    reconnectPeriod: 5000
  });

  mqttClient.on('connect', () => {
    console.log('MQTT连接成功');
    connectionStatus.mqtt = true;
    
    // 订阅车辆数据主题
    mqttClient.subscribe('vehicle/data', (err) => {
      if (err) {
        console.error('订阅主题失败:', err);
      } else {
        console.log('已订阅车辆数据主题');
      }
    });
  });

  mqttClient.on('message', (topic, message) => {
    try {
      if (topic === 'vehicle/data') {
        const data = JSON.parse(message.toString());
        latestVehicleData = {
          ...data,
          timestamp: new Date().toISOString(),
          receivedAt: Date.now()
        };
        
        console.log('收到车辆数据:', latestVehicleData);
        
        // 保存到数据库
        database.saveVehicleData(latestVehicleData);
      }
    } catch (error) {
      console.error('MQTT消息处理错误:', error);
    }
  });

  mqttClient.on('error', (error) => {
    console.error('MQTT连接错误:', error);
    connectionStatus.mqtt = false;
  });

  mqttClient.on('close', () => {
    console.log('MQTT连接断开');
    connectionStatus.mqtt = false;
  });
}

/**
 * 生成模拟数据（当没有真实数据时）
 */
function generateMockData() {
  return {
    user: 'admin',
    password: 'admin123',
    gps: {
      longitude: 116.397428 + (Math.random() - 0.5) * 0.01,
      latitude: 39.90923 + (Math.random() - 0.5) * 0.01
    },
    car: {
      speed: Math.round(Math.random() * 25 * 10) / 10,
      power: Math.round(Math.random() * 100),
      mode: ['youth', 'adult', 'elderly'][Math.floor(Math.random() * 3)],
      connected: true
    },
    _id: Date.now().toString(),
    timestamp: new Date().toISOString()
  };
}

// ==================== API 路由 ====================

/**
 * 健康检查
 */
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: '网关服务正常',
    status: {
      mqtt: connectionStatus.mqtt,
      database: connectionStatus.database,
      uptime: process.uptime()
    },
    timestamp: new Date().toISOString()
  });
});

/**
 * 获取车辆实时数据
 */
app.get('/api/vehicle/data', (req, res) => {
  try {
    let data = latestVehicleData;
    
    // 如果没有真实数据，生成模拟数据
    if (!data) {
      data = generateMockData();
      console.log('使用模拟数据:', data);
    }
    
    res.json({
      success: true,
      message: '获取车辆数据成功',
      data: data,
      dataSource: latestVehicleData ? 'mqtt' : 'mock',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取车辆数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取车辆数据失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 获取车辆状态
 */
app.get('/api/vehicle/status', (req, res) => {
  res.json({
    success: true,
    message: '获取状态成功',
    data: {
      connected: connectionStatus.mqtt,
      lastUpdate: latestVehicleData?.timestamp || null,
      battery: latestVehicleData?.car?.power || 0,
      mode: latestVehicleData?.car?.mode || 'adult',
      location: latestVehicleData?.gps || null,
      dataSource: latestVehicleData ? 'mqtt' : 'mock'
    },
    timestamp: new Date().toISOString()
  });
});

/**
 * 设置车辆模式
 */
app.post('/api/vehicle/mode', (req, res) => {
  try {
    const { mode } = req.body;
    
    if (!['youth', 'adult', 'elderly'].includes(mode)) {
      return res.status(400).json({
        success: false,
        message: '无效的模式参数',
        validModes: ['youth', 'adult', 'elderly'],
        timestamp: new Date().toISOString()
      });
    }
    
    // 发送MQTT命令
    if (mqttClient && connectionStatus.mqtt) {
      const command = {
        type: 'setMode',
        mode: mode,
        timestamp: new Date().toISOString(),
        source: 'gateway_api'
      };
      
      mqttClient.publish('vehicle/command', JSON.stringify(command), (err) => {
        if (err) {
          console.error('发送模式切换命令失败:', err);
        } else {
          console.log('模式切换命令发送成功:', command);
        }
      });
    }
    
    // 更新本地数据
    if (latestVehicleData) {
      latestVehicleData.car.mode = mode;
      latestVehicleData.timestamp = new Date().toISOString();
    }
    
    res.json({
      success: true,
      message: '模式设置成功',
      data: { 
        mode, 
        timestamp: new Date().toISOString(),
        mqttSent: connectionStatus.mqtt
      }
    });
    
  } catch (error) {
    console.error('模式设置失败:', error);
    res.status(500).json({
      success: false,
      message: '模式设置失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 获取历史数据
 */
app.get('/api/vehicle/history', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const offset = parseInt(req.query.offset) || 0;
    
    const historyData = await database.getHistoryData(limit, offset);
    
    res.json({
      success: true,
      message: '获取历史数据成功',
      data: historyData,
      pagination: { limit, offset },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取历史数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取历史数据失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// ==================== 启动服务器 ====================

const PORT = process.env.PORT || 3000;

async function startServer() {
  try {
    // 连接数据库
    await database.connect();
    console.log('数据库连接成功');
    
    // 初始化MQTT
    initializeMqtt();
    
    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log(`网关服务器运行在端口 ${PORT}`);
      console.log(`API地址: http://localhost:${PORT}/api`);
      console.log(`健康检查: http://localhost:${PORT}/api/health`);
    });
    
  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('正在关闭服务器...');
  if (mqttClient) {
    mqttClient.end();
  }
  process.exit(0);
});

// 启动服务器
startServer();
