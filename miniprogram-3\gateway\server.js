// gateway/server.js - 网关服务器
// 提供HTTP API接口，连接本地MQTT服务器和数据库

const express = require('express');
const cors = require('cors');
const mqtt = require('mqtt');
const app = express();

// 中间件配置
app.use(cors({
  origin: '*', // 开发环境允许所有来源，生产环境需要限制
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// MQTT客户端配置 - 根据用户提供的实际配置
const MQTT_CONFIG = {
  host: 's3.v100.vip',
  port: 33880,
  username: 'mdfk',
  password: '1HUJIFDAHSOUF',
  clientId: 'mdfk124xfasrf'
};

// 数据存储
let mqttClient = null;
let latestVehicleData = null;
let connectionStatus = {
  mqtt: false,
  database: false
};

// 本地数据库模拟 - 根据用户提供的数据库结构
const database = {
  connected: false,
  data: {
    "user": [
      {
        "id": 1,
        "role": "admin",
        "username": "admin",
        "password": "admin123",
        "_id": 0
      }
    ],
    "gps_data": [
      {
        "id": 1,
        "longitude": 110.45,
        "latitude": 43.923,
        "_id": 0
      }
    ],
    "car_data": [
      {
        "id": 1,
        "speed": 25,
        "power": 87,
        "mod": 1,
        "_id": 0
      }
    ]
  },

  connect() {
    console.log('连接到本地数据库...');
    this.connected = true;
    connectionStatus.database = true;
    return Promise.resolve();
  },

  // 更新数据库内容
  updateData(db, type, value, id = 1) {
    console.log(`更新数据库: db=${db}, type=${type}, value=${value}, id=${id}`);

    if (this.data[db]) {
      const record = this.data[db].find(item => item.id === id);
      if (record) {
        record[type] = value;
        console.log(`数据更新成功:`, record);
        return Promise.resolve({ success: true, data: record });
      }
    }

    return Promise.reject({ success: false, message: '数据更新失败' });
  },

  // 获取数据库内容
  getData(db, id = 1) {
    console.log(`获取数据库内容: db=${db}, id=${id}`);

    if (this.data[db]) {
      const record = this.data[db].find(item => item.id === id);
      if (record) {
        return Promise.resolve({ success: true, data: record });
      }
    }

    return Promise.reject({ success: false, message: '数据不存在' });
  },

  // 获取所有车辆数据（组合GPS和车辆数据）
  getVehicleData(id = 1) {
    const gpsData = this.data.gps_data.find(item => item.id === id);
    const carData = this.data.car_data.find(item => item.id === id);
    const userData = this.data.user.find(item => item.id === id);

    if (gpsData && carData && userData) {
      return {
        user: userData.username,
        password: userData.password,
        gps: {
          longitude: gpsData.longitude,
          latitude: gpsData.latitude
        },
        car: {
          speed: carData.speed,
          power: carData.power,
          mode: this.convertModeToString(carData.mod),
          connected: true
        },
        _id: Date.now().toString(),
        timestamp: new Date().toISOString()
      };
    }

    return null;
  },

  // 模式转换：数字转字符串
  convertModeToString(mod) {
    const modeMap = { 1: 'youth', 2: 'adult', 3: 'elderly' };
    return modeMap[mod] || 'adult';
  },

  // 模式转换：字符串转数字
  convertModeToNumber(mode) {
    const modeMap = { 'youth': 1, 'adult': 2, 'elderly': 3 };
    return modeMap[mode] || 2;
  }
};

/**
 * 初始化MQTT连接
 */
function initializeMqtt() {
  console.log(`连接到MQTT服务器: ${MQTT_CONFIG.host}:${MQTT_CONFIG.port}`);

  mqttClient = mqtt.connect(`mqtt://${MQTT_CONFIG.host}:${MQTT_CONFIG.port}`, {
    username: MQTT_CONFIG.username,
    password: MQTT_CONFIG.password,
    clientId: MQTT_CONFIG.clientId,
    keepalive: 60,
    reconnectPeriod: 5000
  });

  mqttClient.on('connect', () => {
    console.log('MQTT连接成功');
    connectionStatus.mqtt = true;

    // 订阅数据库操作主题
    mqttClient.subscribe('database/response', (err) => {
      if (err) {
        console.error('订阅数据库响应主题失败:', err);
      } else {
        console.log('已订阅数据库响应主题');
      }
    });

    // 订阅车辆数据主题（如果有直接数据推送）
    mqttClient.subscribe('vehicle/data', (err) => {
      if (err) {
        console.error('订阅车辆数据主题失败:', err);
      } else {
        console.log('已订阅车辆数据主题');
      }
    });
  });

  mqttClient.on('message', (topic, message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log(`收到MQTT消息 [${topic}]:`, data);

      if (topic === 'database/response') {
        // 处理数据库响应
        handleDatabaseResponse(data);
      } else if (topic === 'vehicle/data') {
        // 处理车辆数据推送
        latestVehicleData = {
          ...data,
          timestamp: new Date().toISOString(),
          receivedAt: Date.now()
        };
        console.log('更新车辆数据:', latestVehicleData);
      }
    } catch (error) {
      console.error('MQTT消息处理错误:', error);
    }
  });

  mqttClient.on('error', (error) => {
    console.error('MQTT连接错误:', error);
    connectionStatus.mqtt = false;
  });

  mqttClient.on('close', () => {
    console.log('MQTT连接断开');
    connectionStatus.mqtt = false;
  });
}

/**
 * 处理数据库响应
 */
function handleDatabaseResponse(data) {
  console.log('处理数据库响应:', data);

  // 根据响应更新本地数据库缓存
  if (data.cmd === 'get' && data.success) {
    // 更新本地缓存
    updateLocalCache(data);
  }
}

/**
 * 更新本地缓存
 */
function updateLocalCache(responseData) {
  try {
    const { db, data } = responseData;
    if (db && data && database.data[db]) {
      const existingRecord = database.data[db].find(item => item.id === data.id);
      if (existingRecord) {
        Object.assign(existingRecord, data);
        console.log(`本地缓存已更新 [${db}]:`, existingRecord);
      }
    }
  } catch (error) {
    console.error('更新本地缓存失败:', error);
  }
}

/**
 * 发送数据库查询命令
 */
function sendDatabaseQuery(db, id = 1) {
  if (!mqttClient || !connectionStatus.mqtt) {
    console.warn('MQTT未连接，无法发送数据库查询');
    return Promise.reject(new Error('MQTT未连接'));
  }

  const queryCommand = {
    cmd: "get",
    id: id,
    db: db
  };

  console.log('发送数据库查询:', queryCommand);

  return new Promise((resolve, reject) => {
    mqttClient.publish('database/query', JSON.stringify(queryCommand), (err) => {
      if (err) {
        console.error('发送数据库查询失败:', err);
        reject(err);
      } else {
        console.log('数据库查询命令发送成功');
        resolve();
      }
    });
  });
}

/**
 * 发送数据库更新命令
 */
function sendDatabaseUpdate(db, type, value, id = 1) {
  if (!mqttClient || !connectionStatus.mqtt) {
    console.warn('MQTT未连接，无法发送数据库更新');
    return Promise.reject(new Error('MQTT未连接'));
  }

  const updateCommand = {
    cmd: "update",
    id: id,
    db: db,
    type: type,
    value: value
  };

  console.log('发送数据库更新:', updateCommand);

  return new Promise((resolve, reject) => {
    mqttClient.publish('database/update', JSON.stringify(updateCommand), (err) => {
      if (err) {
        console.error('发送数据库更新失败:', err);
        reject(err);
      } else {
        console.log('数据库更新命令发送成功');
        // 同时更新本地缓存
        database.updateData(db, type, value, id);
        resolve();
      }
    });
  });
}

/**
 * 生成模拟数据（当没有真实数据时）
 */
function generateMockData() {
  return {
    user: 'admin',
    password: 'admin123',
    gps: {
      longitude: 116.397428 + (Math.random() - 0.5) * 0.01,
      latitude: 39.90923 + (Math.random() - 0.5) * 0.01
    },
    car: {
      speed: Math.round(Math.random() * 25 * 10) / 10,
      power: Math.round(Math.random() * 100),
      mode: ['youth', 'adult', 'elderly'][Math.floor(Math.random() * 3)],
      connected: true
    },
    _id: Date.now().toString(),
    timestamp: new Date().toISOString()
  };
}

// ==================== API 路由 ====================

/**
 * 健康检查
 */
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: '网关服务正常',
    status: {
      mqtt: connectionStatus.mqtt,
      database: connectionStatus.database,
      uptime: process.uptime()
    },
    timestamp: new Date().toISOString()
  });
});

/**
 * 获取车辆实时数据
 */
app.get('/api/vehicle/data', async (req, res) => {
  try {
    let data = null;

    // 首先尝试从MQTT获取最新数据
    if (connectionStatus.mqtt) {
      try {
        // 发送数据库查询命令获取最新数据
        await sendDatabaseQuery('gps_data');
        await sendDatabaseQuery('car_data');

        // 等待一小段时间让数据更新
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.warn('MQTT数据查询失败:', error);
      }
    }

    // 从本地数据库获取组合数据
    data = database.getVehicleData(1);

    // 如果没有数据，使用模拟数据
    if (!data) {
      data = generateMockData();
      console.log('使用模拟数据:', data);
    }

    res.json({
      success: true,
      message: '获取车辆数据成功',
      data: data,
      dataSource: connectionStatus.mqtt ? 'database' : 'mock',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取车辆数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取车辆数据失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 获取车辆状态
 */
app.get('/api/vehicle/status', (req, res) => {
  const vehicleData = database.getVehicleData(1);

  res.json({
    success: true,
    message: '获取状态成功',
    data: {
      connected: connectionStatus.mqtt,
      lastUpdate: vehicleData?.timestamp || new Date().toISOString(),
      battery: vehicleData?.car?.power || 0,
      mode: vehicleData?.car?.mode || 'adult',
      location: vehicleData?.gps || null,
      dataSource: connectionStatus.mqtt ? 'database' : 'mock',
      mqtt: {
        host: MQTT_CONFIG.host,
        port: MQTT_CONFIG.port,
        clientId: MQTT_CONFIG.clientId,
        connected: connectionStatus.mqtt
      }
    },
    timestamp: new Date().toISOString()
  });
});

/**
 * 设置车辆模式
 */
app.post('/api/vehicle/mode', async (req, res) => {
  try {
    const { mode } = req.body;

    if (!['youth', 'adult', 'elderly'].includes(mode)) {
      return res.status(400).json({
        success: false,
        message: '无效的模式参数',
        validModes: ['youth', 'adult', 'elderly'],
        timestamp: new Date().toISOString()
      });
    }

    // 转换模式为数字
    const modeNumber = database.convertModeToNumber(mode);

    // 发送数据库更新命令
    if (connectionStatus.mqtt) {
      try {
        await sendDatabaseUpdate('car_data', 'mod', modeNumber, 1);
        console.log(`模式更新成功: ${mode} (${modeNumber})`);
      } catch (error) {
        console.error('MQTT模式更新失败:', error);
      }
    } else {
      // 如果MQTT未连接，直接更新本地数据
      await database.updateData('car_data', 'mod', modeNumber, 1);
    }

    res.json({
      success: true,
      message: '模式设置成功',
      data: {
        mode,
        modeNumber,
        timestamp: new Date().toISOString(),
        mqttSent: connectionStatus.mqtt
      }
    });

  } catch (error) {
    console.error('模式设置失败:', error);
    res.status(500).json({
      success: false,
      message: '模式设置失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 获取数据库表数据
 */
app.get('/api/database/:table', async (req, res) => {
  try {
    const { table } = req.params;
    const id = parseInt(req.query.id) || 1;

    if (!['gps_data', 'car_data', 'user'].includes(table)) {
      return res.status(400).json({
        success: false,
        message: '无效的数据表名',
        validTables: ['gps_data', 'car_data', 'user'],
        timestamp: new Date().toISOString()
      });
    }

    // 尝试从MQTT获取最新数据
    if (connectionStatus.mqtt) {
      try {
        await sendDatabaseQuery(table, id);
        await new Promise(resolve => setTimeout(resolve, 300));
      } catch (error) {
        console.warn('MQTT数据查询失败:', error);
      }
    }

    // 从本地数据库获取数据
    const result = await database.getData(table, id);

    res.json({
      success: true,
      message: `获取${table}数据成功`,
      data: result.data,
      dataSource: connectionStatus.mqtt ? 'database' : 'local',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('获取数据库数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取数据库数据失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 更新数据库表数据
 */
app.put('/api/database/:table', async (req, res) => {
  try {
    const { table } = req.params;
    const { type, value, id = 1 } = req.body;

    if (!['gps_data', 'car_data'].includes(table)) {
      return res.status(400).json({
        success: false,
        message: '无效的数据表名',
        validTables: ['gps_data', 'car_data'],
        timestamp: new Date().toISOString()
      });
    }

    if (!type || value === undefined) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: type, value',
        timestamp: new Date().toISOString()
      });
    }

    // 发送数据库更新命令
    if (connectionStatus.mqtt) {
      await sendDatabaseUpdate(table, type, value, id);
    } else {
      await database.updateData(table, type, value, id);
    }

    res.json({
      success: true,
      message: '数据更新成功',
      data: { table, type, value, id },
      mqttSent: connectionStatus.mqtt,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('数据更新失败:', error);
    res.status(500).json({
      success: false,
      message: '数据更新失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 获取所有数据库数据
 */
app.get('/api/database', (req, res) => {
  try {
    res.json({
      success: true,
      message: '获取所有数据库数据成功',
      data: database.data,
      dataSource: 'local',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取数据库数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取数据库数据失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// ==================== 启动服务器 ====================

const PORT = process.env.PORT || 3000;

async function startServer() {
  try {
    // 连接数据库
    await database.connect();
    console.log('数据库连接成功');
    
    // 初始化MQTT
    initializeMqtt();
    
    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log(`网关服务器运行在端口 ${PORT}`);
      console.log(`API地址: http://localhost:${PORT}/api`);
      console.log(`健康检查: http://localhost:${PORT}/api/health`);
    });
    
  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('正在关闭服务器...');
  if (mqttClient) {
    mqttClient.end();
  }
  process.exit(0);
});

// 启动服务器
startServer();
