@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   电动车小程序网关服务器启动脚本
echo ========================================
echo.

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Node.js
    echo 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js版本:
node --version
echo.

:: 检查是否在正确目录
if not exist "package.json" (
    echo ❌ 错误：未找到package.json文件
    echo 请确保在gateway目录下运行此脚本
    pause
    exit /b 1
)

:: 检查依赖是否安装
if not exist "node_modules" (
    echo 📦 正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
    echo.
)

:: 显示配置信息
echo 🔧 MQTT配置信息:
echo    服务器: s3.v100.vip:33880
echo    用户名: mdfk
echo    客户端ID: mdfk124xfasrf
echo.

echo 🌐 API接口地址:
echo    本地地址: http://localhost:3000/api
echo.

:: 询问用户选择
echo 请选择操作:
echo [1] 启动网关服务器
echo [2] 测试MQTT连接
echo [3] 测试API接口
echo [4] 查看帮助
echo.
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto start_server
if "%choice%"=="2" goto test_mqtt
if "%choice%"=="3" goto test_api
if "%choice%"=="4" goto show_help
echo 无效选择，默认启动服务器
goto start_server

:start_server
echo.
echo 🚀 正在启动网关服务器...
echo 按 Ctrl+C 停止服务器
echo.
npm start
goto end

:test_mqtt
echo.
echo 🔌 正在测试MQTT连接...
echo 按 Ctrl+C 停止测试
echo.
node test-mqtt.js
goto end

:test_api
echo.
echo 🧪 正在测试API接口...
echo.
npm test
goto end

:show_help
echo.
echo 📖 帮助信息
echo ========================================
echo.
echo 网关服务器功能:
echo   - 连接到MQTT服务器 s3.v100.vip:33880
echo   - 提供HTTP API接口给小程序使用
echo   - 处理数据库查询和更新命令
echo   - 支持车辆模式切换
echo.
echo 主要API接口:
echo   GET  /api/health         - 健康检查
echo   GET  /api/vehicle/data   - 获取车辆数据
echo   GET  /api/vehicle/status - 获取车辆状态
echo   POST /api/vehicle/mode   - 设置车辆模式
echo   GET  /api/database/:table - 获取数据库表数据
echo   PUT  /api/database/:table - 更新数据库表数据
echo.
echo 小程序配置:
echo   1. 在微信开发者工具中启用"不校验合法域名"
echo   2. 设置API地址为: http://localhost:3000/api
echo.
echo 故障排除:
echo   - 检查MQTT服务器是否运行
echo   - 检查网络连接
echo   - 查看控制台错误信息
echo   - 运行测试脚本验证功能
echo.
pause
goto end

:end
echo.
echo 按任意键退出...
pause >nul
