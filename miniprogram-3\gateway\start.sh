#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo
echo "========================================"
echo "  电动车小程序网关服务器启动脚本"
echo "========================================"
echo

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 错误：未检测到Node.js${NC}"
    echo "请先安装Node.js: https://nodejs.org/"
    exit 1
fi

echo -e "${GREEN}✅ Node.js版本:${NC}"
node --version
echo

# 检查是否在正确目录
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ 错误：未找到package.json文件${NC}"
    echo "请确保在gateway目录下运行此脚本"
    exit 1
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 正在安装依赖...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 依赖安装失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
    echo
fi

# 显示配置信息
echo -e "${BLUE}🔧 MQTT配置信息:${NC}"
echo "   服务器: s3.v100.vip:33880"
echo "   用户名: mdfk"
echo "   客户端ID: mdfk124xfasrf"
echo

echo -e "${BLUE}🌐 API接口地址:${NC}"
echo "   本地地址: http://localhost:3000/api"
echo

# 询问用户选择
echo "请选择操作:"
echo "[1] 启动网关服务器"
echo "[2] 测试MQTT连接"
echo "[3] 测试API接口"
echo "[4] 查看帮助"
echo
read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo
        echo -e "${GREEN}🚀 正在启动网关服务器...${NC}"
        echo "按 Ctrl+C 停止服务器"
        echo
        npm start
        ;;
    2)
        echo
        echo -e "${YELLOW}🔌 正在测试MQTT连接...${NC}"
        echo "按 Ctrl+C 停止测试"
        echo
        node test-mqtt.js
        ;;
    3)
        echo
        echo -e "${YELLOW}🧪 正在测试API接口...${NC}"
        echo
        npm test
        ;;
    4)
        echo
        echo -e "${BLUE}📖 帮助信息${NC}"
        echo "========================================"
        echo
        echo "网关服务器功能:"
        echo "  - 连接到MQTT服务器 s3.v100.vip:33880"
        echo "  - 提供HTTP API接口给小程序使用"
        echo "  - 处理数据库查询和更新命令"
        echo "  - 支持车辆模式切换"
        echo
        echo "主要API接口:"
        echo "  GET  /api/health         - 健康检查"
        echo "  GET  /api/vehicle/data   - 获取车辆数据"
        echo "  GET  /api/vehicle/status - 获取车辆状态"
        echo "  POST /api/vehicle/mode   - 设置车辆模式"
        echo "  GET  /api/database/:table - 获取数据库表数据"
        echo "  PUT  /api/database/:table - 更新数据库表数据"
        echo
        echo "小程序配置:"
        echo "  1. 在微信开发者工具中启用\"不校验合法域名\""
        echo "  2. 设置API地址为: http://localhost:3000/api"
        echo
        echo "故障排除:"
        echo "  - 检查MQTT服务器是否运行"
        echo "  - 检查网络连接"
        echo "  - 查看控制台错误信息"
        echo "  - 运行测试脚本验证功能"
        echo
        ;;
    *)
        echo "无效选择，默认启动服务器"
        echo
        echo -e "${GREEN}🚀 正在启动网关服务器...${NC}"
        echo "按 Ctrl+C 停止服务器"
        echo
        npm start
        ;;
esac

echo
echo "脚本执行完成"
