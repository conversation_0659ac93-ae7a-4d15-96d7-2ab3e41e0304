// test-gateway.js - 网关服务器测试脚本
const http = require('http');

const API_BASE = 'http://localhost:3001/api';

/**
 * 发送HTTP请求
 */
function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(API_BASE + path);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (error) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

/**
 * 测试健康检查
 */
async function testHealth() {
  console.log('\n=== 测试健康检查 ===');
  try {
    const result = await makeRequest('/health');
    console.log('状态码:', result.status);
    console.log('响应:', JSON.stringify(result.data, null, 2));
    
    if (result.data.success) {
      console.log('✅ 健康检查通过');
      console.log('MQTT连接状态:', result.data.status.mqtt ? '已连接' : '未连接');
      console.log('数据库状态:', result.data.status.database ? '已连接' : '未连接');
    } else {
      console.log('❌ 健康检查失败');
    }
  } catch (error) {
    console.log('❌ 健康检查请求失败:', error.message);
  }
}

/**
 * 测试获取车辆数据
 */
async function testVehicleData() {
  console.log('\n=== 测试获取车辆数据 ===');
  try {
    const result = await makeRequest('/vehicle/data');
    console.log('状态码:', result.status);
    console.log('响应:', JSON.stringify(result.data, null, 2));
    
    if (result.data.success) {
      console.log('✅ 车辆数据获取成功');
      console.log('数据源:', result.data.dataSource);
      console.log('GPS位置:', result.data.data.gps);
      console.log('车辆状态:', result.data.data.car);
    } else {
      console.log('❌ 车辆数据获取失败');
    }
  } catch (error) {
    console.log('❌ 车辆数据请求失败:', error.message);
  }
}

/**
 * 测试获取车辆状态
 */
async function testVehicleStatus() {
  console.log('\n=== 测试获取车辆状态 ===');
  try {
    const result = await makeRequest('/vehicle/status');
    console.log('状态码:', result.status);
    console.log('响应:', JSON.stringify(result.data, null, 2));
    
    if (result.data.success) {
      console.log('✅ 车辆状态获取成功');
      console.log('连接状态:', result.data.data.connected ? '已连接' : '未连接');
      console.log('电池电量:', result.data.data.battery + '%');
      console.log('当前模式:', result.data.data.mode);
      console.log('MQTT配置:', result.data.data.mqtt);
    } else {
      console.log('❌ 车辆状态获取失败');
    }
  } catch (error) {
    console.log('❌ 车辆状态请求失败:', error.message);
  }
}

/**
 * 测试设置车辆模式
 */
async function testSetMode() {
  console.log('\n=== 测试设置车辆模式 ===');
  
  const modes = ['youth', 'adult', 'elderly'];
  
  for (const mode of modes) {
    try {
      console.log(`\n设置模式为: ${mode}`);
      const result = await makeRequest('/vehicle/mode', 'POST', { mode });
      console.log('状态码:', result.status);
      console.log('响应:', JSON.stringify(result.data, null, 2));
      
      if (result.data.success) {
        console.log(`✅ 模式设置成功: ${mode}`);
        console.log('模式数字:', result.data.data.modeNumber);
        console.log('MQTT发送:', result.data.data.mqttSent ? '是' : '否');
      } else {
        console.log(`❌ 模式设置失败: ${mode}`);
      }
      
      // 等待一秒再测试下一个模式
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.log(`❌ 模式设置请求失败 [${mode}]:`, error.message);
    }
  }
}

/**
 * 测试数据库操作
 */
async function testDatabaseOperations() {
  console.log('\n=== 测试数据库操作 ===');
  
  // 测试获取GPS数据
  try {
    console.log('\n获取GPS数据:');
    const result = await makeRequest('/database/gps_data');
    console.log('状态码:', result.status);
    console.log('响应:', JSON.stringify(result.data, null, 2));
    
    if (result.data.success) {
      console.log('✅ GPS数据获取成功');
    } else {
      console.log('❌ GPS数据获取失败');
    }
  } catch (error) {
    console.log('❌ GPS数据请求失败:', error.message);
  }
  
  // 测试获取车辆数据
  try {
    console.log('\n获取车辆数据:');
    const result = await makeRequest('/database/car_data');
    console.log('状态码:', result.status);
    console.log('响应:', JSON.stringify(result.data, null, 2));
    
    if (result.data.success) {
      console.log('✅ 车辆数据获取成功');
    } else {
      console.log('❌ 车辆数据获取失败');
    }
  } catch (error) {
    console.log('❌ 车辆数据请求失败:', error.message);
  }
  
  // 测试更新数据
  try {
    console.log('\n更新车辆速度:');
    const result = await makeRequest('/database/car_data', 'PUT', {
      type: 'speed',
      value: 30,
      id: 1
    });
    console.log('状态码:', result.status);
    console.log('响应:', JSON.stringify(result.data, null, 2));
    
    if (result.data.success) {
      console.log('✅ 数据更新成功');
    } else {
      console.log('❌ 数据更新失败');
    }
  } catch (error) {
    console.log('❌ 数据更新请求失败:', error.message);
  }
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('\n=== 测试错误处理 ===');
  
  // 测试无效模式
  try {
    console.log('\n测试无效模式:');
    const result = await makeRequest('/vehicle/mode', 'POST', { mode: 'invalid' });
    console.log('状态码:', result.status);
    console.log('响应:', JSON.stringify(result.data, null, 2));
    
    if (result.status === 400) {
      console.log('✅ 错误处理正确');
    } else {
      console.log('❌ 错误处理异常');
    }
  } catch (error) {
    console.log('❌ 错误测试请求失败:', error.message);
  }
  
  // 测试无效数据表
  try {
    console.log('\n测试无效数据表:');
    const result = await makeRequest('/database/invalid_table');
    console.log('状态码:', result.status);
    console.log('响应:', JSON.stringify(result.data, null, 2));
    
    if (result.status === 400) {
      console.log('✅ 数据表验证正确');
    } else {
      console.log('❌ 数据表验证异常');
    }
  } catch (error) {
    console.log('❌ 数据表测试请求失败:', error.message);
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始测试网关服务器...');
  console.log('API地址:', API_BASE);
  
  await testHealth();
  await testVehicleData();
  await testVehicleStatus();
  await testSetMode();
  await testDatabaseOperations();
  await testErrorHandling();
  
  console.log('\n🎉 所有测试完成！');
}

// 检查服务器是否运行
async function checkServer() {
  try {
    await makeRequest('/health');
    return true;
  } catch (error) {
    return false;
  }
}

// 主函数
async function main() {
  console.log('检查网关服务器是否运行...');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('❌ 网关服务器未运行！');
    console.log('请先启动服务器: npm start');
    process.exit(1);
  }
  
  console.log('✅ 网关服务器正在运行');
  await runAllTests();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  makeRequest,
  testHealth,
  testVehicleData,
  testVehicleStatus,
  testSetMode,
  testDatabaseOperations,
  testErrorHandling,
  runAllTests
};
