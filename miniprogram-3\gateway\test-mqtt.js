// test-mqtt.js - MQTT连接测试脚本
const mqtt = require('mqtt');

// MQTT配置 - 使用用户提供的实际配置
const MQTT_CONFIG = {
  host: 's3.v100.vip',
  port: 33880,
  username: 'mdfk',
  password: '1HUJIFDAHSOUF',
  clientId: 'test_client_' + Date.now()
};

let client = null;

/**
 * 测试MQTT连接
 */
function testMqttConnection() {
  console.log('🚀 开始测试MQTT连接...');
  console.log('MQTT配置:', {
    host: MQTT_CONFIG.host,
    port: MQTT_CONFIG.port,
    username: MQTT_CONFIG.username,
    clientId: MQTT_CONFIG.clientId
  });
  
  const connectUrl = `mqtt://${MQTT_CONFIG.host}:${MQTT_CONFIG.port}`;
  console.log('连接URL:', connectUrl);
  
  client = mqtt.connect(connectUrl, {
    username: MQTT_CONFIG.username,
    password: MQTT_CONFIG.password,
    clientId: MQTT_CONFIG.clientId,
    keepalive: 60,
    reconnectPeriod: 5000,
    connectTimeout: 30000
  });

  // 连接成功
  client.on('connect', () => {
    console.log('✅ MQTT连接成功！');
    console.log('客户端ID:', MQTT_CONFIG.clientId);
    
    // 订阅测试主题
    subscribeToTopics();
    
    // 发送测试消息
    setTimeout(() => {
      sendTestMessages();
    }, 2000);
  });

  // 接收消息
  client.on('message', (topic, message) => {
    console.log(`📨 收到消息 [${topic}]:`, message.toString());
    
    try {
      const data = JSON.parse(message.toString());
      console.log('解析后的数据:', JSON.stringify(data, null, 2));
    } catch (error) {
      console.log('消息不是JSON格式:', message.toString());
    }
  });

  // 连接错误
  client.on('error', (error) => {
    console.log('❌ MQTT连接错误:', error.message);
    console.log('错误详情:', error);
  });

  // 连接断开
  client.on('close', () => {
    console.log('🔌 MQTT连接断开');
  });

  // 重连
  client.on('reconnect', () => {
    console.log('🔄 MQTT重新连接中...');
  });

  // 离线
  client.on('offline', () => {
    console.log('📴 MQTT客户端离线');
  });
}

/**
 * 订阅主题
 */
function subscribeToTopics() {
  const topics = [
    'database/response',
    'database/query',
    'database/update',
    'vehicle/data',
    'vehicle/command',
    'test/echo'
  ];
  
  console.log('\n📡 订阅主题...');
  
  topics.forEach(topic => {
    client.subscribe(topic, (err) => {
      if (err) {
        console.log(`❌ 订阅主题失败 [${topic}]:`, err.message);
      } else {
        console.log(`✅ 订阅主题成功 [${topic}]`);
      }
    });
  });
}

/**
 * 发送测试消息
 */
function sendTestMessages() {
  console.log('\n📤 发送测试消息...');
  
  // 1. 发送回声测试
  const echoMessage = {
    type: 'echo',
    message: 'Hello MQTT!',
    timestamp: new Date().toISOString(),
    clientId: MQTT_CONFIG.clientId
  };
  
  client.publish('test/echo', JSON.stringify(echoMessage), (err) => {
    if (err) {
      console.log('❌ 发送回声测试失败:', err.message);
    } else {
      console.log('✅ 回声测试消息发送成功');
    }
  });
  
  // 2. 发送数据库查询命令
  setTimeout(() => {
    const queryCommand = {
      cmd: "get",
      id: 1,
      db: "gps_data"
    };
    
    client.publish('database/query', JSON.stringify(queryCommand), (err) => {
      if (err) {
        console.log('❌ 发送数据库查询失败:', err.message);
      } else {
        console.log('✅ 数据库查询命令发送成功:', queryCommand);
      }
    });
  }, 1000);
  
  // 3. 发送数据库更新命令
  setTimeout(() => {
    const updateCommand = {
      cmd: "update",
      id: 1,
      db: "car_data",
      type: "speed",
      value: 35
    };
    
    client.publish('database/update', JSON.stringify(updateCommand), (err) => {
      if (err) {
        console.log('❌ 发送数据库更新失败:', err.message);
      } else {
        console.log('✅ 数据库更新命令发送成功:', updateCommand);
      }
    });
  }, 2000);
  
  // 4. 发送车辆模式切换命令
  setTimeout(() => {
    const modeCommand = {
      type: 'setMode',
      mode: 'youth',
      timestamp: new Date().toISOString(),
      source: 'test_client'
    };
    
    client.publish('vehicle/command', JSON.stringify(modeCommand), (err) => {
      if (err) {
        console.log('❌ 发送模式切换命令失败:', err.message);
      } else {
        console.log('✅ 模式切换命令发送成功:', modeCommand);
      }
    });
  }, 3000);
}

/**
 * 测试连接超时
 */
function testConnectionTimeout() {
  console.log('\n⏱️  测试连接超时...');
  
  setTimeout(() => {
    if (!client || !client.connected) {
      console.log('❌ MQTT连接超时（30秒）');
      console.log('可能的原因：');
      console.log('1. 网络连接问题');
      console.log('2. MQTT服务器未运行');
      console.log('3. 防火墙阻止连接');
      console.log('4. 认证信息错误');
      process.exit(1);
    }
  }, 30000);
}

/**
 * 优雅关闭
 */
function gracefulShutdown() {
  console.log('\n🛑 正在关闭MQTT连接...');
  
  if (client) {
    client.end(() => {
      console.log('✅ MQTT连接已关闭');
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
MQTT测试工具使用说明：

配置信息：
- MQTT服务器: ${MQTT_CONFIG.host}:${MQTT_CONFIG.port}
- 用户名: ${MQTT_CONFIG.username}
- 客户端ID: ${MQTT_CONFIG.clientId}

测试内容：
1. MQTT连接测试
2. 主题订阅测试
3. 消息发布测试
4. 数据库操作命令测试
5. 车辆控制命令测试

如果连接失败，请检查：
1. 网络连接是否正常
2. MQTT服务器是否运行
3. 防火墙设置
4. 认证信息是否正确

按 Ctrl+C 退出测试
`);
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 MQTT连接测试工具');
  console.log('==================');
  
  showHelp();
  
  // 设置信号处理
  process.on('SIGINT', gracefulShutdown);
  process.on('SIGTERM', gracefulShutdown);
  
  // 开始测试
  testConnectionTimeout();
  testMqttConnection();
  
  // 10分钟后自动退出
  setTimeout(() => {
    console.log('\n⏰ 测试时间结束（10分钟），自动退出');
    gracefulShutdown();
  }, 600000);
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = {
  testMqttConnection,
  MQTT_CONFIG
};
