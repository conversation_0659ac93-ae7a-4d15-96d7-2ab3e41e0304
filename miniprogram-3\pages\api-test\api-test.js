// api-test.js - API密钥测试页面
const ApiKeyDiagnostic = require('../../utils/apiKeyDiagnostic.js');
const CONFIG = require('../../utils/config.js');

Page({
  data: {
    // 测试状态
    testing: false,
    testResults: null,
    
    // 当前配置
    currentKey: '',
    testUrl: '',
    
    // 测试结果
    configCheck: null,
    apiTest: null,
    recommendations: [],
    
    // 显示控制
    showDetails: false,
    showInstructions: false
  },

  onLoad: function() {
    console.log('API测试页面加载');
    
    // 显示当前配置
    this.setData({
      currentKey: CONFIG.AMAP_WEB_KEY,
      testUrl: this.buildSimpleTestUrl()
    });
    
    // 自动运行基础检查
    this.runBasicCheck();
  },

  /**
   * 构建简单测试URL
   */
  buildSimpleTestUrl: function() {
    return `https://restapi.amap.com/v3/staticmap?key=${CONFIG.AMAP_WEB_KEY}&location=116.397428,39.90923&zoom=10&size=400*300`;
  },

  /**
   * 运行基础检查
   */
  runBasicCheck: function() {
    console.log('运行基础配置检查...');
    
    const configResult = ApiKeyDiagnostic.checkApiKeyConfig();
    
    this.setData({
      configCheck: configResult
    });
    
    console.log('基础检查完成:', configResult);
  },

  /**
   * 运行完整诊断
   */
  runFullDiagnostic: function() {
    const that = this;
    
    this.setData({
      testing: true,
      testResults: null
    });
    
    wx.showLoading({
      title: '正在诊断...'
    });
    
    ApiKeyDiagnostic.runFullDiagnostic()
      .then(results => {
        console.log('完整诊断结果:', results);
        
        that.setData({
          testing: false,
          testResults: results,
          apiTest: results.apiTest,
          recommendations: results.recommendations || []
        });
        
        wx.hideLoading();
        
        // 显示结果摘要
        const success = results.apiTest && results.apiTest.success;
        wx.showModal({
          title: success ? '诊断完成' : '发现问题',
          content: success ? 
            'API密钥配置正确，静态地图功能正常！' : 
            '发现API配置问题，请查看详细建议。',
          showCancel: false,
          confirmText: '查看详情',
          success: function() {
            that.setData({ showDetails: true });
          }
        });
      })
      .catch(error => {
        console.error('诊断失败:', error);
        
        that.setData({
          testing: false,
          testResults: {
            success: false,
            error: error,
            recommendations: [
              '诊断过程失败，请检查网络连接',
              '确保微信开发者工具中已关闭域名校验',
              '手动检查API密钥配置'
            ]
          }
        });
        
        wx.hideLoading();
        wx.showToast({
          title: '诊断失败',
          icon: 'error'
        });
      });
  },

  /**
   * 测试静态地图URL
   */
  testStaticMapUrl: function() {
    const that = this;
    
    wx.showLoading({
      title: '测试地图URL...'
    });
    
    // 直接测试当前URL
    wx.request({
      url: this.data.testUrl,
      method: 'GET',
      timeout: 10000,
      success: function(res) {
        console.log('URL测试成功:', res);
        
        wx.hideLoading();
        
        let message = '';
        if (res.statusCode === 200) {
          const contentType = res.header['content-type'] || res.header['Content-Type'] || '';
          if (contentType.includes('image')) {
            message = '✅ URL有效，返回图片数据';
          } else {
            message = '⚠️ URL响应异常，返回非图片数据';
          }
        } else {
          message = `❌ HTTP错误：${res.statusCode}`;
        }
        
        wx.showModal({
          title: 'URL测试结果',
          content: `状态码：${res.statusCode}\n内容类型：${res.header['content-type'] || '未知'}\n\n${message}`,
          showCancel: false
        });
      },
      fail: function(error) {
        console.error('URL测试失败:', error);
        
        wx.hideLoading();
        
        let errorMsg = '网络请求失败';
        if (error.errMsg) {
          if (error.errMsg.includes('timeout')) {
            errorMsg = '请求超时，请检查网络';
          } else if (error.errMsg.includes('domain')) {
            errorMsg = '域名不在合法域名列表中';
          }
        }
        
        wx.showModal({
          title: 'URL测试失败',
          content: `错误：${errorMsg}\n\n建议：\n1. 检查网络连接\n2. 确认域名配置\n3. 关闭域名校验（开发阶段）`,
          showCancel: false
        });
      }
    });
  },

  /**
   * 复制测试URL
   */
  copyTestUrl: function() {
    wx.setClipboardData({
      data: this.data.testUrl,
      success: function() {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 复制API密钥
   */
  copyApiKey: function() {
    wx.setClipboardData({
      data: this.data.currentKey,
      success: function() {
        wx.showToast({
          title: '密钥已复制',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 打开高德控制台
   */
  openAmapConsole: function() {
    wx.showModal({
      title: '打开高德控制台',
      content: '将在浏览器中打开高德开放平台控制台\n\nhttps://console.amap.com/dev/key/app',
      confirmText: '复制链接',
      success: function(res) {
        if (res.confirm) {
          wx.setClipboardData({
            data: 'https://console.amap.com/dev/key/app',
            success: function() {
              wx.showToast({
                title: '链接已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 打开微信公众平台
   */
  openWechatConsole: function() {
    wx.showModal({
      title: '打开微信公众平台',
      content: '将在浏览器中打开微信公众平台\n\nhttps://mp.weixin.qq.com',
      confirmText: '复制链接',
      success: function(res) {
        if (res.confirm) {
          wx.setClipboardData({
            data: 'https://mp.weixin.qq.com',
            success: function() {
              wx.showToast({
                title: '链接已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 切换详情显示
   */
  toggleDetails: function() {
    this.setData({
      showDetails: !this.data.showDetails
    });
  },

  /**
   * 切换说明显示
   */
  toggleInstructions: function() {
    this.setData({
      showInstructions: !this.data.showInstructions
    });
  },

  /**
   * 返回首页
   */
  goBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 重新测试
   */
  retryTest: function() {
    this.runFullDiagnostic();
  }
});
