<!-- api-test.wxml - API密钥测试页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">🔧 API密钥诊断工具</text>
    <text class="subtitle">检测高德地图API配置问题</text>
  </view>

  <!-- 当前配置显示 -->
  <view class="config-section">
    <view class="section-title">📋 当前配置</view>
    
    <view class="config-item">
      <text class="label">API密钥:</text>
      <view class="value-row">
        <text class="value">{{currentKey}}</text>
        <button class="copy-btn" bindtap="copyApiKey">复制</button>
      </view>
    </view>
    
    <view class="config-item">
      <text class="label">测试URL:</text>
      <view class="value-row">
        <text class="value small">{{testUrl}}</text>
        <button class="copy-btn" bindtap="copyTestUrl">复制</button>
      </view>
    </view>
  </view>

  <!-- 基础检查结果 -->
  <view class="check-section" wx:if="{{configCheck}}">
    <view class="section-title">✅ 基础检查</view>
    
    <view class="check-item">
      <text class="check-label">密钥长度:</text>
      <text class="check-value {{configCheck.keyLength === 32 ? 'success' : 'error'}}">
        {{configCheck.keyLength}} {{configCheck.keyLength === 32 ? '✓' : '✗'}}
      </text>
    </view>
    
    <view class="check-item">
      <text class="check-label">密钥格式:</text>
      <text class="check-value {{configCheck.keyFormat ? 'success' : 'error'}}">
        {{configCheck.keyFormat ? '正确 ✓' : '错误 ✗'}}
      </text>
    </view>
    
    <view class="check-item">
      <text class="check-label">SDK/Web密钥:</text>
      <text class="check-value {{configCheck.keysMatch ? 'success' : 'warning'}}">
        {{configCheck.keysMatch ? '一致 ✓' : '不一致 ⚠️'}}
      </text>
    </view>
  </view>

  <!-- 测试按钮区域 -->
  <view class="button-section">
    <button class="test-btn primary" bindtap="runFullDiagnostic" disabled="{{testing}}">
      {{testing ? '诊断中...' : '🔍 运行完整诊断'}}
    </button>
    
    <button class="test-btn secondary" bindtap="testStaticMapUrl">
      🌐 测试地图URL
    </button>
  </view>

  <!-- API测试结果 -->
  <view class="result-section" wx:if="{{apiTest}}">
    <view class="section-title">
      🎯 API测试结果
      <text class="status {{apiTest.success ? 'success' : 'error'}}">
        {{apiTest.success ? '成功' : '失败'}}
      </text>
    </view>
    
    <view class="result-item">
      <text class="result-label">状态:</text>
      <text class="result-value {{apiTest.success ? 'success' : 'error'}}">
        {{apiTest.success ? '✅ API调用成功' : '❌ API调用失败'}}
      </text>
    </view>
    
    <view class="result-item" wx:if="{{apiTest.statusCode}}">
      <text class="result-label">HTTP状态码:</text>
      <text class="result-value">{{apiTest.statusCode}}</text>
    </view>
    
    <view class="result-item" wx:if="{{apiTest.contentType}}">
      <text class="result-label">内容类型:</text>
      <text class="result-value">{{apiTest.contentType}}</text>
    </view>
    
    <view class="result-item">
      <text class="result-label">消息:</text>
      <text class="result-value">{{apiTest.message}}</text>
    </view>
    
    <!-- 错误详情 -->
    <view class="error-detail" wx:if="{{!apiTest.success && apiTest.error}}">
      <text class="error-title">错误详情:</text>
      <text class="error-text">{{apiTest.error.errMsg || '未知错误'}}</text>
    </view>
  </view>

  <!-- 修复建议 -->
  <view class="recommendations-section" wx:if="{{recommendations.length > 0}}">
    <view class="section-title">💡 修复建议</view>
    
    <view class="recommendation-item" wx:for="{{recommendations}}" wx:key="index">
      <text class="recommendation-text">{{item}}</text>
    </view>
  </view>

  <!-- 详细信息 -->
  <view class="details-section" wx:if="{{testResults && showDetails}}">
    <view class="section-title" bindtap="toggleDetails">
      📊 详细信息 {{showDetails ? '▼' : '▶'}}
    </view>
    
    <view class="details-content">
      <text class="details-text">{{JSON.stringify(testResults, null, 2)}}</text>
    </view>
  </view>

  <!-- 配置说明 -->
  <view class="instructions-section">
    <view class="section-title" bindtap="toggleInstructions">
      📖 配置说明 {{showInstructions ? '▼' : '▶'}}
    </view>
    
    <view class="instructions-content" wx:if="{{showInstructions}}">
      <view class="instruction-item">
        <text class="instruction-title">🔑 高德地图API密钥配置</text>
        <text class="instruction-text">
          1. 登录高德开放平台控制台
          2. 检查API密钥状态和配额
          3. 确认服务权限配置正确
          4. 检查安全密钥设置
        </text>
        <button class="link-btn" bindtap="openAmapConsole">打开高德控制台</button>
      </view>
      
      <view class="instruction-item">
        <text class="instruction-title">🌐 微信小程序域名配置</text>
        <text class="instruction-text">
          1. 登录微信公众平台
          2. 进入开发设置
          3. 添加request合法域名：
             - https://restapi.amap.com
             - https://webapi.amap.com
        </text>
        <button class="link-btn" bindtap="openWechatConsole">打开微信公众平台</button>
      </view>
      
      <view class="instruction-item">
        <text class="instruction-title">🛠️ 开发阶段临时解决方案</text>
        <text class="instruction-text">
          在微信开发者工具中：
          1. 点击右上角"详情"
          2. 选择"本地设置"
          3. 勾选"不校验合法域名..."
          4. 重新编译项目
        </text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="action-btn" bindtap="retryTest">🔄 重新测试</button>
    <button class="action-btn secondary" bindtap="goBack">← 返回</button>
  </view>
</view>
