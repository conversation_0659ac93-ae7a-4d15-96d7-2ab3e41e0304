/* api-test.wxss - API测试页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 通用区域样式 */
.config-section,
.check-section,
.result-section,
.recommendations-section,
.details-section,
.instructions-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 配置显示 */
.config-item {
  margin-bottom: 20rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.value-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.value {
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
  flex: 1;
  margin-right: 20rpx;
}

.value.small {
  font-size: 22rpx;
}

.copy-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  min-width: 100rpx;
}

/* 检查结果 */
.check-item,
.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.check-item:last-child,
.result-item:last-child {
  border-bottom: none;
}

.check-label,
.result-label {
  font-size: 28rpx;
  color: #666;
}

.check-value,
.result-value {
  font-size: 28rpx;
  font-weight: bold;
}

.check-value.success,
.result-value.success {
  color: #4caf50;
}

.check-value.error,
.result-value.error {
  color: #f44336;
}

.check-value.warning {
  color: #ff9800;
}

/* 状态标签 */
.status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: bold;
}

.status.success {
  background: #e8f5e8;
  color: #4caf50;
}

.status.error {
  background: #ffeaea;
  color: #f44336;
}

/* 按钮区域 */
.button-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin: 30rpx 0;
}

.test-btn {
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}

.test-btn.primary {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
}

.test-btn.secondary {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
}

.test-btn:disabled {
  background: #ccc !important;
  color: #999 !important;
}

/* 错误详情 */
.error-detail {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #ffeaea;
  border-radius: 10rpx;
  border-left: 4rpx solid #f44336;
}

.error-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #f44336;
  margin-bottom: 10rpx;
}

.error-text {
  font-size: 24rpx;
  color: #d32f2f;
  word-break: break-all;
}

/* 建议列表 */
.recommendation-item {
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.recommendation-item:last-child {
  border-bottom: none;
}

.recommendation-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 详细信息 */
.details-content {
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-top: 15rpx;
}

.details-text {
  font-size: 22rpx;
  color: #666;
  font-family: monospace;
  word-break: break-all;
  white-space: pre-wrap;
}

/* 配置说明 */
.instructions-content {
  margin-top: 15rpx;
}

.instruction-item {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #007aff;
}

.instruction-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.instruction-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15rpx;
}

.link-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 15rpx 25rpx;
  font-size: 26rpx;
}

/* 操作按钮 */
.action-section {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.action-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
}

.action-btn.secondary {
  background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
}
