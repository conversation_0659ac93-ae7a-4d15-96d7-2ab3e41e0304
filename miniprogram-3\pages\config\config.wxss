/* config.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.content {
  margin-bottom: 100rpx;
}

.section {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* API密钥显示 */
.key-display {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}

.key-text {
  flex: 1;
  font-family: monospace;
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
}

.copy-btn {
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}

/* 错误信息 */
.error-info {
  background: #ffebee;
  border: 1rpx solid #f44336;
  border-radius: 8rpx;
  padding: 20rpx;
}

.error-text {
  color: #f44336;
  font-size: 28rpx;
}

.no-error {
  text-align: center;
  padding: 20rpx;
}

.success-text {
  color: #4caf50;
  font-size: 28rpx;
}

/* 配置步骤 */
.steps {
  margin-top: 20rpx;
}

.step {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.step:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: #007aff;
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 40rpx;
  font-size: 24rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.step-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 40rpx;
}

/* 域名列表 */
.domain-list {
  margin-top: 20rpx;
}

.domain-item {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 10rpx;
}

.domain-item:last-child {
  margin-bottom: 0;
}

.domain-text {
  flex: 1;
  font-family: monospace;
  font-size: 26rpx;
  color: #333;
}

/* 测试按钮 */
.test-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.test-btn {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.test-btn:active {
  opacity: 0.8;
}

/* 测试结果 */
.test-result {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.result-text {
  font-size: 28rpx;
  font-weight: bold;
}

.result-text.success {
  color: #4caf50;
}

.result-text.error {
  color: #f44336;
}

.result-detail {
  margin-top: 10rpx;
  padding-top: 10rpx;
  border-top: 1rpx solid #eee;
}

.detail-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 底部按钮 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
}

.back-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
}
