// index.js
const amapManager = require('../../utils/amapManager.js'); // 引入SDK管理器
const CONFIG = require('../../utils/config.js'); // 引入配置文件
const staticMapConfig = require('../../utils/staticMapConfig.js'); // 引入静态地图配置工具
const errorHandler = require('../../utils/errorHandler.js'); // 引入错误处理工具

Page({
  data: {
    staticMapUrl: '', // 静态地图URL
    mapStatus: '正在初始化...', // 地图状态
    locationInfo: null, // 位置信息
    // 地图范围选择
    mapRanges: [
      { key: 'city', name: '城市级别', zoom: 10, description: '显示整个城市范围' },
      { key: 'district', name: '区域级别', zoom: 13, description: '显示区域范围' },
      { key: 'street', name: '街道级别', zoom: 15, description: '显示街道详情' },
      { key: 'building', name: '建筑级别', zoom: 17, description: '显示建筑物详情' }
    ],
    mapRangeIndex: 1, // 默认选择区域级别
  },
  onLoad: function() {
    const that = this;
    
    // 使用统一的SDK管理器
    amapManager.testConnection()
      .then(result => {
        console.log('SDK连接测试成功:', result);
        // 页面加载时自动获取静态地图
        that.getStaticMap();
      })
      .catch(error => {
        console.error('SDK连接测试失败:', error);
        
        // 使用错误处理工具显示友好错误信息
        errorHandler.showErrorToast(error);
        
        that.setData({
          mapStatus: 'SDK连接失败，使用默认地图'
        });
        
        // 即使SDK连接失败，也尝试显示默认静态地图
        that.showDefaultStaticMap();
      });
  },

  // 显示默认静态地图
  showDefaultStaticMap: function() {
    const defaultUrl = staticMapConfig.buildStandardStaticMapUrl({
      location: staticMapConfig.STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN,
      zoom: 15,
      size: '400*400',
      scale: 2,
      markers: staticMapConfig.STATIC_MAP_CONFIG.MARKER_STYLES.RED_A + ':' + staticMapConfig.STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN
    });

    this.setData({
      staticMapUrl: defaultUrl,
      mapStatus: '显示默认位置地图（北京天安门）',
      locationInfo: {
        address: '北京市天安门广场（默认位置）',
        longitude: '116.397428',
        latitude: '39.909230'
      }
    });

    console.log('默认静态地图URL:', defaultUrl);
  },

  // 获取静态地图
  getStaticMap: function() {
    const that = this;

    that.setData({
      mapStatus: '正在获取高清静态地图...'
    });

    // 获取当前选择的地图范围
    const selectedRange = this.data.mapRanges[this.data.mapRangeIndex];

    // 使用新的静态地图配置工具获取地图，传入自定义缩放级别
    staticMapConfig.getCurrentLocationMap({
      zoom: selectedRange.zoom,  // 使用用户选择的缩放级别
      size: '512*400',           // 基础尺寸，scale=2时会变为1024*800
      scale: 2                   // 高清图
    })
      .then(result => {
        console.log('静态地图获取成功:', result);

        that.setData({
          staticMapUrl: result.mapUrl,
          mapStatus: `${result.message}（${selectedRange.description}）`,
          locationInfo: {
            address: result.isDefault ? '北京市天安门广场（默认位置）' : '当前位置',
            longitude: result.location.longitude.toFixed(6),
            latitude: result.location.latitude.toFixed(6)
          }
        });

        // 测试URL是否有效
        staticMapConfig.testStaticMapUrl(result.mapUrl)
          .then(testResult => {
            console.log('静态地图URL测试成功:', testResult);
            that.setData({
              mapStatus: `高清地图加载成功（${selectedRange.description}）`
            });

            wx.showToast({
              title: '高清地图加载成功',
              icon: 'success'
            });
          })
          .catch(testError => {
            console.error('静态地图URL测试失败:', testError);
            that.setData({
              mapStatus: '地图URL无效: ' + testError.message
            });

            wx.showModal({
              title: '地图API错误',
              content: `${testError.message}\n\n请检查:\n1. API密钥是否正确\n2. 域名是否已配置\n3. 网络连接状态`,
              showCancel: false
            });
          });

        // 如果使用了默认位置，获取详细地址信息
        if (!result.isDefault) {
          that.getLocationInfo();
        }
      })
      .catch(error => {
        console.error('静态地图获取失败:', error);
        that.setData({
          mapStatus: '地图获取失败: ' + (error.message || '未知错误')
        });

        wx.showModal({
          title: '地图获取失败',
          content: `错误信息: ${error.message}\n\n可能原因:\n1. API密钥无效\n2. 网络连接问题\n3. 定位权限未授权`,
          showCancel: true,
          cancelText: '忽略',
          confirmText: '重试',
          success: function(res) {
            if (res.confirm) {
              that.getStaticMap();
            }
          }
        });
      });
  },

  // 获取位置信息
  getLocationInfo: function() {
    const that = this;

    // 使用SDK管理器安全调用getRegeo方法
    amapManager.safeCall('getRegeo')
      .then(result => {
        console.log('位置信息获取成功:', result);
        
        if (result.data && result.data.length > 0) {
          const locationData = result.data[0];
          that.setData({
            locationInfo: {
              address: locationData.name || '未知位置',
              longitude: locationData.longitude.toFixed(6),
              latitude: locationData.latitude.toFixed(6)
            }
          });
        }
      })
      .catch(error => {
        console.error('位置信息获取失败:', error);
        // 不影响主要功能，只记录错误
      });
  },

  // 刷新静态地图
  refreshStaticMap: function() {
    this.setData({
      staticMapUrl: '',
      mapStatus: '正在刷新...'
    });
    this.getStaticMap();
  },

  // 地图加载完成
  onMapLoad: function() {
    console.log('静态地图加载完成');
    this.setData({
      mapStatus: '地图显示正常'
    });
  },

  // 地图加载错误
  onMapError: function(e) {
    console.error('静态地图加载错误:', e);
    this.setData({
      mapStatus: '地图显示失败',
      staticMapUrl: ''
    });
  },

  bindViewTap() {
    wx.navigateTo({
      url: '../logs/logs'
    })
  },

  // 跳转到定位页面
  goToLocation() {
    wx.navigateTo({
      url: '../location/location'
    })
  },

  // 跳转到配置页面
  goToConfig() {
    wx.navigateTo({
      url: '../config/config'
    })
  },

  // 跳转到导航页面
  goToNavigation() {
    wx.navigateTo({
      url: '../navigation/navigation'
    })
  },

  // 跳转到配置指南页面
  goToSetup() {
    wx.navigateTo({
      url: '../setup/setup'
    })
  },

  // 地图范围改变
  onMapRangeChange: function(e) {
    const newIndex = parseInt(e.detail.value);
    const selectedRange = this.data.mapRanges[newIndex];

    this.setData({
      mapRangeIndex: newIndex,
      mapStatus: `正在切换到${selectedRange.description}...`
    });

    // 重新获取地图
    this.getStaticMap();

    wx.showToast({
      title: `已切换到${selectedRange.name}`,
      icon: 'success'
    });
  },

  // 测试静态地图URL
  testStaticMapUrl: function() {
    const that = this;

    // 创建多个测试示例
    const examples = staticMapConfig.createMapExamples();

    wx.showActionSheet({
      itemList: examples.map(item => item.name),
      success: function(res) {
        const selectedExample = examples[res.tapIndex];
        const testUrl = selectedExample.url;

        console.log('测试静态地图URL:', testUrl);

        wx.showModal({
          title: `${selectedExample.name} - URL测试`,
          content: `生成的URL:\n${testUrl}\n\n是否复制到剪贴板并测试？`,
          showCancel: true,
          cancelText: '取消',
          confirmText: '测试',
          success: function(modalRes) {
            if (modalRes.confirm) {
              // 复制URL
              wx.setClipboardData({
                data: testUrl,
                success: function() {
                  wx.showToast({
                    title: 'URL已复制',
                    icon: 'success'
                  });
                }
              });

              // 设置地图并测试
              that.setData({
                staticMapUrl: testUrl,
                mapStatus: `正在测试${selectedExample.name}...`
              });

              // 测试URL有效性
              staticMapConfig.testStaticMapUrl(testUrl)
                .then(result => {
                  that.setData({
                    mapStatus: `${selectedExample.name}测试成功`
                  });

                  wx.showToast({
                    title: '地图测试成功',
                    icon: 'success'
                  });
                })
                .catch(error => {
                  that.setData({
                    mapStatus: `${selectedExample.name}测试失败: ${error.message}`
                  });

                  wx.showToast({
                    title: '地图测试失败',
                    icon: 'none'
                  });
                });
            }
          }
        });
      }
    });
  },

  // 页面卸载时清理资源
  onUnload: function() {
    // 清理SDK实例
    amapManager.destroyInstance();
    console.log('首页资源清理完成');
  }
})
