// index.js
const amapManager = require('../../utils/amapManager.js'); // 引入SDK管理器
const CONFIG = require('../../utils/config.js'); // 引入配置文件
const staticMapConfig = require('../../utils/staticMapConfig.js'); // 引入静态地图配置工具
const errorHandler = require('../../utils/errorHandler.js'); // 引入错误处理工具
const vehicleDataManager = require('../../utils/vehicleDataManager.js'); // 引入车辆数据管理器

Page({
  data: {
    staticMapUrl: '', // 静态地图URL
    mapStatus: '正在初始化...', // 地图状态
    locationInfo: null, // 位置信息

    // 天气数据
    weatherData: {
      icon: '☀️',
      temperature: '--',
      weather: '晴天'
    },

    // 车辆数据
    vehicleData: {
      battery: { level: 0 },
      speed: { current: 0 },
      connected: false,
      lastUpdate: null
    },

    // 当前骑行模式
    currentMode: {
      code: 2,
      name: '成人模式'
    },

    // 连接状态
    connectionStatus: {
      connected: false,
      statusText: '未连接',
      statusColor: '#999'
    },

    // 警告信息
    warnings: [],

    // 模式选择弹窗
    showModeModal: false,

    // 地图范围选择
    mapRanges: [
      { key: 'city', name: '城市级别', zoom: 10, description: '显示整个城市范围' },
      { key: 'district', name: '区域级别', zoom: 13, description: '显示区域范围' },
      { key: 'street', name: '街道级别', zoom: 15, description: '显示街道详情' },
      { key: 'building', name: '建筑级别', zoom: 17, description: '显示建筑物详情' }
    ],
    mapRangeIndex: 1, // 默认选择区域级别
  },
  onLoad: function() {
    const that = this;

    // 初始化车辆数据管理器
    this.initVehicleDataManager();

    // 获取天气信息
    this.getWeatherInfo();

    // 设置天气信息定期更新（每30分钟更新一次）
    this.weatherUpdateInterval = setInterval(() => {
      that.getWeatherInfo();
    }, 30 * 60 * 1000); // 30分钟

    // 使用统一的SDK管理器
    amapManager.testConnection()
      .then(result => {
        console.log('SDK连接测试成功:', result);
        // 页面加载时自动获取静态地图
        that.getStaticMap();
      })
      .catch(error => {
        console.error('SDK连接测试失败:', error);

        // 使用错误处理工具显示友好错误信息
        errorHandler.showErrorToast(error);

        that.setData({
          mapStatus: 'SDK连接失败，使用默认地图'
        });

        // 即使SDK连接失败，也尝试显示默认静态地图
        that.showDefaultStaticMap();
      });
  },

  // 显示默认静态地图
  showDefaultStaticMap: function() {
    // 检查当前车辆模式，决定使用哪种地图配置
    const currentMode = this.data.currentMode;
    const isElectrobikeMode = currentMode && (currentMode.code === 1 || currentMode.code === 2 || currentMode.code === 3);

    let defaultUrl;
    let mapStatus;

    if (isElectrobikeMode) {
      // 使用电动车专用地图配置
      defaultUrl = staticMapConfig.buildElectrobikeStaticMapUrl({
        location: staticMapConfig.STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN,
        startPoint: staticMapConfig.STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN,
        zoom: staticMapConfig.STATIC_MAP_CONFIG.ELECTROBIKE_DEFAULTS.zoom,
        size: staticMapConfig.STATIC_MAP_CONFIG.ELECTROBIKE_DEFAULTS.size,
        scale: staticMapConfig.STATIC_MAP_CONFIG.ELECTROBIKE_DEFAULTS.scale,
        traffic: staticMapConfig.STATIC_MAP_CONFIG.ELECTROBIKE_DEFAULTS.traffic
      });
      mapStatus = '显示电动车专用默认地图（北京天安门）';
    } else {
      // 使用标准地图配置
      defaultUrl = staticMapConfig.buildStandardStaticMapUrl({
        location: staticMapConfig.STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN,
        zoom: staticMapConfig.STATIC_MAP_CONFIG.DEFAULTS.zoom,
        size: staticMapConfig.STATIC_MAP_CONFIG.DEFAULTS.size,
        scale: staticMapConfig.STATIC_MAP_CONFIG.DEFAULTS.scale,
        markers: staticMapConfig.STATIC_MAP_CONFIG.MARKER_STYLES.RED_A + ':' + staticMapConfig.STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN
      });
      mapStatus = '显示默认位置地图（北京天安门）';
    }

    this.setData({
      staticMapUrl: defaultUrl,
      mapStatus: mapStatus,
      locationInfo: {
        address: '北京市天安门广场（默认位置）',
        longitude: '116.397428',
        latitude: '39.909230'
      }
    });

    console.log('默认静态地图URL:', defaultUrl);
    console.log('地图模式:', isElectrobikeMode ? '电动车专用' : '标准模式');
  },

  // 获取静态地图
  getStaticMap: function() {
    const that = this;

    that.setData({
      mapStatus: '正在获取高清静态地图...'
    });

    // 获取当前选择的地图范围
    const selectedRange = this.data.mapRanges[this.data.mapRangeIndex];

    // 检查当前车辆模式，决定使用哪种地图配置
    const currentMode = this.data.currentMode;
    const isElectrobikeMode = currentMode && (currentMode.code === 1 || currentMode.code === 2 || currentMode.code === 3);

    // 根据车辆模式选择地图配置
    const mapOptions = {
      zoom: selectedRange.zoom,
      size: staticMapConfig.STATIC_MAP_CONFIG.DEFAULTS.size, // 使用优化后的尺寸
      scale: 2
    };

    // 如果是电动车模式，使用电动车专用配置
    if (isElectrobikeMode) {
      mapOptions.zoom = staticMapConfig.STATIC_MAP_CONFIG.ELECTROBIKE_DEFAULTS.zoom;
      mapOptions.traffic = staticMapConfig.STATIC_MAP_CONFIG.ELECTROBIKE_DEFAULTS.traffic;
      that.setData({
        mapStatus: '正在获取电动车专用高清地图...'
      });
    }

    // 使用新的静态地图配置工具获取地图
    staticMapConfig.getCurrentLocationMap(mapOptions)
      .then(result => {
        console.log('静态地图获取成功:', result);

        that.setData({
          staticMapUrl: result.mapUrl,
          mapStatus: `${result.message}（${selectedRange.description}）`,
          locationInfo: {
            address: result.isDefault ? '北京市天安门广场（默认位置）' : '当前位置',
            longitude: result.location.longitude.toFixed(6),
            latitude: result.location.latitude.toFixed(6)
          }
        });

        // 测试URL是否有效
        staticMapConfig.testStaticMapUrl(result.mapUrl)
          .then(testResult => {
            console.log('静态地图URL测试成功:', testResult);
            that.setData({
              mapStatus: `高清地图加载成功（${selectedRange.description}）`
            });

            wx.showToast({
              title: '高清地图加载成功',
              icon: 'success'
            });
          })
          .catch(testError => {
            console.error('静态地图URL测试失败:', testError);
            that.setData({
              mapStatus: '地图URL无效: ' + testError.message
            });

            wx.showModal({
              title: '地图API错误',
              content: `${testError.message}\n\n请检查:\n1. API密钥是否正确\n2. 域名是否已配置\n3. 网络连接状态`,
              showCancel: false
            });
          });

        // 如果使用了默认位置，获取详细地址信息
        if (!result.isDefault) {
          that.getLocationInfo();
        }
      })
      .catch(error => {
        console.error('静态地图获取失败:', error);
        that.setData({
          mapStatus: '地图获取失败: ' + (error.message || '未知错误')
        });

        wx.showModal({
          title: '地图获取失败',
          content: `错误信息: ${error.message}\n\n可能原因:\n1. API密钥无效\n2. 网络连接问题\n3. 定位权限未授权`,
          showCancel: true,
          cancelText: '忽略',
          confirmText: '重试',
          success: function(res) {
            if (res.confirm) {
              that.getStaticMap();
            }
          }
        });
      });
  },

  // 获取位置信息
  getLocationInfo: function() {
    const that = this;

    // 先获取当前位置
    wx.getLocation({
      type: 'gcj02',
      success: function(res) {
        console.log('获取位置成功:', res);

        // 使用Web服务API进行逆地理编码
        that.reverseGeocode(res.longitude, res.latitude);
      },
      fail: function(error) {
        console.error('获取位置失败:', error);
        that.setData({
          locationInfo: {
            address: '位置获取失败，请检查权限设置',
            longitude: '0.000000',
            latitude: '0.000000'
          }
        });
      }
    });
  },

  // 逆地理编码（使用Web服务API）
  reverseGeocode: function(longitude, latitude) {
    const that = this;
    const CONFIG = require('../../utils/config.js');

    const url = `https://restapi.amap.com/v3/geocode/regeo?key=${CONFIG.AMAP_WEB_KEY}&location=${longitude},${latitude}&output=json&radius=1000&extensions=all`;

    wx.request({
      url: url,
      method: 'GET',
      success: function(res) {
        console.log('逆地理编码成功:', res.data);

        if (res.data.status === '1' && res.data.regeocode) {
          const regeocode = res.data.regeocode;
          const addressComponent = regeocode.addressComponent;

          that.setData({
            locationInfo: {
              address: regeocode.formatted_address || '未知位置',
              longitude: longitude.toFixed(6),
              latitude: latitude.toFixed(6)
            }
          });
        } else {
          console.error('逆地理编码失败:', res.data);
          that.setData({
            locationInfo: {
              address: `地址解析失败 (${longitude.toFixed(6)}, ${latitude.toFixed(6)})`,
              longitude: longitude.toFixed(6),
              latitude: latitude.toFixed(6)
            }
          });
        }
      },
      fail: function(error) {
        console.error('逆地理编码请求失败:', error);
        that.setData({
          locationInfo: {
            address: `网络请求失败 (${longitude.toFixed(6)}, ${latitude.toFixed(6)})`,
            longitude: longitude.toFixed(6),
            latitude: latitude.toFixed(6)
          }
        });
      }
    });
  },

  // 刷新静态地图
  refreshStaticMap: function() {
    this.setData({
      staticMapUrl: '',
      mapStatus: '正在刷新...'
    });
    this.getStaticMap();
  },

  // 地图加载完成
  onMapLoad: function() {
    console.log('静态地图加载完成');
    this.setData({
      mapStatus: '地图显示正常'
    });
  },

  // 地图加载错误
  onMapError: function(e) {
    console.error('静态地图加载错误:', e);
    this.setData({
      mapStatus: '地图显示失败',
      staticMapUrl: ''
    });
  },

  bindViewTap() {
    wx.navigateTo({
      url: '../logs/logs'
    })
  },

  // 跳转到定位页面
  goToLocation() {
    wx.navigateTo({
      url: '../location/location'
    })
  },

  // 跳转到配置页面
  goToConfig() {
    wx.navigateTo({
      url: '../config/config'
    })
  },

  // 跳转到导航页面
  goToNavigation() {
    wx.navigateTo({
      url: '../navigation/navigation'
    })
  },

  // 跳转到配置指南页面
  goToSetup() {
    wx.navigateTo({
      url: '../setup/setup'
    })
  },

  // 跳转到API诊断页面
  goToApiTest() {
    wx.navigateTo({
      url: '../api-test/api-test'
    })
  },

  // 地图范围改变
  onMapRangeChange: function(e) {
    const newIndex = parseInt(e.detail.value);
    const selectedRange = this.data.mapRanges[newIndex];

    this.setData({
      mapRangeIndex: newIndex,
      mapStatus: `正在切换到${selectedRange.description}...`
    });

    // 重新获取地图
    this.getStaticMap();

    wx.showToast({
      title: `已切换到${selectedRange.name}`,
      icon: 'success'
    });
  },

  // 测试静态地图URL
  testStaticMapUrl: function() {
    const that = this;

    // 创建多个测试示例
    const examples = staticMapConfig.createMapExamples();

    wx.showActionSheet({
      itemList: examples.map(item => item.name),
      success: function(res) {
        const selectedExample = examples[res.tapIndex];
        const testUrl = selectedExample.url;

        console.log('测试静态地图URL:', testUrl);

        wx.showModal({
          title: `${selectedExample.name} - URL测试`,
          content: `生成的URL:\n${testUrl}\n\n是否复制到剪贴板并测试？`,
          showCancel: true,
          cancelText: '取消',
          confirmText: '测试',
          success: function(modalRes) {
            if (modalRes.confirm) {
              // 复制URL
              wx.setClipboardData({
                data: testUrl,
                success: function() {
                  wx.showToast({
                    title: 'URL已复制',
                    icon: 'success'
                  });
                }
              });

              // 设置地图并测试
              that.setData({
                staticMapUrl: testUrl,
                mapStatus: `正在测试${selectedExample.name}...`
              });

              // 测试URL有效性
              staticMapConfig.testStaticMapUrl(testUrl)
                .then(result => {
                  that.setData({
                    mapStatus: `${selectedExample.name}测试成功`
                  });

                  wx.showToast({
                    title: '地图测试成功',
                    icon: 'success'
                  });
                })
                .catch(error => {
                  that.setData({
                    mapStatus: `${selectedExample.name}测试失败: ${error.message}`
                  });

                  wx.showToast({
                    title: '地图测试失败',
                    icon: 'none'
                  });
                });
            }
          }
        });
      }
    });
  },

  // 初始化车辆数据管理器
  initVehicleDataManager: function() {
    const that = this;

    // 初始化车辆数据管理器
    vehicleDataManager.initialize()
      .then(result => {
        console.log('车辆数据管理器初始化成功:', result);

        // 添加数据监听器
        vehicleDataManager.addDataListener('battery', (data) => {
          that.updateBatteryDisplay(data);
        });

        vehicleDataManager.addDataListener('speed', (data) => {
          that.updateSpeedDisplay(data);
        });

        vehicleDataManager.addDataListener('mode', (data) => {
          that.updateModeDisplay(data);
        });

        // 添加连接状态监听器
        vehicleDataManager.addDataListener('connection', (data) => {
          that.updateConnectionStatus(data);
        });

        // 添加警告监听器
        vehicleDataManager.addDataListener('warning', (warning) => {
          that.handleVehicleWarning(warning);
        });

        // 获取初始车辆数据
        const initialData = vehicleDataManager.getVehicleData();
        const currentModeConfig = initialData.modes[initialData.mode.current];

        that.setData({
          vehicleData: {
            battery: initialData.battery,
            speed: initialData.speed,
            connected: initialData.connected,
            lastUpdate: initialData.lastUpdate
          },
          currentMode: {
            code: initialData.mode.current,
            name: currentModeConfig.name
          },
          connectionStatus: {
            connected: initialData.connected,
            statusText: initialData.connected ? '已连接' : '未连接',
            statusColor: initialData.connected ? '#52c41a' : '#999'
          }
        });
      })
      .catch(error => {
        console.error('车辆数据管理器初始化失败:', error);
        // 设置默认数据
        that.setData({
          vehicleData: {
            battery: { level: 85 },
            speed: { current: 0 },
            connected: false,
            lastUpdate: null
          },
          currentMode: {
            code: 2,
            name: '成人模式'
          },
          connectionStatus: {
            connected: false,
            statusText: '连接失败',
            statusColor: '#ff4d4f'
          }
        });

        // 显示连接失败提示
        wx.showToast({
          title: '车辆连接失败',
          icon: 'error',
          duration: 2000
        });
      });
  },

  // 获取天气信息
  getWeatherInfo: function() {
    const that = this;

    // 使用SDK管理器安全调用getWeather方法
    amapManager.safeCall('getWeather')
      .then(result => {
        console.log('天气信息获取成功:', result);

        const weatherData = result.data;
        const weatherIcon = that.getWeatherIcon(weatherData.weather);

        that.setData({
          weatherData: {
            icon: weatherIcon,
            temperature: weatherData.temperature,
            weather: weatherData.weather
          }
        });
      })
      .catch(error => {
        console.error('天气信息获取失败:', error);
        // 设置默认天气数据
        that.setData({
          weatherData: {
            icon: '☀️',
            temperature: '22',
            weather: '晴天'
          }
        });
      });
  },

  // 获取天气图标
  getWeatherIcon: function(weather) {
    const weatherIcons = {
      '晴': '☀️',
      '多云': '⛅',
      '少云': '🌤️',
      '阴': '☁️',
      '雨': '🌧️',
      '小雨': '🌦️',
      '中雨': '🌧️',
      '大雨': '⛈️',
      '暴雨': '⛈️',
      '雪': '❄️',
      '小雪': '🌨️',
      '中雪': '❄️',
      '大雪': '❄️',
      '雾': '🌫️',
      '霾': '😷',
      '沙尘': '🌪️',
      '风': '💨'
    };

    if (!weather) return '☀️';

    // 优先匹配更具体的天气描述
    const sortedKeys = Object.keys(weatherIcons).sort((a, b) => b.length - a.length);

    for (let key of sortedKeys) {
      if (weather.includes(key)) {
        return weatherIcons[key];
      }
    }

    return '☀️'; // 默认晴天图标
  },

  // 手动刷新天气信息
  refreshWeather: function() {
    wx.showToast({
      title: '刷新天气中...',
      icon: 'loading',
      duration: 1000
    });

    this.getWeatherInfo();
  },

  // 显示模式选择器
  showModeSelector: function() {
    this.setData({
      showModeModal: true
    });
  },

  // 隐藏模式选择器
  hideModeSelector: function() {
    this.setData({
      showModeModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止事件冒泡，防止点击模态框内容时关闭弹窗
  },

  // 选择骑行模式
  selectMode: function(e) {
    const mode = parseInt(e.currentTarget.dataset.mode);
    const that = this;

    wx.showLoading({
      title: '切换模式中...'
    });

    vehicleDataManager.switchDrivingMode(mode)
      .then(result => {
        console.log('模式切换成功:', result);

        that.setData({
          currentMode: {
            code: mode,
            name: result.config.name
          },
          showModeModal: false
        });

        // 模式切换后自动刷新地图以应用新的显示样式
        that.refreshStaticMap();

        wx.hideLoading();
        wx.showToast({
          title: `已切换到${result.config.name}`,
          icon: 'success'
        });
      })
      .catch(error => {
        console.error('模式切换失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '模式切换失败',
          icon: 'error'
        });
      });
  },

  // 更新电池显示
  updateBatteryDisplay: function(data) {
    this.setData({
      'vehicleData.battery': data,
      'vehicleData.lastUpdate': new Date()
    });

    console.log('电池信息更新:', data);
  },

  // 更新速度显示
  updateSpeedDisplay: function(data) {
    this.setData({
      'vehicleData.speed': data,
      'vehicleData.lastUpdate': new Date()
    });

    console.log('速度信息更新:', data);
  },

  // 更新驾驶模式显示
  updateModeDisplay: function(data) {
    const modeConfig = vehicleDataManager.DRIVING_MODES[data.mode];
    this.setData({
      currentMode: {
        code: data.mode,
        name: modeConfig.name
      }
    });

    console.log('驾驶模式更新:', data);
  },

  // 更新连接状态
  updateConnectionStatus: function(data) {
    const statusText = data.connected ? '已连接' : '连接断开';
    const statusColor = data.connected ? '#52c41a' : '#ff4d4f';

    this.setData({
      'vehicleData.connected': data.connected,
      connectionStatus: {
        connected: data.connected,
        statusText: statusText,
        statusColor: statusColor
      }
    });

    console.log('连接状态更新:', data);

    // 显示连接状态变化提示
    if (data.connected) {
      wx.showToast({
        title: '车辆已连接',
        icon: 'success',
        duration: 1500
      });
    } else {
      wx.showToast({
        title: '车辆连接断开',
        icon: 'error',
        duration: 2000
      });
    }
  },

  // 处理车辆警告
  handleVehicleWarning: function(warning) {
    console.log('收到车辆警告:', warning);

    // 添加到警告列表
    const warnings = this.data.warnings;
    warnings.unshift({
      ...warning,
      timestamp: new Date(),
      id: Date.now()
    });

    // 只保留最近10条警告
    if (warnings.length > 10) {
      warnings.splice(10);
    }

    this.setData({
      warnings: warnings
    });

    // 显示警告提示
    wx.showModal({
      title: '车辆警告',
      content: warning.message,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 跳转到导航页面（传递当前位置和车辆状态）
  goToNavigation: function() {
    const currentLocation = this.data.locationInfo;
    const currentMode = this.data.currentMode;
    const vehicleConnected = this.data.vehicleData.connected;

    // 参数验证
    if (!currentMode || !currentMode.code) {
      wx.showToast({
        title: '请先选择骑行模式',
        icon: 'none'
      });
      return;
    }

    // 检查位置信息
    if (!currentLocation || !currentLocation.longitude || !currentLocation.latitude) {
      wx.showModal({
        title: '位置信息缺失',
        content: '无法获取当前位置，导航页面将尝试重新定位，是否继续？',
        success: (res) => {
          if (res.confirm) {
            this.navigateToNavigation(null, currentMode);
          }
        }
      });
      return;
    }

    // 检查车辆连接状态
    if (!vehicleConnected) {
      wx.showModal({
        title: '提示',
        content: '车辆未连接，是否继续导航？',
        success: (res) => {
          if (res.confirm) {
            this.navigateToNavigation(currentLocation, currentMode);
          }
        }
      });
      return;
    }

    this.navigateToNavigation(currentLocation, currentMode);
  },

  // 执行导航跳转
  navigateToNavigation: function(location, mode) {
    let url = '../navigation/navigation';
    let params = [];

    // 添加位置参数
    if (location && location.longitude && location.latitude) {
      params.push(`longitude=${location.longitude}`);
      params.push(`latitude=${location.latitude}`);
      console.log('传递起点位置:', location);
    }

    // 添加车辆模式参数
    if (mode && mode.code) {
      params.push(`mode=${mode.code}`);
      console.log('传递车辆模式:', mode);
    }

    // 添加车辆连接状态参数
    const vehicleConnected = this.data.vehicleData.connected;
    params.push(`connected=${vehicleConnected ? 1 : 0}`);

    // 添加车辆电量参数（如果有）
    const batteryLevel = this.data.vehicleData.battery.level;
    if (batteryLevel !== undefined) {
      params.push(`battery=${batteryLevel}`);
    }

    // 构建完整URL
    if (params.length > 0) {
      url += '?' + params.join('&');
    }

    console.log('导航跳转URL:', url);

    wx.navigateTo({
      url: url,
      success: function() {
        console.log('导航页面跳转成功');
      },
      fail: function(error) {
        console.error('导航页面跳转失败:', error);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  // 测试车辆数据功能
  testVehicleData: function() {
    const that = this;

    wx.showLoading({
      title: '测试车辆数据...'
    });

    // 模拟车辆数据更新
    setTimeout(() => {
      // 模拟电池数据更新
      that.updateBatteryDisplay({
        level: Math.floor(Math.random() * 100),
        voltage: 48.5,
        current: 2.3,
        temperature: 25,
        charging: false
      });

      // 模拟速度数据更新
      that.updateSpeedDisplay({
        current: Math.floor(Math.random() * 30),
        max: 25,
        average: 18
      });

      // 模拟连接状态更新
      that.updateConnectionStatus({
        connected: true
      });

      wx.hideLoading();
      wx.showToast({
        title: '测试数据已更新',
        icon: 'success'
      });
    }, 1000);

    // 模拟警告测试
    setTimeout(() => {
      that.handleVehicleWarning({
        type: 'test_warning',
        message: '这是一个测试警告信息'
      });
    }, 2000);
  },

  // 页面卸载时清理资源
  onUnload: function() {
    // 清理车辆数据监听器
    vehicleDataManager.removeDataListener('battery');
    vehicleDataManager.removeDataListener('speed');
    vehicleDataManager.removeDataListener('mode');
    vehicleDataManager.removeDataListener('connection');
    vehicleDataManager.removeDataListener('warning');

    // 清理天气更新定时器
    if (this.weatherUpdateInterval) {
      clearInterval(this.weatherUpdateInterval);
      this.weatherUpdateInterval = null;
    }

    // 清理SDK实例
    amapManager.destroyInstance();
    console.log('首页资源清理完成');
  }
})
