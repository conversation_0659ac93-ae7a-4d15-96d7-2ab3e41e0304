<!--index.wxml-->
<view class="container">
  <!-- 顶部信息栏 -->
  <view class="top-info-bar">
    <!-- 天气信息(左) -->
    <view class="weather-info">
      <view class="weather-icon">{{weatherData.icon || '☀️'}}</view>
      <view class="weather-text">
        <text class="weather-temp">{{weatherData.temperature || '--'}}°</text>
        <text class="weather-desc">{{weatherData.weather || '晴天'}}</text>
      </view>
    </view>

    <!-- 骑行模式(中) -->
    <view class="riding-mode">
      <view class="mode-icon">🛵</view>
      <text class="mode-text">{{currentMode.name || '成人模式'}}</text>
    </view>

    <!-- 电量时速(右) -->
    <view class="vehicle-status">
      <view class="battery-info">
        <text class="battery-icon">🔋</text>
        <text class="battery-level">{{vehicleData.battery.level || 0}}%</text>
      </view>
      <view class="speed-info">
        <text class="speed-icon">⚡</text>
        <text class="speed-value">{{vehicleData.speed.current || 0}}km/h</text>
      </view>
    </view>
  </view>

  <!-- 静态地图显示区域(中间) -->
  <view class="static-map-section">
    <view class="static-map-container">
      <image
        wx:if="{{staticMapUrl}}"
        class="static-map"
        src="{{staticMapUrl}}"
        mode="aspectFit"
        bindload="onMapLoad"
        binderror="onMapError">
      </image>

      <view wx:else class="map-placeholder">
        <text class="placeholder-text">{{mapStatus || '正在获取地图...'}}</text>
        <button class="get-map-btn" bindtap="getStaticMap">获取静态地图</button>
      </view>
    </view>

    <!-- 地图控制按钮 -->
    <view class="map-controls">
      <picker bindchange="onMapRangeChange" value="{{mapRangeIndex}}" range="{{mapRanges}}" range-key="name">
        <view class="range-picker">
          <text class="range-text">{{mapRanges[mapRangeIndex].name}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
      <button class="refresh-btn" bindtap="refreshStaticMap">刷新</button>
    </view>
  </view>

  <!-- 底部按钮区 -->
  <view class="bottom-buttons">
    <button class="main-btn mode-btn" bindtap="showModeSelector">
      <text class="btn-icon">🚴</text>
      <text class="btn-text">骑行模式</text>
    </button>
    <button class="main-btn nav-btn" bindtap="goToNavigation">
      <text class="btn-icon">🧭</text>
      <text class="btn-text">路线导航</text>
    </button>
  </view>

  <!-- 骑行模式选择弹窗 -->
  <view class="mode-modal" wx:if="{{showModeModal}}" bindtap="hideModeSelector">
    <view class="mode-modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">选择骑行模式</text>
        <text class="modal-close" bindtap="hideModeSelector">✕</text>
      </view>
      <view class="mode-options">
        <view
          class="mode-option {{currentMode.code === 1 ? 'active' : ''}}"
          bindtap="selectMode"
          data-mode="1">
          <text class="mode-name">青少年模式</text>
          <text class="mode-speed">限速15km/h</text>
        </view>
        <view
          class="mode-option {{currentMode.code === 2 ? 'active' : ''}}"
          bindtap="selectMode"
          data-mode="2">
          <text class="mode-name">成人模式</text>
          <text class="mode-speed">限速25km/h</text>
        </view>
        <view
          class="mode-option {{currentMode.code === 3 ? 'active' : ''}}"
          bindtap="selectMode"
          data-mode="3">
          <text class="mode-name">老年模式</text>
          <text class="mode-speed">限速12km/h</text>
        </view>
      </view>
    </view>
  </view>
</view>
