<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 应用标题 -->
    <view class="app-header">
      <text class="app-title">高德地图导航小程序</text>
      <text class="app-subtitle">基于高德地图API的定位导航功能</text>
    </view>

    <!-- 静态地图显示区域 -->
    <view class="static-map-section">
      <view class="section-title">
        <text class="title-text">📍 当前位置地图（高清）</text>
        <view class="map-controls">
          <picker bindchange="onMapRangeChange" value="{{mapRangeIndex}}" range="{{mapRanges}}" range-key="name">
            <view class="range-picker">
              <text class="range-text">{{mapRanges[mapRangeIndex].name}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
          <button class="refresh-btn" bindtap="refreshStaticMap">刷新</button>
        </view>
      </view>

      <view class="static-map-container">
        <image
          wx:if="{{staticMapUrl}}"
          class="static-map"
          src="{{staticMapUrl}}"
          mode="aspectFit"
          bindload="onMapLoad"
          binderror="onMapError">
        </image>

        <view wx:else class="map-placeholder">
          <text class="placeholder-text">{{mapStatus || '点击获取地图'}}</text>
          <button class="get-map-btn" bindtap="getStaticMap">获取静态地图</button>
        </view>
      </view>

      <!-- 地图信息 -->
      <view class="map-info" wx:if="{{locationInfo}}">
        <view class="info-row">
          <text class="info-label">位置：</text>
          <text class="info-value">{{locationInfo.address}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">坐标：</text>
          <text class="info-value">{{locationInfo.longitude}}, {{locationInfo.latitude}}</text>
        </view>
      </view>
    </view>

    <!-- 导航功能按钮 -->
    <view class="nav-buttons">
      <button class="nav-btn" bindtap="goToNavigation">
        <text class="nav-btn-text">🧭 路线导航</text>
      </button>
      <button class="nav-btn" bindtap="goToLocation">
        <text class="nav-btn-text">📍 高德地图定位</text>
      </button>
      <button class="nav-btn" bindtap="testStaticMapUrl">
        <text class="nav-btn-text">🗺️ 测试静态地图URL</text>
      </button>
      <button class="nav-btn secondary" bindtap="goToSetup">
        <text class="nav-btn-text">🛠️ 配置指南</text>
      </button>
      <button class="nav-btn secondary" bindtap="goToConfig">
        <text class="nav-btn-text">⚙️ API配置说明</text>
      </button>
      <button class="nav-btn secondary" bindtap="bindViewTap">
        <text class="nav-btn-text">📋 查看日志</text>
      </button>
      <button class="nav-btn debug" bindtap="goToApiTest">
        <text class="nav-btn-text">🔧 API诊断工具</text>
      </button>
    </view>
  </view>
</scroll-view>
