/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding: 0;  /* 移除容器内边距，给地图更多空间 */
}

/* 应用头部样式 */
.app-header {
  text-align: center;
  padding: 30rpx 20rpx;  /* 减少上下内边距 */
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  color: white;
  margin-bottom: 20rpx;  /* 减少下边距 */
}

.app-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.app-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 静态地图区域样式 */
.static-map-section {
  background: white;
  margin: 0 10rpx 30rpx;  /* 减少左右边距，让地图更宽 */
  border-radius: 15rpx;
  padding: 20rpx;  /* 减少内边距，给地图更多空间 */
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
  gap: 15rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  min-width: 200rpx;
}

.map-controls {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.range-picker {
  display: flex;
  align-items: center;
  padding: 8rpx 15rpx;
  background: #f0f8ff;
  border: 2rpx solid #007aff;
  border-radius: 20rpx;
  min-width: 120rpx;
}

.range-text {
  font-size: 24rpx;
  color: #007aff;
  margin-right: 8rpx;
}

.picker-arrow {
  font-size: 18rpx;
  color: #007aff;
}

.refresh-btn {
  padding: 10rpx 20rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.static-map-container {
  border-radius: 10rpx;
  overflow: hidden;
  background: #f8f8f8;
  min-height: 500rpx;  /* 适应高清地图的显示比例 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.static-map {
  width: 100%;
  height: 500rpx;      /* 适应1024*800高清地图的显示比例 */
  min-height: 500rpx;
  max-width: 100%;
  object-fit: contain; /* 确保高清地图完整显示 */
}

.map-placeholder {
  text-align: center;
  padding: 60rpx 20rpx;
}

.placeholder-text {
  display: block;
  color: #999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.get-map-btn {
  padding: 20rpx 40rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 25rpx;
  font-size: 28rpx;
}

/* 地图信息样式 */
.map-info {
  margin-top: 15rpx;  /* 减少上边距 */
  padding: 15rpx;     /* 减少内边距 */
  background: #f8f9fa;
  border-radius: 8rpx;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: bold;
  color: #666;
  min-width: 100rpx;
  font-size: 26rpx;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 26rpx;
  word-break: break-all;
}

/* 导航按钮样式 */
.nav-buttons {
  display: flex;
  flex-direction: column;
  gap: 15rpx;        /* 减少按钮间距 */
  width: 90%;        /* 增加按钮宽度 */
  margin-top: 30rpx; /* 减少上边距 */
}

.nav-btn {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  background: linear-gradient(45deg, #007aff, #5ac8fa);
  color: white;
  border: none;
  font-size: 30rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 122, 255, 0.3);
}

.nav-btn.secondary {
  background: white;
  color: #007aff;
  border: 2rpx solid #007aff;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

.nav-btn.debug {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
  border: 2rpx solid #ff5722;
  box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.3);
}

.nav-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
}

.nav-btn-text {
  color: white;
  font-weight: 500;
}
