/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 0;
}

/* 顶部信息栏样式 */
.top-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  color: white;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

/* 天气信息(左) */
.weather-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.weather-icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.weather-text {
  display: flex;
  flex-direction: column;
}

.weather-temp {
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1;
}

.weather-desc {
  font-size: 22rpx;
  opacity: 0.9;
  line-height: 1;
}

/* 骑行模式(中) */
.riding-mode {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.mode-icon {
  font-size: 36rpx;
  margin-bottom: 5rpx;
}

.mode-text {
  font-size: 24rpx;
  font-weight: bold;
}

/* 电量时速(右) */
.vehicle-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  flex: 1;
  gap: 6rpx;
}

.battery-info, .speed-info, .connection-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.battery-icon, .speed-icon {
  font-size: 24rpx;
}

.battery-level, .speed-value {
  font-size: 24rpx;
  font-weight: bold;
}

.connection-status {
  gap: 6rpx;
}

.connection-dot {
  font-size: 20rpx;
  line-height: 1;
}

.connection-text {
  font-size: 20rpx;
  font-weight: 500;
}

/* 静态地图区域样式(中间) */
.static-map-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  margin: 20rpx 20rpx 0;
  border-radius: 15rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.static-map-container {
  flex: 1;
  border-radius: 10rpx;
  overflow: hidden;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
}

.static-map {
  width: 100%;
  height: 100%;
  min-height: 400rpx;
  object-fit: cover;
}

.map-placeholder {
  text-align: center;
  padding: 60rpx 20rpx;
}

.placeholder-text {
  display: block;
  color: #999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.get-map-btn {
  padding: 20rpx 40rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 25rpx;
  font-size: 28rpx;
}

.map-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.range-picker {
  display: flex;
  align-items: center;
  padding: 8rpx 15rpx;
  background: #f0f8ff;
  border: 2rpx solid #007aff;
  border-radius: 20rpx;
  min-width: 120rpx;
}

.range-text {
  font-size: 24rpx;
  color: #007aff;
  margin-right: 8rpx;
}

.picker-arrow {
  font-size: 18rpx;
  color: #007aff;
}

.refresh-btn, .test-btn {
  padding: 10rpx 20rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.test-btn {
  background: #52c41a;
  margin-left: 10rpx;
}

/* 底部按钮区样式 */
.bottom-buttons {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background: white;
  border-top: 1rpx solid #f0f0f0;
}

.main-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 20rpx;
  border: none;
  font-size: 28rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 122, 255, 0.3);
}

.mode-btn {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  color: white;
}

.nav-btn {
  background: linear-gradient(45deg, #007aff, #5ac8fa);
  color: white;
}

.btn-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 24rpx;
  font-weight: bold;
}

.main-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
}

/* 骑行模式选择弹窗样式 */
.mode-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.mode-modal-content {
  background: white;
  border-radius: 20rpx;
  padding: 0;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 36rpx;
  color: #999;
  padding: 10rpx;
}

.mode-options {
  padding: 20rpx 0;
}

.mode-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.3s;
}

.mode-option:last-child {
  border-bottom: none;
}

.mode-option.active {
  background: #f0f8ff;
  border-left: 6rpx solid #007aff;
}

.mode-option:active {
  background: #f5f5f5;
}

.mode-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.mode-option.active .mode-name {
  color: #007aff;
}

.mode-speed {
  font-size: 26rpx;
  color: #666;
}

.mode-option.active .mode-speed {
  color: #007aff;
}
