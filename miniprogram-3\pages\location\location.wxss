/* location.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 定位信息样式 */
.location-info {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: bold;
  color: #666;
  min-width: 140rpx;
  font-size: 28rpx;
}

.value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
  word-break: break-all;
}

/* 地图样式 */
.map-container {
  margin-bottom: 30rpx;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.map {
  width: 100%;
  height: 400rpx;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.btn {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  border: none;
}

.btn.primary {
  background: linear-gradient(45deg, #007aff, #5ac8fa);
  color: white;
}

.btn.secondary {
  background: white;
  color: #007aff;
  border: 2rpx solid #007aff;
}

.btn:active {
  opacity: 0.8;
}

/* 天气信息样式 */
.weather-info {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.weather-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.weather-item {
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
  font-size: 28rpx;
  color: #666;
}

.weather-item:last-child {
  border-bottom: none;
}

/* POI列表样式 */
.poi-list {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.poi-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.poi-scroll {
  max-height: 600rpx;
}

.poi-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
  background: #fafafa;
}

.poi-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.poi-item:active {
  background: #e6f3ff;
}

.poi-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.poi-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.poi-distance {
  font-size: 24rpx;
  color: #999;
}

/* 错误信息样式 */
.error-info {
  background: #ffebee;
  border: 1rpx solid #f44336;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.error-text {
  color: #f44336;
  font-size: 28rpx;
  text-align: center;
}
