// map-test.js
const CONFIG = require('../../utils/config.js');

Page({
  data: {
    amapKey: '',
    mapCenter: {
      longitude: 116.397428,
      latitude: 39.90923
    },
    mapScale: 16,
    markers: []
  },

  onLoad: function() {
    console.log('地图测试页面加载');
    
    // 获取API密钥
    const amapKey = wx.getStorageSync('AMAP_KEY') || CONFIG.AMAP_KEY;
    this.setData({
      amapKey: amapKey
    });
    
    console.log('使用的API密钥:', amapKey);
  },

  onReady: function() {
    console.log('地图测试页面准备就绪');
    
    // 创建地图上下文
    this.mapContext = wx.createMapContext('testMap', this);
    console.log('测试地图上下文创建成功');
    
    // 自动获取当前位置
    this.getCurrentLocation();
  },

  getCurrentLocation: function() {
    const that = this;
    
    wx.getLocation({
      type: 'gcj02',
      success: function(res) {
        console.log('获取当前位置成功:', res);
        
        that.setData({
          mapCenter: {
            longitude: res.longitude,
            latitude: res.latitude
          }
        });
        
        // 添加当前位置标记
        that.addMarker(res.longitude, res.latitude, '当前位置');
      },
      fail: function(error) {
        console.error('获取当前位置失败:', error);
        wx.showToast({
          title: '获取位置失败',
          icon: 'none'
        });
      }
    });
  },

  addMarker: function(longitude, latitude, title) {
    const markers = this.data.markers;
    markers.push({
      id: markers.length,
      longitude: longitude,
      latitude: latitude,
      title: title,
      iconPath: '/images/marker.png',
      width: 30,
      height: 30
    });
    
    this.setData({
      markers: markers
    });
  },

  testMarker: function() {
    const center = this.data.mapCenter;
    this.addMarker(
      center.longitude + 0.001,
      center.latitude + 0.001,
      '测试标记'
    );
  },

  zoomIn: function() {
    this.setData({
      mapScale: Math.min(this.data.mapScale + 1, 20)
    });
  },

  zoomOut: function() {
    this.setData({
      mapScale: Math.max(this.data.mapScale - 1, 5)
    });
  },

  onMapTap: function(e) {
    console.log('地图点击事件:', e);
  },

  onRegionChange: function(e) {
    console.log('地图区域变化:', e);
  },

  goBack: function() {
    wx.navigateBack();
  }
});
