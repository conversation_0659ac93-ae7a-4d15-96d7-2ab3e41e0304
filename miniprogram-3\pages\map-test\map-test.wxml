<!--map-test.wxml-->
<view class="container">
  <view class="header">
    <text class="title">地图测试页面</text>
    <button class="back-btn" bindtap="goBack">返回</button>
  </view>
  
  <view class="info-panel">
    <text class="info-text">API密钥: {{amapKey}}</text>
    <text class="info-text">地图中心: {{mapCenter.longitude}}, {{mapCenter.latitude}}</text>
    <text class="info-text">缩放级别: {{mapScale}}</text>
  </view>
  
  <view class="map-container">
    <map
      id="testMap"
      class="test-map"
      longitude="{{mapCenter.longitude}}"
      latitude="{{mapCenter.latitude}}"
      scale="{{mapScale}}"
      show-location="{{true}}"
      markers="{{markers}}"
      bindtap="onMapTap"
      bindregionchange="onRegionChange">
      
      <cover-view class="map-overlay">
        <cover-view class="overlay-text">地图加载测试</cover-view>
      </cover-view>
    </map>
  </view>
  
  <view class="controls">
    <button class="control-btn" bindtap="getCurrentLocation">获取当前位置</button>
    <button class="control-btn" bindtap="testMarker">添加测试标记</button>
    <button class="control-btn" bindtap="zoomIn">放大</button>
    <button class="control-btn" bindtap="zoomOut">缩小</button>
  </view>
</view>
