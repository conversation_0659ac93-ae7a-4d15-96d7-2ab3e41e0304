/* map-test.wxss */
page {
  height: 100vh;
  background-color: #f5f5f5;
}

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.back-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.info-panel {
  padding: 20rpx;
  background: white;
  margin: 10rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.info-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.map-container {
  flex: 1;
  margin: 10rpx;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.test-map {
  width: 100%;
  height: 100%;
}

.map-overlay {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}

.overlay-text {
  font-size: 24rpx;
}

.controls {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  background: white;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.1);
}

.control-btn {
  background: #28a745;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 15rpx 25rpx;
  font-size: 24rpx;
  flex: 1;
  margin: 0 5rpx;
}
