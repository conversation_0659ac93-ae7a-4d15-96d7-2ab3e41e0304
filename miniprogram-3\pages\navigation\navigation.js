// navigation.js
const navigationConfig = require('../../utils/navigationConfig.js');
const poiSearchConfig = require('../../utils/poiSearchConfig.js');
const CONFIG = require('../../utils/config.js');
const errorHandler = require('../../utils/errorHandler.js');
const { calculateDistance, formatDistance, formatDuration } = require('../../utils/util.js');

Page({
  data: {
    // 路线类型选择 - 包含步行、骑行和电动车
    routeTypes: [
      { key: 'walking', name: '🚶 步行', strategy: 'SINGLE' },
      { key: 'bicycling', name: '🚴 骑行', strategy: 'SINGLE' },
      { key: 'electrobike', name: '🛵 电动车', strategy: 'SINGLE' },
      { key: 'walking_multi', name: '🚶 步行(多路线)', strategy: 'MULTIPLE' },
      { key: 'bicycling_multi', name: '🚴 骑行(多路线)', strategy: 'MULTIPLE' },
      { key: 'electrobike_multi', name: '🛵 电动车(多路线)', strategy: 'MULTIPLE' }
    ],
    routeTypeIndex: 0, // 默认选择步行

    // 当前位置信息（起点）
    currentLocation: null,
    currentLocationAddress: '',

    // 目的地输入和搜索
    destinationInput: '',
    searchResults: [],
    selectedDestination: null,
    inputFocus: false,

    // 地图相关
    mapCenter: { longitude: 116.397428, latitude: 39.90923 },
    mapScale: 16,
    markers: [],
    polylines: [],

    // 路线数据
    routeData: null,
    selectedRouteIndex: 0,
    planningRoute: false,
    canPlanRoute: false,

    // 导航状态
    isNavigating: false,
    currentInstruction: '',
    remainingDistance: 0,
    remainingTime: 0,

    // UI状态
    panelExpanded: false,  // 默认收起面板，让地图全屏显示
    errorMessage: '',
    navigationPaused: false
  },

  // 性能优化：防抖和节流
  _debounceTimers: {},
  _throttleTimers: {},

  // 防抖函数
  debounce: function(func, delay, key) {
    if (this._debounceTimers[key]) {
      clearTimeout(this._debounceTimers[key]);
    }
    this._debounceTimers[key] = setTimeout(() => {
      func.call(this);
      delete this._debounceTimers[key];
    }, delay);
  },

  // 节流函数
  throttle: function(func, delay, key) {
    if (this._throttleTimers[key]) {
      return;
    }
    this._throttleTimers[key] = setTimeout(() => {
      func.call(this);
      delete this._throttleTimers[key];
    }, delay);
  },

  onLoad: function(options) {
    console.log('导航页面接收到的参数:', options);

    // 如果有传入的起点位置参数，使用传入的位置作为起点
    if (options.longitude && options.latitude) {
      const passedLocation = {
        longitude: parseFloat(options.longitude),
        latitude: parseFloat(options.latitude)
      };

      this.setData({
        currentLocation: passedLocation,
        mapCenter: passedLocation,
        currentLocationAddress: '主页面传递的位置'
      });

      // 使用逆地理编码获取详细地址
      this.getAddressFromLocation(passedLocation.longitude, passedLocation.latitude);
      this.updateMarkers();
      this.updateCanPlanRoute();

      console.log('使用主页面传递的起点位置:', passedLocation);
    } else {
      // 没有传入位置参数时，自动获取当前位置作为起点
      this.getCurrentLocation();
    }

    // 如果有传入的车辆模式参数，设置对应的路线类型
    if (options.mode) {
      const vehicleMode = parseInt(options.mode);
      let routeTypeIndex = 0; // 默认步行

      // 根据车辆模式设置路线类型
      if (vehicleMode === 1 || vehicleMode === 2 || vehicleMode === 3) {
        // 车辆模式1,2,3都使用电动车路线规划
        routeTypeIndex = this.data.routeTypes.findIndex(type => type.key === 'electrobike');
        if (routeTypeIndex === -1) routeTypeIndex = 0; // 如果找不到电动车选项，使用默认
      }

      this.setData({
        routeTypeIndex: routeTypeIndex
      });

      console.log(`根据车辆模式${vehicleMode}设置路线类型为: ${this.data.routeTypes[routeTypeIndex].name}`);
    }

    // 处理车辆连接状态参数
    if (options.connected !== undefined) {
      const isConnected = options.connected === '1';
      console.log('接收到车辆连接状态:', isConnected);

      // 可以在这里设置一些基于连接状态的UI提示
      if (!isConnected) {
        wx.showToast({
          title: '车辆未连接',
          icon: 'none',
          duration: 2000
        });
      }
    }

    // 处理车辆电量参数
    if (options.battery !== undefined) {
      const batteryLevel = parseInt(options.battery);
      console.log('接收到车辆电量:', batteryLevel);

      // 如果电量过低，给出提示
      if (batteryLevel < 20) {
        wx.showModal({
          title: '电量提醒',
          content: `当前电量${batteryLevel}%，建议充电后再进行长距离导航`,
          showCancel: false,
          confirmText: '知道了'
        });
      }
    }

    // 如果有传入的目的地参数，设置目的地
    if (options.endLng && options.endLat) {
      this.setData({
        selectedDestination: {
          name: options.endName || '目的地',
          address: options.endAddress || '',
          longitude: parseFloat(options.endLng),
          latitude: parseFloat(options.endLat)
        }
      });
      this.updateCanPlanRoute();
    }
  },

  onReady: function() {
    // 创建地图上下文
    this.mapContext = wx.createMapContext('navigationMap', this);
    console.log('地图上下文创建成功');

    // 验证地图API密钥
    const amapKey = wx.getStorageSync('AMAP_KEY');
    console.log('地图API密钥:', amapKey);

    // 设置地图初始状态
    this.setData({
      mapCenter: this.data.currentLocation || {
        longitude: CONFIG.MAP_CONFIG.DEFAULT_LONGITUDE,
        latitude: CONFIG.MAP_CONFIG.DEFAULT_LATITUDE
      },
      mapScale: 16
    });

    // 如果已有当前位置，移动地图到当前位置
    if (this.data.currentLocation) {
      this.moveToLocation(this.data.currentLocation);
    } else {
      // 没有当前位置时，获取当前位置
      this.getCurrentLocation();
    }

    console.log('地图初始化完成，中心点:', this.data.mapCenter);

    // 延迟检查地图显示状态
    setTimeout(() => {
      this.checkMapDisplayStatus();
    }, 1000);
  },

  // 检查地图显示状态
  checkMapDisplayStatus: function() {
    console.log('检查地图显示状态...');
    console.log('地图中心点:', this.data.mapCenter);
    console.log('地图缩放级别:', this.data.mapScale);
    console.log('标记数量:', this.data.markers.length);
    console.log('路线数量:', this.data.polylines.length);

    if (this.mapContext) {
      console.log('地图上下文已创建');
    } else {
      console.error('地图上下文未创建');
    }
  },

  // 返回主页面
  goBackToHome: function() {
    // 保存当前导航状态（如果需要的话）
    const currentState = {
      routeTypeIndex: this.data.routeTypeIndex,
      currentLocation: this.data.currentLocation,
      selectedDestination: this.data.selectedDestination,
      routes: this.data.routes
    };

    console.log('保存导航状态:', currentState);

    // 返回主页面
    wx.navigateBack({
      delta: 1,
      success: function() {
        console.log('返回主页面成功');
      },
      fail: function(error) {
        console.error('返回主页面失败:', error);
        // 如果返回失败，尝试重定向到主页面
        wx.redirectTo({
          url: '../index/index'
        });
      }
    });
  },

  // 获取当前位置（作为起点）- 高精度定位优化
  getCurrentLocation: function() {
    const that = this;

    that.setData({
      currentLocationAddress: '正在获取当前位置...'
    });

    // 使用高精度定位配置
    wx.getLocation({
      type: 'gcj02', // 使用国测局坐标系
      altitude: true, // 获取高度信息
      isHighAccuracy: true, // 开启高精度定位
      highAccuracyExpireTime: 4000, // 高精度定位超时时间
      success: function(res) {
        console.log('获取位置成功:', res);

        // 检查定位精度，如果精度不够则重新定位
        const accuracy = res.accuracy || 999;
        console.log('定位精度:', accuracy + '米');

        if (accuracy > 100) {
          console.warn('定位精度较低，尝试重新定位');
          // 精度不够时，延迟重试
          setTimeout(() => {
            that.getCurrentLocationWithRetry();
          }, 2000);
          return;
        }

        that.setData({
          currentLocation: {
            longitude: res.longitude,
            latitude: res.latitude,
            accuracy: accuracy,
            speed: res.speed || 0,
            altitude: res.altitude || 0
          },
          mapCenter: {
            longitude: res.longitude,
            latitude: res.latitude
          }
        });

        // 使用逆地理编码获取详细地址
        that.getAddressFromLocation(res.longitude, res.latitude);
        that.updateMarkers();
        that.updateCanPlanRoute();

        wx.showToast({
          title: `定位成功(精度${Math.round(accuracy)}m)`,
          icon: 'success',
          duration: 1500
        });
      },
      fail: function(error) {
        console.error('获取位置失败:', error);
        that.setData({
          currentLocationAddress: '定位失败，请检查权限'
        });

        // 定位失败时的处理
        that.handleLocationError(error);
      }
    });
  },

  // 高精度定位重试机制
  getCurrentLocationWithRetry: function(retryCount = 0) {
    const that = this;
    const maxRetries = 3;

    if (retryCount >= maxRetries) {
      that.setData({
        currentLocationAddress: '定位精度不足，请手动选择位置'
      });
      return;
    }

    wx.getLocation({
      type: 'gcj02',
      isHighAccuracy: true,
      highAccuracyExpireTime: 6000,
      success: function(res) {
        const accuracy = res.accuracy || 999;
        console.log(`第${retryCount + 1}次重试定位，精度:`, accuracy + '米');

        if (accuracy <= 50 || retryCount === maxRetries - 1) {
          // 精度足够或已达最大重试次数
          that.setData({
            currentLocation: {
              longitude: res.longitude,
              latitude: res.latitude,
              accuracy: accuracy,
              speed: res.speed || 0,
              altitude: res.altitude || 0
            },
            mapCenter: {
              longitude: res.longitude,
              latitude: res.latitude
            }
          });

          that.getAddressFromLocation(res.longitude, res.latitude);
          that.updateMarkers();
          that.updateCanPlanRoute();

          wx.showToast({
            title: `定位成功(精度${Math.round(accuracy)}m)`,
            icon: 'success'
          });
        } else {
          // 继续重试
          setTimeout(() => {
            that.getCurrentLocationWithRetry(retryCount + 1);
          }, 1000);
        }
      },
      fail: function(error) {
        if (retryCount < maxRetries - 1) {
          setTimeout(() => {
            that.getCurrentLocationWithRetry(retryCount + 1);
          }, 1000);
        } else {
          that.handleLocationError(error);
        }
      }
    });
  },

  // 定位错误处理
  handleLocationError: function(error) {
    let errorMessage = '定位失败';

    if (error.errMsg) {
      if (error.errMsg.includes('auth deny')) {
        errorMessage = '定位权限被拒绝，请在设置中开启';
      } else if (error.errMsg.includes('timeout')) {
        errorMessage = '定位超时，请检查网络连接';
      } else if (error.errMsg.includes('fail')) {
        errorMessage = '定位服务不可用';
      }
    }

    this.setData({
      currentLocationAddress: errorMessage
    });

    wx.showModal({
      title: '定位失败',
      content: errorMessage + '\n\n建议：\n1. 检查定位权限\n2. 确保网络连接正常\n3. 尝试到空旷地区重新定位',
      showCancel: true,
      cancelText: '手动选择',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          this.getCurrentLocation();
        } else {
          // 用户选择手动选择位置
          this.showLocationPicker();
        }
      }
    });
  },

  // 显示位置选择器
  showLocationPicker: function() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          currentLocation: {
            longitude: res.longitude,
            latitude: res.latitude,
            accuracy: 0, // 手动选择的位置精度设为0
            name: res.name,
            address: res.address
          },
          currentLocationAddress: res.name || res.address,
          mapCenter: {
            longitude: res.longitude,
            latitude: res.latitude
          }
        });

        this.updateMarkers();
        this.updateCanPlanRoute();

        wx.showToast({
          title: '起点已设置',
          icon: 'success'
        });
      },
      fail: (error) => {
        console.log('用户取消选择位置:', error);
      }
    });
  },

  // 刷新当前位置
  refreshCurrentLocation: function() {
    this.getCurrentLocation();
    wx.showToast({
      title: '正在刷新位置',
      icon: 'loading'
    });
  },

  // 根据坐标获取地址
  getAddressFromLocation: function(longitude, latitude) {
    const that = this;

    poiSearchConfig.reverseGeocoding(longitude, latitude)
      .then(result => {
        console.log('逆地理编码成功:', result);
        that.setData({
          currentLocationAddress: result.formatted_address
        });
      })
      .catch(error => {
        console.error('逆地理编码失败:', error);
        const fallbackAddress = `经度:${longitude.toFixed(6)}, 纬度:${latitude.toFixed(6)}`;
        that.setData({
          currentLocationAddress: fallbackAddress
        });
      });
  },

  // 路线类型改变
  onRouteTypeChange: function(e) {
    this.setData({
      routeTypeIndex: parseInt(e.detail.value),
      routeData: null,
      polylines: []
    });
  },

  // 目的地输入
  onDestinationInput: function(e) {
    const value = e.detail.value;
    this.setData({
      destinationInput: value,
      searchResults: [] // 清空之前的搜索结果
    });

    // 如果输入为空，清除选中的目的地
    if (!value.trim()) {
      this.setData({
        selectedDestination: null
      });
      this.updateCanPlanRoute();
    }
  },

  // 搜索目的地
  searchDestination: function() {
    const keyword = this.data.destinationInput.trim();
    if (!keyword) {
      wx.showToast({
        title: '请输入目的地',
        icon: 'none'
      });
      return;
    }

    this.performPOISearch(keyword);
  },

  // 执行POI搜索
  performPOISearch: function(keyword) {
    const that = this;

    wx.showLoading({
      title: '搜索中...'
    });

    // 构建搜索选项
    const searchOptions = {};

    // 如果有当前位置，优先搜索附近的POI
    if (this.data.currentLocation) {
      searchOptions.location = `${this.data.currentLocation.longitude},${this.data.currentLocation.latitude}`;
      searchOptions.radius = 50000; // 50km范围
      searchOptions.sortrule = 'distance'; // 按距离排序
    }

    poiSearchConfig.searchPOI(keyword, searchOptions)
      .then(result => {
        wx.hideLoading();
        console.log('POI搜索成功:', result);

        that.setData({
          searchResults: result.results
        });

        if (result.results.length === 0) {
          wx.showToast({
            title: '未找到相关地点',
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: `找到${result.results.length}个地点`,
            icon: 'success'
          });
        }
      })
      .catch(error => {
        wx.hideLoading();
        console.error('POI搜索失败:', error);
        that.showError(error.message || '搜索失败，请检查网络连接');
      });
  },

  // 选择目的地
  selectDestination: function(e) {
    const index = e.currentTarget.dataset.index;
    const destination = this.data.searchResults[index];

    this.setData({
      selectedDestination: destination,
      searchResults: [], // 清空搜索结果
      destinationInput: destination.name // 更新输入框显示
    });

    this.updateMarkers();
    this.updateCanPlanRoute();

    wx.showToast({
      title: '目的地已选择',
      icon: 'success'
    });
  },

  // 清除目的地
  clearDestination: function() {
    this.setData({
      selectedDestination: null,
      destinationInput: '',
      searchResults: []
    });

    this.updateMarkers();
    this.updateCanPlanRoute();
  },

  // 清除路线
  clearRoute: function() {
    this.setData({
      routeData: null,
      polylines: [],
      isNavigating: false,
      selectedRouteIndex: 0
    });
  },

  // 规划路线（电动车路径规划2.0优化）
  planRoute: function() {
    // 防重复点击检查
    if (!this.data.canPlanRoute || this.data.planningRoute) {
      console.log('路线规划被阻止:', {
        canPlanRoute: this.data.canPlanRoute,
        planningRoute: this.data.planningRoute
      });
      return;
    }

    const that = this;
    const selectedRouteType = this.data.routeTypes[this.data.routeTypeIndex];

    // 优先使用电动车路线规划
    let routeType = 'electrobike'; // 默认使用电动车路线规划
    if (selectedRouteType.key === 'walking') {
      routeType = 'walking';
    } else if (selectedRouteType.key === 'bicycling') {
      routeType = 'bicycling';
    }

    const strategy = selectedRouteType.strategy;

    const startPoint = this.data.currentLocation;
    const endPoint = this.data.selectedDestination;

    // 验证起终点精度
    if (startPoint.accuracy && startPoint.accuracy > 200) {
      wx.showModal({
        title: '定位精度不足',
        content: '当前位置精度较低，可能影响路线规划质量。是否重新定位？',
        confirmText: '重新定位',
        cancelText: '继续规划',
        success: (res) => {
          if (res.confirm) {
            this.getCurrentLocation();
            return;
          } else {
            this.executePlanRoute(routeType, strategy, startPoint, endPoint);
          }
        }
      });
      return;
    }

    this.executePlanRoute(routeType, strategy, startPoint, endPoint);
  },

  // 执行路线规划
  executePlanRoute: function(routeType, strategy, startPoint, endPoint) {
    const that = this;

    // 立即设置规划状态，防止重复点击
    this.setData({
      planningRoute: true,
      errorMessage: ''
    });

    console.log('开始路线规划:', {
      type: routeType,
      strategy: strategy,
      start: startPoint,
      end: endPoint
    });

    // 显示加载提示
    wx.showLoading({
      title: '规划路线中...',
      mask: true // 防止用户操作
    });

    // 构建路线规划选项 - 路径规划2.0增强
    const options = this.buildRouteOptions(routeType, strategy);

    // 添加电动车特有参数
    if (routeType === 'electrobike') {
      options.alternative_route = 1; // 获取备选路线
      options.cartype = 0; // 电动车类型
    }

    navigationConfig.planRoute(routeType, startPoint, endPoint, options)
      .then(result => {
        console.log('路线规划成功:', result);

        wx.hideLoading();

        // 验证路线数据
        if (!result.data || !result.data.routes || result.data.routes.length === 0) {
          console.error('路线规划返回空数据，使用演示数据');
          that.setData({
            routeData: that.createDemoRouteData(),
            planningRoute: false
          });
        } else {
          // 路径规划2.0成功处理
          const routeData = result.data;
          console.log('路线数据详情:', routeData);

          that.setData({
            routeData: routeData,
            planningRoute: false
          });

          // 显示路线质量信息
          if (routeData.routes && routeData.routes.length > 0) {
            const mainRoute = routeData.routes[0];
            const distance = (mainRoute.distance / 1000).toFixed(1);
            const duration = Math.round(mainRoute.duration / 60);

            let qualityInfo = `距离: ${distance}km, 预计: ${duration}分钟`;
            if (mainRoute.traffic_lights) {
              qualityInfo += `\n红绿灯: ${mainRoute.traffic_lights}个`;
            }
            if (routeData.routes.length > 1) {
              qualityInfo += `\n共${routeData.routes.length}条路线可选`;
            }

            wx.showToast({
              title: qualityInfo,
              icon: 'none',
              duration: 3000
            });
          }
        }

        // 显示路线 - 防抖动优化
        that.showRouteOnMapStable();

        // 调整地图视野 - 平滑过渡
        that.fitRouteInViewSmooth();

        // 询问是否开始导航
        setTimeout(() => {
          const selectedRouteType = that.data.routeTypes[that.data.routeTypeIndex];
          wx.showModal({
            title: '路线规划成功',
            content: `${selectedRouteType.name}路线已规划完成，是否开始导航？`,
            confirmText: '开始导航',
            cancelText: '查看路线',
            success: function(res) {
              if (res.confirm) {
                // 开始导航
                that.startNavigation();
              } else {
                wx.showToast({
                  title: '路线规划成功',
                  icon: 'success'
                });
              }
            }
          });
        }, 500); // 延迟显示，避免与地图更新冲突
      })
      .catch(error => {
        console.error('路线规划失败:', error);

        wx.hideLoading();

        that.setData({
          planningRoute: false
        });

        // 使用统一的错误处理
        errorHandler.handleApiError(error, {
          showModal: true,
          onCancel: () => that.executePlanRoute(routeType, strategy, startPoint, endPoint) // 重试路线规划
        });
      });
  },

  // 构建路线规划选项（专注于步行和骑行）
  buildRouteOptions: function(routeType, strategy) {
    const options = {};

    switch (routeType) {
      case 'walking':
        // 步行路线规划选项
        options.alternative_route = strategy === 'MULTIPLE' ? 1 : 0; // 是否返回多条路线
        break;
      case 'bicycling':
        // 骑行路线规划选项（使用v4 API）
        options.alternative_route = strategy === 'MULTIPLE' ? 1 : 0; // 是否返回多条路线
        break;
      case 'electrobike':
        // 电动车路线规划选项（使用v5 API）
        options.alternative_route = strategy === 'MULTIPLE' ? 1 : 0; // 是否返回多条路线
        options.show_fields = 'cost,navi,polyline'; // 显示费用、导航和路径信息
        break;
    }

    return options;
  },

  // 在地图上显示路线 - 稳定版本（防抖动）
  showRouteOnMapStable: function() {
    if (!this.data.routeData || this.data.routeData.routes.length === 0) return;

    // 获取当前路线类型
    const selectedRouteType = this.data.routeTypes[this.data.routeTypeIndex];
    let routeType = 'electrobike'; // 默认使用电动车
    if (selectedRouteType.key === 'walking') {
      routeType = 'walking';
    } else if (selectedRouteType.key === 'bicycling') {
      routeType = 'bicycling';
    }

    // 转换为地图polyline格式，传入路线类型用于样式定制
    const polylines = navigationConfig.convertToMapPolyline(this.data.routeData, routeType);

    // 防抖动：延迟更新polylines，避免地图抖动
    this.debounce(() => {
      this.setData({
        polylines: polylines
      });
      console.log('路线已显示在地图上，polylines数量:', polylines.length);
    }, 300, 'showRouteOnMap');
  },

  // 在地图上显示路线 - 原版本（保持兼容性）
  showRouteOnMap: function() {
    if (!this.data.routeData || this.data.routeData.routes.length === 0) return;

    // 获取当前路线类型
    const selectedRouteType = this.data.routeTypes[this.data.routeTypeIndex];
    const routeType = selectedRouteType.key.includes('_') ?
                     selectedRouteType.key.split('_')[0] : selectedRouteType.key;

    // 转换为地图polyline格式，传入路线类型用于样式定制
    const polylines = navigationConfig.convertToMapPolyline(this.data.routeData, routeType);

    this.setData({
      polylines: polylines
    });

    // 调整地图视野以包含整条路线
    this.fitRouteInView();
  },

  // 调整地图视野 - 平滑过渡版本（防抖动）
  fitRouteInViewSmooth: function() {
    const startPoint = this.data.currentLocation;
    const endPoint = this.data.selectedDestination;

    if (!startPoint || !endPoint) return;

    // 计算中心点
    const centerLng = (startPoint.longitude + endPoint.longitude) / 2;
    const centerLat = (startPoint.latitude + endPoint.latitude) / 2;

    // 计算合适的缩放级别（电动车路线优化）
    const distance = this.calculateDistance(startPoint, endPoint);
    let scale = 16;

    if (distance > 50000) scale = 10;      // 超过50km
    else if (distance > 20000) scale = 11; // 20-50km
    else if (distance > 10000) scale = 12; // 10-20km
    else if (distance > 5000) scale = 13;  // 5-10km
    else if (distance > 2000) scale = 14;  // 2-5km
    else if (distance > 1000) scale = 15;  // 1-2km
    else scale = 16;                       // 小于1km

    // 检查是否需要调整视野
    const currentCenter = this.data.mapCenter;
    const currentScale = this.data.mapScale;

    const centerChanged = !currentCenter ||
                         Math.abs(currentCenter.longitude - centerLng) > 0.001 ||
                         Math.abs(currentCenter.latitude - centerLat) > 0.001;
    const scaleChanged = currentScale !== scale;

    if (centerChanged || scaleChanged) {
      // 使用防抖动延迟更新，避免地图频繁调整
      this.debounce(() => {
        this.setData({
          mapCenter: {
            longitude: centerLng,
            latitude: centerLat
          },
          mapScale: scale
        });
        console.log('地图视野已调整:', { center: { lng: centerLng, lat: centerLat }, scale: scale });
      }, 500, 'fitRouteInView');
    }
  },

  // 调整地图视野 - 原版本（保持兼容性）
  fitRouteInView: function() {
    const startPoint = this.data.currentLocation;
    const endPoint = this.data.selectedDestination;

    if (!startPoint || !endPoint) return;

    // 计算中心点
    const centerLng = (startPoint.longitude + endPoint.longitude) / 2;
    const centerLat = (startPoint.latitude + endPoint.latitude) / 2;

    // 计算合适的缩放级别（步行和骑行通常距离较短）
    const distance = this.calculateDistance(startPoint, endPoint);
    let scale = 16;

    if (distance > 20000) scale = 11;      // 超过20km
    else if (distance > 10000) scale = 12; // 10-20km
    else if (distance > 5000) scale = 13;  // 5-10km
    else if (distance > 2000) scale = 14;  // 2-5km
    else if (distance > 1000) scale = 15;  // 1-2km
    else scale = 16;                       // 小于1km

    this.setData({
      mapCenter: {
        longitude: centerLng,
        latitude: centerLat
      },
      mapScale: scale
    });
  },

  // 计算两点间距离（使用工具函数）
  calculateDistance: function(point1, point2) {
    return calculateDistance(point1, point2);
  },

  // 更新地图标记 - 添加防抖优化
  updateMarkers: function() {
    this.debounce(() => {
      const markers = [];

      // 起点标记（当前位置）
      if (this.data.currentLocation) {
        markers.push({
          id: 1,
          latitude: this.data.currentLocation.latitude,
          longitude: this.data.currentLocation.longitude,
          width: 30,
          height: 30,
          title: '当前位置（起点）',
          callout: {
            content: '起点',
            color: '#ffffff',
            bgColor: '#4caf50',
            borderRadius: 5,
            padding: 5
          }
        });
      }

      // 终点标记（选中的目的地）
      if (this.data.selectedDestination) {
        markers.push({
          id: 2,
          latitude: this.data.selectedDestination.latitude,
          longitude: this.data.selectedDestination.longitude,
          width: 30,
          height: 30,
          title: this.data.selectedDestination.name,
          callout: {
            content: '终点',
            color: '#ffffff',
            bgColor: '#f44336',
            borderRadius: 5,
            padding: 5
          }
        });
      }

      this.setData({
        markers: markers
      });
    }, 150, 'updateMarkers');
  },

  // 更新是否可以规划路线
  updateCanPlanRoute: function() {
    const canPlan = this.data.currentLocation &&
                   this.data.selectedDestination;
    this.setData({
      canPlanRoute: canPlan
    });
  },

  // 格式化距离
  formatDistance: function(distance) {
    if (distance < 1000) {
      return distance + 'm';
    } else {
      return (distance / 1000).toFixed(1) + 'km';
    }
  },

  // 格式化时间
  formatDuration: function(duration) {
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    
    if (hours > 0) {
      return hours + '小时' + minutes + '分钟';
    } else {
      return minutes + '分钟';
    }
  },

  // 显示错误信息
  showError: function(message) {
    this.setData({
      errorMessage: message
    });
    
    // 3秒后自动清除错误信息
    setTimeout(() => {
      this.setData({
        errorMessage: ''
      });
    }, 3000);
  },

  // 清除错误信息
  clearError: function() {
    this.setData({
      errorMessage: ''
    });
  },

  // 地图相关事件处理
  onMapTap: function(e) {
    console.log('地图点击:', e);
  },

  onMarkerTap: function(e) {
    console.log('标记点击:', e);
  },

  onRegionChange: function(e) {
    if (e.type === 'end') {
      // 使用节流优化地图中心点更新
      this.throttle(() => {
        this.setData({
          mapCenter: {
            longitude: e.detail.centerLocation.longitude,
            latitude: e.detail.centerLocation.latitude
          }
        });
      }, 300, 'regionChange');
    }
  },

  // 地图控制 - 添加节流优化
  zoomIn: function() {
    this.throttle(() => {
      this.setData({
        mapScale: Math.min(this.data.mapScale + 1, 20)
      });
    }, 200, 'zoomIn');
  },

  zoomOut: function() {
    this.throttle(() => {
      this.setData({
        mapScale: Math.max(this.data.mapScale - 1, 5)
      });
    }, 200, 'zoomOut');
  },

  // 移动地图到指定位置
  moveToLocation: function(location) {
    if (location && location.longitude && location.latitude) {
      this.setData({
        mapCenter: {
          longitude: location.longitude,
          latitude: location.latitude
        },
        mapScale: 16
      });

      // 如果地图上下文已创建，使用地图API移动
      if (this.mapContext) {
        this.mapContext.moveToLocation({
          longitude: location.longitude,
          latitude: location.latitude
        });
      }
    }
  },

  moveToCurrentLocation: function() {
    if (this.data.currentLocation) {
      this.moveToLocation(this.data.currentLocation);
    } else {
      this.getCurrentLocation();
    }
  },

  // 面板控制 - 添加防抖优化
  togglePanel: function() {
    this.debounce(() => {
      this.setData({
        panelExpanded: !this.data.panelExpanded
      });
    }, 100, 'togglePanel');
  },

  // 选择路线
  selectRoute: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      selectedRouteIndex: index
    });
    
    // 重新显示选中的路线
    this.showRouteOnMap();
  },

  // 开始导航 - 在当前页面进行导航
  startNavigation: function() {
    // 检查基本数据
    if (!this.data.currentLocation || !this.data.selectedDestination) {
      wx.showToast({
        title: '起点或终点信息缺失',
        icon: 'none'
      });
      return;
    }

    // 检查路线数据，如果没有则创建演示数据
    let routeData = this.data.routeData;
    if (!routeData || !routeData.routes || routeData.routes.length === 0) {
      console.log('路线数据缺失，创建演示数据');
      routeData = this.createDemoRouteData();
      this.setData({
        routeData: routeData
      });
    }

    const selectedRoute = routeData.routes[this.data.selectedRouteIndex];

    this.setData({
      isNavigating: true,
      currentInstruction: '导航开始，请按照指示行驶',
      remainingDistance: selectedRoute.distance,
      remainingTime: selectedRoute.duration,
      panelExpanded: false,  // 导航时收起面板
      navigationPaused: false
    });

    // 开始模拟导航
    this.startNavigationSimulation();

    wx.showToast({
      title: '导航开始',
      icon: 'success'
    });
  },

  // 模拟导航过程 - 性能优化
  startNavigationSimulation: function() {
    if (!this.data.isNavigating) return;

    const instructions = [
      '直行500米',
      '前方路口右转',
      '直行300米后左转',
      '继续直行200米',
      '到达目的地'
    ];

    let currentStep = 0;
    // 使用更长的间隔减少频繁更新
    this.navigationInterval = setInterval(() => {
      if (!this.data.isNavigating || this.data.navigationPaused) {
        if (!this.data.isNavigating) {
          clearInterval(this.navigationInterval);
        }
        return;
      }

      if (currentStep < instructions.length) {
        // 批量更新数据，减少setData调用
        const updateData = {
          currentInstruction: instructions[currentStep],
          remainingDistance: Math.max(0, this.data.remainingDistance - 100),
          remainingTime: Math.max(0, this.data.remainingTime - 30)
        };
        this.setData(updateData);
        currentStep++;
      } else {
        // 导航结束
        this.setData({
          isNavigating: false,
          currentInstruction: '已到达目的地',
          panelExpanded: true
        });
        clearInterval(this.navigationInterval);

        wx.showModal({
          title: '导航完成',
          content: '您已到达目的地',
          showCancel: false
        });
      }
    }, 5000); // 增加到5秒更新一次，减少性能消耗
  },

  // 暂停/继续导航
  pauseNavigation: function() {
    this.setData({
      navigationPaused: !this.data.navigationPaused
    });

    wx.showToast({
      title: this.data.navigationPaused ? '导航已暂停' : '导航已继续',
      icon: 'none'
    });
  },

  // 停止导航
  stopNavigation: function() {
    wx.showModal({
      title: '确认停止',
      content: '确定要停止导航吗？',
      success: (res) => {
        if (res.confirm) {
          if (this.navigationInterval) {
            clearInterval(this.navigationInterval);
          }

          this.setData({
            isNavigating: false,
            navigationPaused: false,
            currentInstruction: '',
            remainingDistance: 0,
            remainingTime: 0,
            panelExpanded: true
          });

          wx.showToast({
            title: '导航已停止',
            icon: 'none'
          });
        }
      }
    });
  },

  // 测试电动车路线规划功能
  testElectrobikeRoute: function() {
    const that = this;

    wx.showLoading({
      title: '测试电动车路线...'
    });

    // 设置测试目的地（北京天安门）
    const testDestination = {
      name: '天安门广场',
      address: '北京市东城区天安门广场',
      longitude: 116.397128,
      latitude: 39.916527
    };

    this.setData({
      selectedDestination: testDestination,
      destinationInput: testDestination.name
    });

    this.updateMarkers();
    this.updateCanPlanRoute();

    // 自动规划路线
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '测试目的地已设置',
        icon: 'success'
      });

      // 如果可以规划路线，自动开始规划
      if (this.data.canPlanRoute) {
        setTimeout(() => {
          that.planRoute();
        }, 1000);
      }
    }, 500);
  },

  // 创建演示路线数据
  createDemoRouteData: function() {
    const start = this.data.currentLocation || { longitude: 116.397428, latitude: 39.90923 };
    const end = this.data.selectedDestination || { longitude: 116.407526, latitude: 39.904030 };

    return {
      type: this.data.routeTypes[this.data.routeTypeIndex].key,
      routes: [{
        id: 0,
        distance: 1200,
        duration: 900,
        polyline: `${start.longitude},${start.latitude};${end.longitude},${end.latitude}`,
        steps: [
          {
            instruction: '从起点出发',
            road: '起点',
            distance: 0,
            duration: 0,
            action: 'start',
            orientation: '0',
            polyline: `${start.longitude},${start.latitude}`
          },
          {
            instruction: '直行前往目的地',
            road: '路线',
            distance: 1200,
            duration: 900,
            action: 'straight',
            orientation: '90',
            polyline: `${start.longitude},${start.latitude};${end.longitude},${end.latitude}`
          },
          {
            instruction: '到达目的地',
            road: '目的地',
            distance: 0,
            duration: 0,
            action: 'end',
            orientation: '0',
            polyline: `${end.longitude},${end.latitude}`
          }
        ]
      }],
      summary: {
        distance: 1200,
        duration: 900,
        routeCount: 1
      }
    };
  },

  // 切换起点终点
  switchStartEnd: function() {
    if (!this.data.selectedDestination) {
      wx.showToast({
        title: '请先选择目的地',
        icon: 'none'
      });
      return;
    }

    const currentStart = this.data.currentLocation;
    const currentEnd = this.data.selectedDestination;

    this.setData({
      currentLocation: currentEnd,
      selectedDestination: currentStart,
      currentLocationAddress: currentEnd.name || currentEnd.address,
      destinationInput: currentStart.name || '起点位置'
    });

    this.updateMarkers();
    this.updateCanPlanRoute();

    wx.showToast({
      title: '起终点已交换',
      icon: 'success'
    });
  },

  // 页面卸载时清理资源 - 性能优化
  onUnload: function() {
    // 清理导航定时器
    if (this.navigationInterval) {
      clearInterval(this.navigationInterval);
      this.navigationInterval = null;
    }

    // 清理所有防抖和节流定时器
    Object.keys(this._debounceTimers).forEach(key => {
      clearTimeout(this._debounceTimers[key]);
    });
    Object.keys(this._throttleTimers).forEach(key => {
      clearTimeout(this._throttleTimers[key]);
    });

    this._debounceTimers = {};
    this._throttleTimers = {};

    // 清理地图上下文
    this.mapContext = null;

    console.log('导航页面资源清理完成');
  },

  // 页面隐藏时暂停不必要的操作
  onHide: function() {
    // 暂停导航模拟（如果正在导航）
    if (this.data.isNavigating && !this.data.navigationPaused) {
      this.setData({
        navigationPaused: true
      });
    }
  },

  // 页面显示时恢复操作
  onShow: function() {
    // 恢复导航模拟（如果之前被暂停）
    if (this.data.isNavigating && this.data.navigationPaused) {
      this.setData({
        navigationPaused: false
      });
    }
  }
});
