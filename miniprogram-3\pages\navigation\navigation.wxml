<!--navigation.wxml-->
<view class="container">
  <!-- 地图区域 - 放在最上面，占据主要空间 -->
  <view class="map-container">
    <map
      id="navigationMap"
      class="navigation-map"
      longitude="{{mapCenter.longitude}}"
      latitude="{{mapCenter.latitude}}"
      scale="{{mapScale}}"
      markers="{{markers}}"
      polyline="{{polylines}}"
      show-location="{{true}}"
      bindmarkertap="onMarkerTap"
      bindtap="onMapTap"
      bindregionchange="onRegionChange">

      <!-- 地图控件 -->
      <cover-view class="map-controls">
        <cover-view class="control-group">
          <cover-view class="map-btn" bindtap="zoomIn">+</cover-view>
          <cover-view class="map-btn" bindtap="zoomOut">-</cover-view>
        </cover-view>

        <cover-view class="control-group">
          <cover-view class="map-btn location-btn" bindtap="moveToCurrentLocation">📍</cover-view>
        </cover-view>

        <!-- 返回按钮 -->
        <cover-view class="back-button">
          <cover-view class="map-btn back-btn" bindtap="goBackToHome">🏠</cover-view>
        </cover-view>
      </cover-view>

      <!-- 导航指引覆盖层 -->
      <cover-view class="navigation-overlay" wx:if="{{isNavigating}}">
        <cover-view class="nav-instruction">
          <cover-text class="instruction-text">{{currentInstruction}}</cover-text>
        </cover-view>
        <cover-view class="nav-info">
          <cover-text class="nav-distance">剩余 {{formatDistance(remainingDistance)}}</cover-text>
          <cover-text class="nav-time">{{formatDuration(remainingTime)}}</cover-text>
        </cover-view>
      </cover-view>
    </map>
  </view>

  <!-- 底部控制面板 -->
  <view class="bottom-panel {{panelExpanded ? 'expanded' : 'collapsed'}}">
    <!-- 面板头部 -->
    <view class="panel-header" bindtap="togglePanel">
      <view class="panel-handle"></view>
      <text class="panel-title">
        {{isNavigating ? '导航中' : (routeData ? '路线方案' : '路线规划')}}
      </text>
      <text class="toggle-icon">{{panelExpanded ? '▼' : '▲'}}</text>
    </view>

    <!-- 面板内容 -->
    <view class="panel-content" wx:if="{{panelExpanded}}">

      <!-- 路线规划模式 -->
      <view class="planning-mode" wx:if="{{!isNavigating && !routeData}}">
        <!-- 起点显示 -->
        <view class="location-row">
          <text class="location-icon start-icon">🟢</text>
          <view class="location-info">
            <text class="location-label">起点</text>
            <text class="location-address">{{currentLocationAddress || '正在获取位置...'}}</text>
          </view>
          <button class="location-btn" bindtap="getCurrentLocation">📍</button>
        </view>

        <!-- 终点输入 -->
        <view class="location-row">
          <text class="location-icon end-icon">🔴</text>
          <view class="destination-input-container">
            <input
              class="destination-input"
              placeholder="输入目的地"
              value="{{destinationInput}}"
              bindinput="onDestinationInput"
              bindconfirm="searchDestination"
              confirm-type="search"
            />
            <button class="search-btn" bindtap="searchDestination" disabled="{{!destinationInput}}">搜索</button>
          </view>
        </view>

        <!-- 搜索结果 -->
        <scroll-view class="search-results" scroll-y="true" wx:if="{{searchResults.length > 0}}">
          <view
            class="result-item"
            wx:for="{{searchResults}}"
            wx:key="id"
            bindtap="selectDestination"
            data-index="{{index}}">
            <text class="result-name">{{item.name}}</text>
            <text class="result-address">{{item.address}}</text>
          </view>
        </scroll-view>

        <!-- 路线类型选择 -->
        <view class="route-type-selector">
          <picker bindchange="onRouteTypeChange" value="{{routeTypeIndex}}" range="{{routeTypes}}" range-key="name">
            <view class="picker-display">
              <text class="route-type-text">{{routeTypes[routeTypeIndex].name}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>

        <!-- 规划按钮 -->
        <button class="plan-route-btn" bindtap="planRoute" disabled="{{!canPlanRoute}}">
          {{planningRoute ? '规划中...' : '开始导航'}}
        </button>
      </view>

      <!-- 路线选择模式 -->
      <view class="route-selection-mode" wx:if="{{!isNavigating && routeData && routeData.routes.length > 0}}">
        <view class="routes-header">
          <text class="routes-title">路线方案 ({{routeData.routes.length}}条)</text>
        </view>

        <scroll-view class="route-list" scroll-y="true">
          <view
            class="route-item {{selectedRouteIndex === index ? 'selected' : ''}}"
            wx:for="{{routeData.routes}}"
            wx:key="id"
            bindtap="selectRoute"
            data-index="{{index}}">

            <view class="route-summary">
              <text class="route-distance">{{formatDistance(item.distance)}}</text>
              <text class="route-duration">{{formatDuration(item.duration)}}</text>
            </view>

            <view class="route-details">
              <text class="route-detail" wx:if="{{item.tolls > 0}}">过路费: ¥{{item.tolls}}</text>
              <text class="route-detail" wx:if="{{item.trafficLights > 0}}">红绿灯: {{item.trafficLights}}个</text>
              <text class="route-detail electrobike-info" wx:if="{{routeTypes[routeTypeIndex].key.includes('electrobike') && item.energyConsumption}}">预计耗电: {{item.energyConsumption}}%</text>
            </view>
          </view>
        </scroll-view>

        <view class="route-actions">
          <button class="action-btn secondary" bindtap="clearRoute">重新规划</button>
          <button class="action-btn primary" bindtap="startNavigation">开始导航</button>
        </view>
      </view>

      <!-- 导航模式 -->
      <view class="navigation-mode" wx:if="{{isNavigating}}">
        <view class="nav-instruction-panel">
          <text class="instruction-text">{{currentInstruction}}</text>
        </view>

        <view class="nav-info-panel">
          <view class="nav-info-item">
            <text class="info-label">剩余距离</text>
            <text class="info-value">{{formatDistance(remainingDistance)}}</text>
          </view>
          <view class="nav-info-item">
            <text class="info-label">预计时间</text>
            <text class="info-value">{{formatDuration(remainingTime)}}</text>
          </view>
        </view>

        <view class="nav-controls">
          <button class="nav-btn" bindtap="pauseNavigation">{{navigationPaused ? '继续' : '暂停'}}</button>
          <button class="nav-btn stop-btn" bindtap="stopNavigation">结束导航</button>
        </view>
      </view>

    </view>
  </view>

  <!-- 加载提示 -->
  <view class="loading-overlay" wx:if="{{planningRoute}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在规划路线...</text>
    </view>
  </view>

  <!-- 错误提示 -->
  <view class="error-toast" wx:if="{{errorMessage}}">
    <text class="error-text">{{errorMessage}}</text>
    <view class="error-close" bindtap="clearError">×</view>
  </view>
</view>
