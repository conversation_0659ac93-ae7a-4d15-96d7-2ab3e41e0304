<!--navigation.wxml-->
<view class="container">
  <!-- 顶部控制栏 -->
  <view class="control-bar">
    <view class="route-selector">
      <picker bindchange="onRouteTypeChange" value="{{routeTypeIndex}}" range="{{routeTypes}}" range-key="name">
        <view class="picker-display">
          <text class="route-type-text">{{routeTypes[routeTypeIndex].name}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>
    
    <view class="action-buttons">
      <button class="action-btn" bindtap="getCurrentLocation">📍</button>
      <button class="action-btn" bindtap="switchStartEnd">🔄</button>
      <button class="action-btn" bindtap="clearRoute">🗑️</button>
      <button class="action-btn test-btn" bindtap="testElectrobikeRoute" wx:if="{{routeTypes[routeTypeIndex].key.includes('electrobike')}}">🛵</button>
    </view>
  </view>

  <!-- 起终点输入区域 -->
  <view class="location-input-section">
    <!-- 起点（自动获取当前位置） -->
    <view class="input-row start-row">
      <view class="location-icon start-icon">🟢</view>
      <view class="location-info">
        <view class="location-label">起点（当前位置）</view>
        <view class="location-address">{{currentLocationAddress || '正在获取当前位置...'}}</view>
      </view>
      <button class="location-action-btn" bindtap="refreshCurrentLocation">
        <text class="action-icon">🔄</text>
      </button>
    </view>

    <!-- 终点（键盘输入） -->
    <view class="input-row end-row">
      <view class="location-icon end-icon">🔴</view>
      <view class="destination-input-container">
        <input
          class="destination-input"
          placeholder="请输入目的地地址或关键词"
          value="{{destinationInput}}"
          bindinput="onDestinationInput"
          bindconfirm="searchDestination"
          focus="{{inputFocus}}"
          confirm-type="search"
        />
        <button class="search-btn" bindtap="searchDestination" disabled="{{!destinationInput}}">
          搜索
        </button>
      </view>
    </view>

    <!-- 搜索结果列表 -->
    <view class="search-results" wx:if="{{searchResults.length > 0}}">
      <view class="results-title">搜索结果</view>
      <scroll-view class="results-scroll" scroll-y="true">
        <view
          class="result-item"
          wx:for="{{searchResults}}"
          wx:key="id"
          bindtap="selectDestination"
          data-index="{{index}}">
          <view class="result-name">{{item.name}}</view>
          <view class="result-address">{{item.address}}</view>
          <view class="result-distance" wx:if="{{item.distance}}">距离: {{item.distance}}米</view>
        </view>
      </scroll-view>
    </view>

    <!-- 选中的目的地 -->
    <view class="selected-destination" wx:if="{{selectedDestination}}">
      <view class="destination-info">
        <view class="destination-name">{{selectedDestination.name}}</view>
        <view class="destination-address">{{selectedDestination.address}}</view>
      </view>
      <button class="clear-btn" bindtap="clearDestination">清除</button>
    </view>

    <!-- 路线规划按钮 -->
    <button class="plan-route-btn" bindtap="planRoute" disabled="{{!canPlanRoute}}">
      {{planningRoute ? '规划路线中...' : '开始路线规划'}}
    </button>
  </view>

  <!-- 地图区域 -->
  <view class="map-container">
    <map
      id="navigationMap"
      class="navigation-map"
      longitude="{{mapCenter.longitude}}"
      latitude="{{mapCenter.latitude}}"
      scale="{{mapScale}}"
      markers="{{markers}}"
      polyline="{{polylines}}"
      show-location="{{true}}"
      bindmarkertap="onMarkerTap"
      bindtap="onMapTap"
      bindregionchange="onRegionChange">
      
      <!-- 地图控件 -->
      <cover-view class="map-controls">
        <cover-view class="control-group">
          <cover-view class="map-btn" bindtap="zoomIn">+</cover-view>
          <cover-view class="map-btn" bindtap="zoomOut">-</cover-view>
        </cover-view>
        
        <cover-view class="control-group">
          <cover-view class="map-btn location-btn" bindtap="moveToCurrentLocation">📍</cover-view>
        </cover-view>
      </cover-view>
    </map>
  </view>

  <!-- 路线信息面板 -->
  <view class="route-info-panel" wx:if="{{routeData && routeData.routes.length > 0}}">
    <view class="panel-header">
      <text class="panel-title">路线方案 ({{routeData.routes.length}}条)</text>
      <view class="panel-toggle" bindtap="togglePanel">
        <text class="toggle-icon">{{panelExpanded ? '▼' : '▲'}}</text>
      </view>
    </view>
    
    <view class="panel-content" wx:if="{{panelExpanded}}">
      <scroll-view class="route-list" scroll-y="true">
        <view 
          class="route-item {{selectedRouteIndex === index ? 'selected' : ''}}" 
          wx:for="{{routeData.routes}}" 
          wx:key="id"
          bindtap="selectRoute"
          data-index="{{index}}">
          
          <view class="route-summary">
            <view class="route-distance">{{formatDistance(item.distance)}}</view>
            <view class="route-duration">{{formatDuration(item.duration)}}</view>
          </view>
          
          <view class="route-details">
            <text class="route-detail-item" wx:if="{{item.tolls > 0}}">过路费: ¥{{item.tolls}}</text>
            <text class="route-detail-item" wx:if="{{item.trafficLights > 0}}">红绿灯: {{item.trafficLights}}个</text>
            <text class="route-detail-item" wx:if="{{item.cost > 0}}">费用: ¥{{item.cost}}</text>
            <!-- 电动车特有信息 -->
            <text class="route-detail-item electrobike-info" wx:if="{{routeTypes[routeTypeIndex].key.includes('electrobike') && item.restriction}}">限行规避: {{item.restriction}}</text>
            <text class="route-detail-item electrobike-info" wx:if="{{routeTypes[routeTypeIndex].key.includes('electrobike') && item.energyConsumption}}">预计耗电: {{item.energyConsumption}}%</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 导航指引面板 -->
  <view class="navigation-guide" wx:if="{{isNavigating}}">
    <view class="guide-header">
      <view class="current-instruction">
        <text class="instruction-text">{{currentInstruction}}</text>
      </view>
      <view class="guide-controls">
        <button class="guide-btn" bindtap="pauseNavigation">暂停</button>
        <button class="guide-btn" bindtap="stopNavigation">结束</button>
      </view>
    </view>
    
    <view class="guide-info">
      <view class="remaining-distance">剩余: {{formatDistance(remainingDistance)}}</view>
      <view class="remaining-time">预计: {{formatDuration(remainingTime)}}</view>
    </view>
  </view>

  <!-- 加载提示 -->
  <view class="loading-overlay" wx:if="{{planningRoute}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在规划路线...</text>
    </view>
  </view>

  <!-- 错误提示 -->
  <view class="error-toast" wx:if="{{errorMessage}}">
    <text class="error-text">{{errorMessage}}</text>
    <view class="error-close" bindtap="clearError">×</view>
  </view>
</view>
