/* navigation.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 顶部控制栏 */
.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  z-index: 100;
}

.route-selector {
  flex: 1;
}

.picker-display {
  display: flex;
  align-items: center;
  padding: 15rpx 20rpx;
  background: #f8f9fa;
  border-radius: 25rpx;
  border: 2rpx solid #e9ecef;
}

.route-type-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 20rpx;
  color: #666;
  margin-left: 10rpx;
}

.action-buttons {
  display: flex;
  gap: 10rpx;
  margin-left: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #007aff;
  color: white;
  border: none;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 起终点输入区域 */
.location-input-section {
  background: white;
  padding: 30rpx 20rpx;
  margin-bottom: 10rpx;
}

.input-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  border: 2rpx solid #e9ecef;
}

.start-row {
  background: #e8f5e8;
  border-color: #4caf50;
}

.end-row {
  background: #fff3e0;
  border-color: #ff9800;
}

.location-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 20rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 起点信息显示 */
.location-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.location-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.location-address {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.location-action-btn {
  width: 60rpx;
  height: 60rpx;
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 15rpx;
}

.action-icon {
  font-size: 20rpx;
}

/* 目的地输入容器 */
.destination-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.destination-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 10rpx 0;
}

.search-btn {
  padding: 12rpx 24rpx;
  background: #ff9800;
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.search-btn[disabled] {
  background: #ccc;
  color: #999;
}

/* 搜索结果 */
.search-results {
  margin-top: 20rpx;
  background: white;
  border-radius: 15rpx;
  border: 2rpx solid #e9ecef;
  max-height: 400rpx;
  overflow: hidden;
}

.results-title {
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.results-scroll {
  max-height: 300rpx;
}

.result-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:active {
  background: #f5f5f5;
}

.result-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.result-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.result-distance {
  font-size: 24rpx;
  color: #999;
}

/* 选中的目的地 */
.selected-destination {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding: 20rpx;
  background: #e3f2fd;
  border-radius: 15rpx;
  border: 2rpx solid #2196f3;
}

.destination-info {
  flex: 1;
}

.destination-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.destination-address {
  font-size: 26rpx;
  color: #666;
}

.clear-btn {
  padding: 10rpx 20rpx;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 15rpx;
  font-size: 24rpx;
  margin-left: 15rpx;
}

.plan-route-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(45deg, #007aff, #5ac8fa);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.plan-route-btn[disabled] {
  background: #ccc;
  color: #999;
}

/* 地图区域 */
.map-container {
  flex: 1;
  position: relative;
}

.navigation-map {
  width: 100%;
  height: 100%;
}

/* 地图控件 */
.map-controls {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.map-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.location-btn {
  font-size: 20rpx;
}

/* 路线信息面板 */
.route-info-panel {
  background: white;
  border-top: 1rpx solid #e9ecef;
  max-height: 400rpx;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.panel-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.panel-toggle {
  padding: 10rpx;
}

.toggle-icon {
  font-size: 20rpx;
  color: #666;
}

.panel-content {
  max-height: 300rpx;
}

.route-list {
  max-height: 300rpx;
}

.route-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}

.route-item.selected {
  background: #e3f2fd;
  border-left: 4rpx solid #007aff;
}

.route-item:active {
  background: #f5f5f5;
}

.route-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.route-distance {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.route-duration {
  font-size: 28rpx;
  color: #007aff;
}

.route-details {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.route-detail-item {
  font-size: 24rpx;
  color: #666;
  background: #f8f9fa;
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
}

/* 导航指引面板 */
.navigation-guide {
  background: #007aff;
  color: white;
  padding: 20rpx;
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.current-instruction {
  flex: 1;
}

.instruction-text {
  font-size: 30rpx;
  font-weight: bold;
}

.guide-controls {
  display: flex;
  gap: 10rpx;
}

.guide-btn {
  padding: 10rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  font-size: 24rpx;
}

.guide-info {
  display: flex;
  justify-content: space-between;
}

.remaining-distance,
.remaining-time {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 40rpx;
  border-radius: 15rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #333;
}

/* 错误提示 */
.error-toast {
  position: fixed;
  top: 100rpx;
  left: 20rpx;
  right: 20rpx;
  background: #ff4444;
  color: white;
  padding: 20rpx;
  border-radius: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1000;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.error-text {
  flex: 1;
  font-size: 28rpx;
}

.error-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;
}
