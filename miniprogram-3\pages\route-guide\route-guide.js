// route-guide.js
const navigationConfig = require('../../utils/navigationConfig.js');
const routeStepParser = require('../../utils/routeStepParser.js');
const staticMapConfig = require('../../utils/staticMapConfig.js');
const errorHandler = require('../../utils/errorHandler.js');
const voiceManager = require('../../utils/voiceManager.js');
const vehicleDataManager = require('../../utils/vehicleDataManager.js');

Page({
  data: {
    // 路线基本信息
    routeType: '步行',
    totalDistance: '',
    totalDuration: '',
    
    // 当前指引
    currentStep: {
      arrow: '↑',
      instruction: '开始导航',
      road: '',
      distance: '',
      remainingDistance: ''
    },
    nextStep: null,
    currentStepIndex: 0,
    
    // 地图相关
    mapCenter: { longitude: 116.397428, latitude: 39.90923 },
    mapScale: 17,
    markers: [],
    polylines: [],

    // 高清静态地图
    useStaticMap: true,
    staticMapUrl: '',
    routeMarkers: [],
    currentPositionX: 0,
    currentPositionY: 0,
    mapStatus: '准备生成地图',
    mapRetryCount: 0,
    
    // 路线步骤
    routeSteps: [],
    
    // 导航状态
    isNavigating: true,
    isAutoDemo: false,
    isRealNavigation: false,
    voiceEnabled: true,
    stepsExpanded: false,
    showOptions: false,
    showVoiceNotification: false,
    voiceText: '',

    // 语音设置
    showVoiceSettings: false,
    voiceVolume: 80,
    voiceRate: 1.0,
    voiceSettings: {
      navigation: true,
      battery: true,
      speed: true,
      charging: true
    },

    // 调试信息
    showDebugInfo: false,
    
    // 原始路线数据
    routeData: null,
    startLocation: null,
    endLocation: null,

    // ✅ 新增：GPS导航相关状态
    currentGPSLocation: null,
    navigationInterval: null,
    offRouteAlerts: 0,
    gpsAccuracy: null,
    lastGPSUpdate: null
  },

  onLoad: function(options) {
    console.log('路线指引页面加载，参数:', options);

    // 从导航页面传递过来的路线数据
    if (options.routeData) {
      try {
        const routeData = JSON.parse(decodeURIComponent(options.routeData));
        console.log('接收到的路线数据:', routeData);

        // 验证路线数据结构
        if (this.validateRouteData(routeData)) {
          console.log('路线数据验证通过，初始化路线');
          this.initializeRoute(routeData);
        } else {
          console.error('路线数据结构无效，使用演示数据');
          this.loadDemoRoute();
        }
      } catch (error) {
        console.error('解析路线数据失败:', error);
        console.log('原始路线数据参数:', options.routeData);
        this.loadDemoRoute();
      }
    } else {
      console.log('没有路线数据参数，加载演示路线');
      this.loadDemoRoute(); // 没有数据时加载演示路线
    }

    // 获取其他参数
    if (options.routeType) {
      this.setData({
        routeType: decodeURIComponent(options.routeType)
      });
    }

    if (options.startLocation) {
      try {
        this.setData({
          startLocation: JSON.parse(decodeURIComponent(options.startLocation))
        });
      } catch (error) {
        console.error('解析起点数据失败:', error);
      }
    }

    if (options.endLocation) {
      try {
        this.setData({
          endLocation: JSON.parse(decodeURIComponent(options.endLocation))
        });
      } catch (error) {
        console.error('解析终点数据失败:', error);
      }
    }

    // 如果没有起终点数据，使用默认数据
    if (!this.data.startLocation || !this.data.endLocation) {
      this.setDefaultLocations();
    }

    // 初始化语音播报管理器
    this.initVoiceManager();
  },

  // 设置默认位置
  setDefaultLocations: function() {
    const defaultStart = {
      longitude: 116.397428,
      latitude: 39.90923,
      name: '天安门广场',
      address: '北京市东城区天安门广场'
    };

    const defaultEnd = {
      longitude: 116.407526,
      latitude: 39.904030,
      name: '王府井大街',
      address: '北京市东城区王府井大街'
    };

    this.setData({
      startLocation: defaultStart,
      endLocation: defaultEnd
    });

    console.log('设置默认起终点:', defaultStart, defaultEnd);
  },

  // 验证路线数据结构
  validateRouteData: function(routeData) {
    if (!routeData || typeof routeData !== 'object') {
      console.error('路线数据不是有效对象');
      return false;
    }

    // 检查必要的属性
    if (!routeData.routes || !Array.isArray(routeData.routes)) {
      console.error('路线数据缺少routes数组');
      return false;
    }

    if (routeData.routes.length === 0) {
      console.error('路线数组为空');
      return false;
    }

    const firstRoute = routeData.routes[0];
    
    // 检查路线的基本属性
    const requiredProps = ['distance', 'duration'];
    for (const prop of requiredProps) {
      if (typeof firstRoute[prop] === 'undefined') {
        console.error(`路线数据缺少必要属性: ${prop}`);
        return false;
      }
    }

    // 检查步骤数据（如果存在）
    if (firstRoute.steps && Array.isArray(firstRoute.steps)) {
      for (let i = 0; i < firstRoute.steps.length; i++) {
        const step = firstRoute.steps[i];
        if (!step.instruction) {
          console.error(`路线步骤 ${i} 缺少instruction属性`);
          return false;
        }
      }
    }

    console.log('路线数据验证通过');
    return true;
  },

  // 加载演示路线
  loadDemoRoute: function() {
    console.log('加载演示路线数据');

    // 创建演示路线数据
    const demoRouteData = {
      routes: [{
        distance: 1200,
        duration: 900, // 15分钟
        // 添加真实的路线polyline数据（天安门到王府井的实际路线）
        polyline: '116.397428,39.90923;116.398428,39.909230;116.399428,39.909230;116.400428,39.909230;116.401428,39.909230;116.402428,39.909230;116.403428,39.909230;116.404428,39.909230;116.405428,39.909230;116.406428,39.909230;116.407526,39.904030',
        steps: [
          {
            instruction: '从天安门广场出发',
            road: '天安门广场',
            distance: 0,
            duration: 0,
            action: 'start',
            orientation: '0',
            polyline: '116.397428,39.90923'
          },
          {
            instruction: '向东直行',
            road: '长安街',
            distance: 300,
            duration: 180,
            action: 'straight',
            orientation: '90',
            polyline: '116.397428,39.90923;116.398428,39.909230;116.399428,39.909230;116.400428,39.909230'
          },
          {
            instruction: '右转进入王府井大街',
            road: '王府井大街',
            distance: 400,
            duration: 240,
            action: 'turn-right',
            orientation: '180',
            polyline: '116.400428,39.909230;116.401428,39.909230;116.402428,39.909230;116.403428,39.909230'
          },
          {
            instruction: '继续直行',
            road: '王府井大街',
            distance: 300,
            duration: 180,
            action: 'straight',
            orientation: '180',
            polyline: '116.403428,39.909230;116.404428,39.909230;116.405428,39.909230;116.406428,39.909230'
          },
          {
            instruction: '左转进入东华门大街',
            road: '东华门大街',
            distance: 200,
            duration: 120,
            action: 'turn-left',
            orientation: '90',
            polyline: '116.406428,39.909230;116.407026,39.906630;116.407526,39.904030'
          },
          {
            instruction: '到达目的地',
            road: '王府井大街',
            distance: 0,
            duration: 0,
            action: 'end',
            orientation: '90',
            polyline: '116.407526,39.904030'
          }
        ]
      }]
    };

    // 确保有起终点数据
    if (!this.data.startLocation || !this.data.endLocation) {
      this.setDefaultLocations();
    }

    // 初始化演示路线
    this.initializeRoute(demoRouteData);
  },

  // 初始化路线
  initializeRoute: function(routeData) {
    console.log('初始化路线数据:', routeData);
    
    this.setData({
      routeData: routeData
    });
    
    if (routeData && routeData.routes && routeData.routes.length > 0) {
      const route = routeData.routes[0]; // 使用第一条路线
      
      // 设置总距离和时间
      this.setData({
        totalDistance: routeStepParser.formatDistance(route.distance),
        totalDuration: routeStepParser.formatDuration(route.duration)
      });
      
      // 解析路线步骤
      this.parseRouteSteps(route);
      
      // 生成高清静态地图
      this.generateStaticRouteMap();

      // 显示路线在地图上
      this.showRouteOnMap();

      // 开始导航指引
      this.startNavigation();
    } else {
      this.showError('路线数据无效');
    }
  },

  // 解析路线步骤
  parseRouteSteps: function(route) {
    // 使用专门的路线步骤解析工具
    const routeSteps = routeStepParser.parseRouteSteps({
      routes: [route]
    }, this.data.routeType);

    console.log('解析的路线步骤:', routeSteps);

    this.setData({
      routeSteps: routeSteps
    });

    // 设置当前步骤
    if (routeSteps.length > 0) {
      this.updateCurrentStep(0);
    }
  },

  // 更新当前步骤
  updateCurrentStep: function(stepIndex) {
    const steps = this.data.routeSteps;
    if (stepIndex >= 0 && stepIndex < steps.length) {
      const currentStep = steps[stepIndex];
      const nextStep = stepIndex + 1 < steps.length ? steps[stepIndex + 1] : null;
      
      // 计算剩余距离
      const remainingDistance = routeStepParser.calculateRemainingDistance(steps, stepIndex);
      
      this.setData({
        currentStep: {
          arrow: currentStep.arrow,
          instruction: currentStep.instruction,
          road: currentStep.road,
          distance: currentStep.distance,
          remainingDistance: routeStepParser.formatDistance(remainingDistance)
        },
        nextStep: nextStep ? {
          arrow: nextStep.arrow,
          instruction: nextStep.instruction
        } : null,
        currentStepIndex: stepIndex
      });
      
      // 更新当前位置指示器
      if (this.data.useStaticMap) {
        this.updateCurrentPositionIndicator();
      }

      // 语音播报
      if (this.data.voiceEnabled) {
        const voiceText = routeStepParser.generateVoiceText(currentStep, remainingDistance, {
          vehicleType: 'electrobike'
        });
        this.playVoiceInstruction(voiceText);
      }
    }
  },

  // 开始导航
  startNavigation: function() {
    this.setData({
      isNavigating: true
    });
    
    // ✅ 调用真实导航功能
    this.startRealNavigation();
  },

  // ✅ 新增：自动演示模式（可选）
  startAutoDemo: function() {
    this.setData({
      isAutoDemo: true
    });
    
    wx.showToast({
      title: '开始自动演示',
      icon: 'success',
      duration: 1500
    });
    
    // 延迟5秒后开始第一步自动演示
    setTimeout(() => {
      this.autoNavigateNextStep();
    }, 5000);
  },

  // ✅ 新增：自动演示下一步（仅演示用）
  autoNavigateNextStep: function() {
    if (!this.data.isNavigating || !this.data.isAutoDemo) return;
    
    const currentIndex = this.data.currentStepIndex;
    const totalSteps = this.data.routeSteps.length;
    
    if (currentIndex + 1 < totalSteps) {
      this.updateCurrentStep(currentIndex + 1);
      
      // 继续下一步自动演示（延迟3-8秒，更真实）
      const randomDelay = 3000 + Math.random() * 5000;
      setTimeout(() => {
        this.autoNavigateNextStep();
      }, randomDelay);
    } else {
      // 到达目的地
      this.arriveDestination();
    }
  },

  // ✅ 新增：手动导航控制
  manualNextStep: function() {
    const currentIndex = this.data.currentStepIndex;
    const totalSteps = this.data.routeSteps.length;
    
    if (currentIndex + 1 < totalSteps) {
      this.updateCurrentStep(currentIndex + 1);
      wx.showToast({
        title: '下一步',
        icon: 'success',
        duration: 1000
      });
    } else {
      this.arriveDestination();
    }
  },

  // ✅ 新增：手动上一步
  manualPrevStep: function() {
    const currentIndex = this.data.currentStepIndex;
    
    if (currentIndex > 0) {
      this.updateCurrentStep(currentIndex - 1);
      wx.showToast({
        title: '上一步',
        icon: 'success',
        duration: 1000
      });
    }
  },

  // 模拟导航进度（已修复：移除自动递归）
  simulateNavigation: function() {
    // ✅ 改为单次执行，不自动循环
    console.log('导航已启动，等待用户操作或GPS更新');
    
    // 在实际项目中，这里应该是：
    // 1. 监听GPS位置变化
    // 2. 根据位置计算当前步骤
    // 3. 更新导航指引
    
    // 演示模式提示
    wx.showToast({
      title: '导航已启动',
      icon: 'success',
      duration: 2000
    });
  },

  // 到达目的地
  arriveDestination: function() {
    this.setData({
      isNavigating: false
    });

    // 播报到达信息
    if (this.data.voiceEnabled) {
      voiceManager.speakElectricVehicleInfo('arrival', {
        message: '您已到达目的地，导航结束，感谢使用电动车导航'
      });
    }

    wx.showModal({
      title: '导航完成',
      content: '您已到达目的地！',
      showCancel: false,
      confirmText: '确定',
      success: () => {
        this.exitNavigation();
      }
    });
  },

  // 初始化语音管理器
  initVoiceManager: function() {
    const that = this;

    voiceManager.init()
      .then(() => {
        console.log('语音管理器初始化成功');

        // 设置语音配置
        voiceManager.setConfig({
          enabled: that.data.voiceEnabled,
          volume: 0.8,
          rate: 1.0
        });

        // 监听车辆数据变化，播报电动车特有信息
        that.startVehicleDataMonitoring();

        // 播报导航开始
        if (that.data.voiceEnabled) {
          voiceManager.speak('导航开始，请注意安全驾驶');
        }
      })
      .catch(error => {
        console.error('语音管理器初始化失败:', error);
      });
  },

  // 语音播报
  playVoiceInstruction: function(instruction) {
    if (!this.data.voiceEnabled) return;

    this.setData({
      showVoiceNotification: true,
      voiceText: instruction
    });

    // 使用语音管理器进行播报
    voiceManager.speakNavigation(instruction)
      .then(() => {
        console.log('语音播报完成:', instruction);
      })
      .catch(error => {
        console.error('语音播报失败:', error);
      });

    // 3秒后隐藏语音提示
    setTimeout(() => {
      this.setData({
        showVoiceNotification: false
      });
    }, 3000);
  },

  // 开始车辆数据监控
  startVehicleDataMonitoring: function() {
    const that = this;

    // 监听电量变化
    vehicleDataManager.onBatteryUpdate((batteryData) => {
      if (batteryData.level <= 20 && batteryData.level > 15) {
        voiceManager.speakElectricVehicleInfo('lowBattery', batteryData);
      } else if (batteryData.level <= 15) {
        voiceManager.speakElectricVehicleInfo('lowBattery', {
          ...batteryData,
          urgent: true
        });
      }
    });

    // 监听速度变化（限速提醒）
    vehicleDataManager.onSpeedUpdate((speedData) => {
      // 这里可以根据路段信息判断是否超速
      // 暂时使用固定限速值演示
      const speedLimit = 25; // 电动车限速25km/h
      if (speedData.current > speedLimit) {
        voiceManager.speakElectricVehicleInfo('speedLimit', {
          limit: speedLimit,
          current: speedData.current
        });
      }
    });

    // 监听连接状态变化
    vehicleDataManager.onConnectionChange((connected) => {
      if (!connected) {
        voiceManager.speak('车辆连接已断开，请检查设备连接');
      } else {
        voiceManager.speak('车辆连接已恢复');
      }
    });
  },

  // 播报电动车特有信息
  speakElectricVehicleInfo: function(type, data) {
    if (!this.data.voiceEnabled) return;

    voiceManager.speakElectricVehicleInfo(type, data)
      .then(() => {
        console.log('电动车信息播报完成:', type, data);
      })
      .catch(error => {
        console.error('电动车信息播报失败:', error);
      });
  },

  // 生成高清静态路线地图（增强版）
  generateStaticRouteMap: function() {
    console.log('开始生成静态路线地图');
    console.log('起点:', this.data.startLocation);
    console.log('终点:', this.data.endLocation);

    // ✅ 修复数据完整性检查 - 传递正确的参数
    const validation = this.validateRouteDataForMap();
    console.log('数据验证结果:', validation);

    if (!validation.isValid && !validation.canProceed) {
      console.error('数据验证失败:', validation.issues);
      this.handleMapGenerationError('数据验证失败', validation.issues);
      return;
    }

    // 显示验证警告（如果有）
    if (validation.issues.length > 0) {
      console.warn('数据验证发现问题:', validation.issues);
    }

    const that = this;

    // 设置加载状态
    this.setData({
      mapStatus: '正在生成高清路线图...'
    });

    // 检查staticMapConfig是否存在buildRouteStaticMapUrl方法
    if (staticMapConfig.buildRouteStaticMapUrl) {
      // 使用专门的路线静态地图工具
      staticMapConfig.buildRouteStaticMapUrl(
        this.data.startLocation,
        this.data.endLocation,
        this.data.routeData,
        {
          routeType: this.data.routeType || 'walking', // 传递路线类型
          size: '600*400',  // 适中尺寸，适合手机屏幕
          scale: 2,         // 高清模式，实际1200*800
          zoom: 15,         // 稍高缩放级别，确保路线清晰
          traffic: 0        // 不显示路况
        }
      )
        .then(result => {
          console.log('静态路线地图生成成功:', result);

          // URL有效性预检测
          return that.validateMapUrl(result.mapUrl);
        })
        .then(validatedResult => {
          console.log('地图URL验证成功:', validatedResult);

          that.setData({
            staticMapUrl: validatedResult.url,
            mapStatus: '高清路线图加载成功'
          });

          // 计算转向标识位置
          that.calculateRouteMarkerPositions();

          wx.showToast({
            title: '高清路线图已生成',
            icon: 'success',
            duration: 1500
          });
        })
        .catch(error => {
          console.error('静态路线地图生成或验证失败:', error);
          that.handleMapGenerationError('静态地图生成失败', [error.message || error]);
        });
    } else {
      console.log('buildRouteStaticMapUrl方法不存在，使用基础静态地图');
      this.generateBasicStaticMap();
    }
  },

  // ✅ 新增：专门用于地图生成的数据验证
  validateRouteDataForMap: function() {
    const validation = {
      isValid: false,
      canProceed: false,
      issues: [],
      recommendations: []
    };

    // 检查基本位置信息
    if (!this.data.startLocation || !this.data.endLocation) {
      validation.issues.push('缺少起点或终点位置信息');
      validation.recommendations.push('使用默认位置数据');
      validation.canProceed = true; // 可以用默认数据继续
    } else {
      validation.isValid = true;
      validation.canProceed = true;
    }

    // 检查路线数据
    if (!this.data.routeData) {
      validation.issues.push('缺少路线数据');
      validation.recommendations.push('使用基础地图显示起终点');
      validation.canProceed = true; // 可以生成基础地图
    } else {
      // 使用原有的验证函数
      const routeValidation = this.validateRouteData(this.data.routeData);
      if (!routeValidation) {
        validation.issues.push('路线数据格式无效');
        validation.recommendations.push('使用基础地图显示起终点');
        validation.canProceed = true;
      }
    }

    // 至少要有位置信息才能生成地图
    validation.isValid = this.data.startLocation && this.data.endLocation;
    validation.canProceed = validation.isValid;

    return validation;
  },

  // URL有效性预检测
  validateMapUrl: function(mapUrl) {
    return new Promise((resolve, reject) => {
      if (!mapUrl || typeof mapUrl !== 'string') {
        reject(new Error('无效的地图URL'));
        return;
      }

      // 基本URL格式检查
      if (!mapUrl.startsWith('https://restapi.amap.com/v3/staticmap')) {
        reject(new Error('地图URL格式不正确'));
        return;
      }

      // URL长度检查
      if (mapUrl.length > 2048) {
        console.warn('地图URL可能过长:', mapUrl.length);
      }

      // 使用staticMapConfig的testStaticMapUrl方法进行验证
      if (staticMapConfig.testStaticMapUrl) {
        staticMapConfig.testStaticMapUrl(mapUrl)
          .then(result => {
            resolve({
              url: mapUrl,
              isValid: true,
              message: result.message || '地图URL验证成功'
            });
          })
          .catch(error => {
            console.error('地图URL验证失败:', error);
            reject(new Error(`地图URL验证失败: ${error.message || '未知错误'}`));
          });
      } else {
        // 如果没有验证方法，直接返回成功
        console.warn('缺少URL验证方法，跳过验证');
        resolve({
          url: mapUrl,
          isValid: true,
          message: '跳过URL验证'
        });
      }
    });
  },

  // 统一的地图生成错误处理
  handleMapGenerationError: function(errorType, details = []) {
    console.error(`地图生成错误 - ${errorType}:`, details);

    const that = this;
    let errorMessage = '';
    let shouldRetry = false;
    let fallbackAction = null;

    // 根据错误类型确定处理策略
    switch (errorType) {
      case '数据验证失败':
        errorMessage = '路线数据不完整，尝试使用基础地图';
        fallbackAction = () => that.generateBasicStaticMap();
        break;

      case '静态地图生成失败':
        errorMessage = '高清地图生成失败，尝试基础地图';
        shouldRetry = true;
        fallbackAction = () => that.generateBasicStaticMap();
        break;

      case '基础地图生成失败':
        errorMessage = '静态地图不可用，切换到动态地图';
        fallbackAction = () => that.fallbackToDynamicMap();
        break;

      default:
        errorMessage = '地图生成遇到问题，正在尝试其他方案';
        fallbackAction = () => that.generateBasicStaticMap();
    }

    // 更新状态
    this.setData({
      mapStatus: errorMessage
    });

    // 显示用户友好的提示
    wx.showModal({
      title: '地图加载提示',
      content: `${errorMessage}\n\n${details.length > 0 ? '详细信息：' + details.join(', ') : ''}`,
      showCancel: shouldRetry,
      cancelText: '重试',
      confirmText: '继续',
      success: function(res) {
        if (res.cancel && shouldRetry) {
          // 用户选择重试
          setTimeout(() => {
            that.generateStaticRouteMap();
          }, 1000);
        } else {
          // 执行降级方案
          if (fallbackAction) {
            setTimeout(fallbackAction, 500);
          }
        }
      }
    });
  },

  // 降级到动态地图
  fallbackToDynamicMap: function() {
    console.log('降级到动态地图模式');

    this.setData({
      useStaticMap: false,
      mapStatus: '使用动态地图模式'
    });

    // 显示路线在动态地图上
    this.showRouteOnMap();

    wx.showToast({
      title: '已切换到动态地图',
      icon: 'none',
      duration: 2000
    });
  },

  // 生成基础静态地图（增强版）
  generateBasicStaticMap: function() {
    console.log('生成基础静态地图');

    const that = this;
    const startLoc = this.data.startLocation;
    const endLoc = this.data.endLocation;

    if (!startLoc || !endLoc) {
      console.error('缺少位置信息，无法生成基础地图');
      this.handleMapGenerationError('基础地图生成失败', ['缺少起终点位置信息']);
      return;
    }

    // 设置加载状态
    this.setData({
      mapStatus: '正在生成基础地图...'
    });

    // 计算中心点
    const centerLng = (startLoc.longitude + endLoc.longitude) / 2;
    const centerLat = (startLoc.latitude + endLoc.latitude) / 2;

    // 构建基础静态地图URL
    try {
      const mapUrl = staticMapConfig.buildStandardStaticMapUrl({
        location: `${centerLng},${centerLat}`,
        zoom: 15,
        size: '600*400',
        scale: 2,
        markers: `large,0x4caf50,S:${startLoc.longitude},${startLoc.latitude}|large,0xf44336,E:${endLoc.longitude},${endLoc.latitude}`
      });

      console.log('基础静态地图生成成功:', mapUrl);

      // URL有效性验证
      this.validateMapUrl(mapUrl)
        .then(validatedResult => {
          console.log('基础地图URL验证成功');

          that.setData({
            staticMapUrl: validatedResult.url,
            mapStatus: '基础地图加载成功'
          });

          // 计算转向标识位置
          that.calculateRouteMarkerPositions();

          wx.showToast({
            title: '基础地图已生成',
            icon: 'success',
            duration: 1500
          });
        })
        .catch(error => {
          console.error('基础地图URL验证失败:', error);
          that.handleMapGenerationError('基础地图生成失败', [error.message]);
        });

    } catch (error) {
      console.error('基础静态地图生成失败:', error);
      this.handleMapGenerationError('基础地图生成失败', [error.message || '构建地图URL失败']);
    }
  },

  // 构建路线路径字符串
  buildRoutePathString: function() {
    if (!this.data.routeData || !this.data.routeData.routes || this.data.routeData.routes.length === 0) {
      return '';
    }

    const route = this.data.routeData.routes[0];
    const steps = route.steps || [];

    // 收集所有路径点
    const pathPoints = [];

    // 添加起点
    if (this.data.startLocation) {
      pathPoints.push(`${this.data.startLocation.longitude},${this.data.startLocation.latitude}`);
    }

    // 添加路径中的关键点
    steps.forEach(step => {
      if (step.polyline) {
        // 解析polyline中的坐标点（简化处理）
        const coords = this.parsePolylineCoords(step.polyline);
        pathPoints.push(...coords);
      }
    });

    // 添加终点
    if (this.data.endLocation) {
      pathPoints.push(`${this.data.endLocation.longitude},${this.data.endLocation.latitude}`);
    }

    // 构建路径字符串：线宽,颜色,透明度,填充色,填充透明度:坐标点
    return `5,0x007aff,1,,:${pathPoints.join(';')}`;
  },

  // 构建路线标记字符串
  buildRouteMarkersString: function() {
    const markers = [];

    // 起点标记
    if (this.data.startLocation) {
      markers.push(`large,0x4caf50,S:${this.data.startLocation.longitude},${this.data.startLocation.latitude}`);
    }

    // 终点标记
    if (this.data.endLocation) {
      markers.push(`large,0xf44336,E:${this.data.endLocation.longitude},${this.data.endLocation.latitude}`);
    }

    return markers.join('|');
  },

  // 解析polyline坐标（简化版本）
  parsePolylineCoords: function(polyline) {
    // 这里应该实现polyline解码算法
    // 为了简化，返回空数组，实际项目中需要实现完整的polyline解码
    return [];
  },

  // 计算转向标识在静态地图上的位置
  calculateRouteMarkerPositions: function() {
    const routeSteps = this.data.routeSteps;
    const routeMarkers = [];

    console.log('计算转向标识位置，路线步骤数:', routeSteps.length);

    if (routeSteps.length === 0) {
      console.log('没有路线步骤，无法计算标记位置');
      return;
    }

    // 显示所有重要的转向点和起终点
    const importantActions = ['start', 'turn-left', 'turn-right', 'turn-sharp-left', 'turn-sharp-right', 'uturn', 'end'];

    routeSteps.forEach((step, index) => {
      if (importantActions.includes(step.action)) {
        const position = this.calculateMarkerPosition(step, index);
        if (position) {
          routeMarkers.push({
            x: position.x,
            y: position.y,
            arrow: step.arrow,
            step: index + 1,
            instruction: step.instruction.length > 8 ?
                        step.instruction.substring(0, 8) + '...' :
                        step.instruction,
            action: step.action
          });
        }
      }
    });

    console.log('生成的转向标记:', routeMarkers);

    // 限制标记数量，避免过于拥挤
    const maxMarkers = 6;
    if (routeMarkers.length > maxMarkers) {
      // 保留起点、终点和重要转向点
      const filteredMarkers = [];

      // 始终保留起点
      const startMarker = routeMarkers.find(m => m.action === 'start');
      if (startMarker) filteredMarkers.push(startMarker);

      // 保留转向点
      const turnMarkers = routeMarkers.filter(m => m.action.includes('turn'));
      const step = Math.ceil(turnMarkers.length / (maxMarkers - 2));
      for (let i = 0; i < turnMarkers.length; i += step) {
        if (filteredMarkers.length < maxMarkers - 1) {
          filteredMarkers.push(turnMarkers[i]);
        }
      }

      // 始终保留终点
      const endMarker = routeMarkers.find(m => m.action === 'end');
      if (endMarker) filteredMarkers.push(endMarker);

      this.setData({
        routeMarkers: filteredMarkers
      });
    } else {
      this.setData({
        routeMarkers: routeMarkers
      });
    }

    console.log('最终显示的标记:', this.data.routeMarkers);
  },

  // 计算标记点在地图图片上的像素位置
  calculateMarkerPosition: function(step, index) {
    const mapWidth = 600;   // 地图宽度（对应新的尺寸）
    const mapHeight = 400;  // 地图高度（对应新的尺寸）
    const totalSteps = this.data.routeSteps.length;

    console.log(`计算标记位置 - 步骤${index}/${totalSteps}, 动作:${step.action}`);

    if (totalSteps <= 1) {
      return {
        x: mapWidth / 2,
        y: mapHeight / 2
      };
    }

    // 根据步骤类型和位置计算
    let x, y;

    if (step.action === 'start') {
      // 起点位置：左上角区域
      x = mapWidth * 0.2;
      y = mapHeight * 0.3;
    } else if (step.action === 'end') {
      // 终点位置：右下角区域
      x = mapWidth * 0.8;
      y = mapHeight * 0.7;
    } else {
      // 中间转向点：根据进度分布
      const progress = index / (totalSteps - 1);

      // 创建S型路径分布，更符合实际路线
      x = mapWidth * 0.2 + (mapWidth * 0.6 * progress);
      y = mapHeight * 0.3 + (mapHeight * 0.4 * Math.sin(progress * Math.PI));

      // 根据转向类型微调位置
      if (step.action === 'turn-left') {
        x -= 30;
      } else if (step.action === 'turn-right') {
        x += 30;
      }
    }

    // 确保标记在地图范围内
    x = Math.max(40, Math.min(mapWidth - 40, x));
    y = Math.max(40, Math.min(mapHeight - 40, y));

    console.log(`标记位置计算结果: x=${x}, y=${y}`);

    return { x: Math.round(x), y: Math.round(y) };
  },

  // 显示路线在动态地图上
  showRouteOnMap: function() {
    if (!this.data.routeData) return;

    // 转换为地图polyline格式
    const polylines = navigationConfig.convertToMapPolyline(this.data.routeData, '#007aff');

    // 设置起终点标记
    const markers = [];
    if (this.data.startLocation) {
      markers.push({
        id: 1,
        latitude: this.data.startLocation.latitude,
        longitude: this.data.startLocation.longitude,
        width: 30,
        height: 30,
        title: '起点',
        callout: {
          content: '起点',
          color: '#ffffff',
          bgColor: '#4caf50',
          borderRadius: 5,
          padding: 5
        }
      });
    }

    if (this.data.endLocation) {
      markers.push({
        id: 2,
        latitude: this.data.endLocation.latitude,
        longitude: this.data.endLocation.longitude,
        width: 30,
        height: 30,
        title: '终点',
        callout: {
          content: '终点',
          color: '#ffffff',
          bgColor: '#f44336',
          borderRadius: 5,
          padding: 5
        }
      });
    }

    this.setData({
      polylines: polylines,
      markers: markers
    });

    // 调整地图视野
    this.fitRouteInView();
  },

  // 调整地图视野
  fitRouteInView: function() {
    if (this.data.startLocation && this.data.endLocation) {
      const start = this.data.startLocation;
      const end = this.data.endLocation;
      
      const centerLng = (start.longitude + end.longitude) / 2;
      const centerLat = (start.latitude + end.latitude) / 2;
      
      this.setData({
        mapCenter: {
          longitude: centerLng,
          latitude: centerLat
        },
        mapScale: 15
      });
    }
  },

  // 显示错误信息
  showError: function(message) {
    wx.showModal({
      title: '错误',
      content: message,
      showCancel: false
    });
  },

  onShow: function() {
    console.log('路线指引页面显示');
    console.log('当前数据状态:', {
      useStaticMap: this.data.useStaticMap,
      staticMapUrl: this.data.staticMapUrl,
      routeSteps: this.data.routeSteps.length,
      routeMarkers: this.data.routeMarkers.length,
      startLocation: this.data.startLocation,
      endLocation: this.data.endLocation
    });

    // 如果没有地图URL，尝试重新生成
    if (this.data.useStaticMap && !this.data.staticMapUrl) {
      console.log('静态地图URL为空，重新生成');
      this.generateStaticRouteMap();
    }

    if (this.data.isNavigating && this.data.isRealNavigation && !this.data.navigationInterval) {
      this.startGPSMonitoring();
      console.log('页面显示，GPS监听已恢复');
    }
  },

  onReady: function() {
    console.log('路线指引页面准备完成');

    // 创建地图上下文
    this.mapContext = wx.createMapContext('routeMap', this);
    console.log('路线引导地图上下文创建成功');

    // 验证地图API密钥
    const amapKey = wx.getStorageSync('AMAP_KEY');
    console.log('路线引导页面地图API密钥:', amapKey);

    // 初始化地图中心点
    if (!this.data.mapCenter) {
      this.setData({
        mapCenter: {
          longitude: 116.397428,
          latitude: 39.90923
        },
        mapScale: 16
      });
    }

    // 如果有路线数据，显示路线
    if (this.data.routeSteps && this.data.routeSteps.length > 0) {
      this.showRouteOnMap();
    }

    console.log('路线引导地图初始化完成');

    // 延迟检查地图显示状态
    setTimeout(() => {
      this.checkMapDisplayStatus();
    }, 1000);
  },

  // 检查地图显示状态
  checkMapDisplayStatus: function() {
    console.log('检查路线引导地图显示状态...');
    console.log('使用静态地图:', this.data.useStaticMap);
    console.log('静态地图URL:', this.data.staticMapUrl ? '已生成' : '未生成');
    console.log('地图中心点:', this.data.mapCenter);
    console.log('地图缩放级别:', this.data.mapScale);
    console.log('标记数量:', this.data.markers.length);
    console.log('路线数量:', this.data.polylines.length);
    console.log('路线步骤数量:', this.data.routeSteps.length);

    if (this.mapContext) {
      console.log('路线引导地图上下文已创建');
    } else {
      console.error('路线引导地图上下文未创建');
    }
  },

  // 强制显示动态地图（调试用）
  forceShowDynamicMap: function() {
    console.log('强制显示动态地图');
    this.setData({
      useStaticMap: false,
      mapStatus: '使用动态地图模式'
    });

    // 确保地图数据正确
    if (!this.data.mapCenter.longitude || !this.data.mapCenter.latitude) {
      this.setData({
        mapCenter: {
          longitude: 116.397428,
          latitude: 39.90923
        }
      });
    }

    // 显示路线
    if (this.data.routeSteps && this.data.routeSteps.length > 0) {
      this.showRouteOnMap();
    }

    wx.showToast({
      title: '已强制显示动态地图',
      icon: 'none'
    });
  },

  // 事件处理函数
  toggleVoice: function() {
    const voiceEnabled = !this.data.voiceEnabled;
    this.setData({
      voiceEnabled: voiceEnabled
    });

    // 同步到语音管理器
    voiceManager.setEnabled(voiceEnabled);

    wx.showToast({
      title: voiceEnabled ? '语音已开启' : '语音已关闭',
      icon: 'success',
      duration: 1500
    });

    // 播报状态变化
    if (voiceEnabled) {
      setTimeout(() => {
        voiceManager.speak('语音播报已开启');
      }, 1600);
    }
  },

  // 显示语音设置
  showVoiceSettings: function() {
    this.setData({
      showVoiceSettings: true
    });
  },

  // 隐藏语音设置
  hideVoiceSettings: function() {
    this.setData({
      showVoiceSettings: false
    });
  },

  // 语音开关变化
  onVoiceEnabledChange: function(e) {
    const enabled = e.detail.value;
    this.setData({
      voiceEnabled: enabled
    });

    voiceManager.setEnabled(enabled);

    if (enabled) {
      voiceManager.speak('语音播报已开启');
    }
  },

  // 音量变化
  onVolumeChange: function(e) {
    const volume = e.detail.value;
    this.setData({
      voiceVolume: volume
    });

    voiceManager.setVolume(volume / 100);
  },

  // 语速变化
  onRateChange: function(e) {
    const rate = e.detail.value;
    this.setData({
      voiceRate: rate
    });

    voiceManager.setRate(rate);
  },

  // 测试语音
  testVoice: function() {
    if (this.data.voiceEnabled) {
      voiceManager.speak('这是语音播报测试，前方100米左转');
    } else {
      wx.showToast({
        title: '请先开启语音播报',
        icon: 'none'
      });
    }
  },

  exitNavigation: function() {
    wx.showModal({
      title: '退出导航',
      content: '确定要退出当前导航吗？',
      success: (res) => {
        if (res.confirm) {
          // 清理GPS监听
          if (this.data.navigationInterval) {
            clearInterval(this.data.navigationInterval);
          }
          
          // 重置状态
          this.setData({
            isNavigating: false,
            isRealNavigation: false,
            isAutoDemo: false,
            navigationInterval: null,
            currentGPSLocation: null,
            offRouteAlerts: 0
          });
          
          // 返回上一页
          wx.navigateBack({
            delta: 1
          });
        }
      }
    });
  },

  toggleStepsPanel: function() {
    this.setData({
      stepsExpanded: !this.data.stepsExpanded
    });
  },

  jumpToStep: function(e) {
    const index = e.currentTarget.dataset.index;
    this.updateCurrentStep(index);
  },

  pauseNavigation: function() {
    // 暂停GPS监听
    if (this.data.navigationInterval) {
      clearInterval(this.data.navigationInterval);
      this.setData({
        navigationInterval: null
      });
    }
    
    this.setData({
      isNavigating: false
    });
    
    wx.showToast({
      title: '导航已暂停',
      icon: 'success',
      duration: 1500
    });
  },

  resumeNavigation: function() {
    this.setData({
      isNavigating: true
    });
    
    // ✅ 重新启动真实导航
    this.startRealNavigation();
  },

  showRouteOptions: function() {
    this.setData({
      showOptions: true
    });
  },

  hideRouteOptions: function() {
    this.setData({
      showOptions: false
    });
  },

  preventClose: function() {
    // 阻止事件冒泡，防止点击内容区域关闭弹窗
  },

  // 地图控制
  centerToLocation: function() {
    // 居中到当前位置
    this.fitRouteInView();
  },

  zoomIn: function() {
    this.setData({
      mapScale: Math.min(this.data.mapScale + 1, 20)
    });
  },

  zoomOut: function() {
    this.setData({
      mapScale: Math.max(this.data.mapScale - 1, 5)
    });
  },

  onMapRegionChange: function(e) {
    if (e.type === 'end') {
      this.setData({
        mapCenter: {
          longitude: e.detail.centerLocation.longitude,
          latitude: e.detail.centerLocation.latitude
        }
      });
    }
  },

  // 路线选项
  switchToWalking: function() {
    wx.showToast({
      title: '切换到步行模式',
      icon: 'success'
    });
    this.hideRouteOptions();
  },

  switchToBicycling: function() {
    wx.showToast({
      title: '切换到骑行模式',
      icon: 'success'
    });
    this.hideRouteOptions();
  },

  avoidCongestion: function() {
    wx.showToast({
      title: '重新规划路线',
      icon: 'success'
    });
    this.hideRouteOptions();
  },

  shareRoute: function() {
    wx.showShareMenu({
      withShareTicket: true
    });
    this.hideRouteOptions();
  },

  recalculateRoute: function() {
    wx.showToast({
      title: '重新规划路线',
      icon: 'loading'
    });

    // 重新规划路线逻辑
    setTimeout(() => {
      wx.showToast({
        title: '路线已更新',
        icon: 'success'
      });
    }, 2000);
  },

  // 地图模式切换
  toggleMapMode: function() {
    this.setData({
      useStaticMap: !this.data.useStaticMap
    });

    wx.showToast({
      title: this.data.useStaticMap ? '切换到高清路线图' : '切换到动态地图',
      icon: 'success'
    });
  },

  switchToStaticMap: function() {
    this.setData({
      useStaticMap: true
    });

    if (!this.data.staticMapUrl) {
      this.generateStaticRouteMap();
    }

    wx.showToast({
      title: '高清路线图模式',
      icon: 'success'
    });
  },

  switchToDynamicMap: function() {
    this.setData({
      useStaticMap: false
    });

    wx.showToast({
      title: '动态地图模式',
      icon: 'success'
    });
  },

  // 静态地图加载事件
  onStaticMapLoad: function(e) {
    console.log('静态地图加载成功:', e);

    // 更新当前位置指示器位置
    this.updateCurrentPositionIndicator();

    wx.showToast({
      title: '高清路线图加载完成',
      icon: 'success',
      duration: 1500
    });
  },

  onStaticMapError: function(e) {
    console.error('静态地图加载失败:', e);
    console.log('当前静态地图URL:', this.data.staticMapUrl);
    console.log('错误详情:', e.detail);

    const that = this;
    let errorDetails = [];

    // 分析错误类型
    if (e.detail) {
      if (e.detail.errMsg) {
        errorDetails.push(e.detail.errMsg);
      }
      if (e.detail.statusCode) {
        errorDetails.push(`HTTP状态码: ${e.detail.statusCode}`);
      }
    }

    // 更新状态
    this.setData({
      mapStatus: '地图加载失败，正在尝试修复...'
    });

    // 智能重试策略
    if (this.data.staticMapUrl && !this.data.mapRetryCount) {
      // 第一次失败，尝试重新生成基础地图
      console.log('第一次加载失败，尝试重新生成基础地图');

      this.setData({
        mapRetryCount: 1
      });

      wx.showModal({
        title: '地图加载失败',
        content: '正在尝试重新生成地图，请稍候...',
        showCancel: false,
        confirmText: '确定',
        success: function() {
          setTimeout(() => {
            that.generateBasicStaticMap();
          }, 1000);
        }
      });

    } else if (this.data.mapRetryCount && this.data.mapRetryCount < 2) {
      // 第二次失败，尝试不同的地图参数
      console.log('第二次加载失败，尝试简化地图参数');

      this.setData({
        mapRetryCount: this.data.mapRetryCount + 1
      });

      // 使用更简单的地图参数重试
      this.generateSimplifiedMap();

    } else {
      // 多次失败，降级到动态地图
      console.log('多次重试失败，降级到动态地图');

      this.setData({
        mapRetryCount: 0
      });

      wx.showModal({
        title: '静态地图不可用',
        content: '静态地图加载多次失败，是否切换到动态地图？\n\n错误信息：' + errorDetails.join(', '),
        cancelText: '重试',
        confirmText: '切换',
        success: function(res) {
          if (res.confirm) {
            that.fallbackToDynamicMap();
          } else {
            // 用户选择重试，重置计数器
            that.setData({
              mapRetryCount: 0
            });
            setTimeout(() => {
              that.generateStaticRouteMap();
            }, 1000);
          }
        }
      });
    }
  },

  // 生成简化地图（最后的重试方案）
  generateSimplifiedMap: function() {
    console.log('生成简化地图');

    const that = this;
    const startLoc = this.data.startLocation;
    const endLoc = this.data.endLocation;

    if (!startLoc || !endLoc) {
      this.fallbackToDynamicMap();
      return;
    }

    // 设置加载状态
    this.setData({
      mapStatus: '正在生成简化地图...'
    });

    try {
      // 使用最简单的参数
      const mapUrl = staticMapConfig.buildStandardStaticMapUrl({
        location: `${startLoc.longitude},${startLoc.latitude}`,
        zoom: 12,  // 降低缩放级别
        size: '400*300',  // 减小尺寸
        scale: 1,  // 不使用高清模式
        markers: `mid,0x4caf50,A:${startLoc.longitude},${startLoc.latitude}|mid,0xf44336,B:${endLoc.longitude},${endLoc.latitude}`
      });

      console.log('简化地图生成成功:', mapUrl);

      that.setData({
        staticMapUrl: mapUrl,
        mapStatus: '简化地图加载成功'
      });

      wx.showToast({
        title: '简化地图已生成',
        icon: 'success',
        duration: 1500
      });

    } catch (error) {
      console.error('简化地图生成失败:', error);
      this.fallbackToDynamicMap();
    }
  },

  // 更新当前位置指示器
  updateCurrentPositionIndicator: function() {
    const currentStepIndex = this.data.currentStepIndex;
    const totalSteps = this.data.routeSteps.length;

    if (totalSteps === 0) return;

    // 根据当前步骤计算位置
    const progress = currentStepIndex / totalSteps;
    const mapWidth = 600;   // 对应新的地图尺寸
    const mapHeight = 400;

    // 模拟当前位置（实际应该根据GPS位置计算）
    const currentX = mapWidth * 0.2 + (mapWidth * 0.6 * progress);
    const currentY = mapHeight * 0.3 + (mapHeight * 0.4 * Math.sin(progress * Math.PI));

    this.setData({
      currentPositionX: currentX,
      currentPositionY: currentY
    });
  },

  // 测试地图生成
  testMapGeneration: function() {
    console.log('手动测试地图生成');

    wx.showLoading({
      title: '生成测试地图...'
    });

    // 强制重新生成地图
    this.setData({
      staticMapUrl: '',
      useStaticMap: true
    });

    // 确保有演示数据
    if (!this.data.routeSteps || this.data.routeSteps.length === 0) {
      this.loadDemoRoute();
    }

    // 重新生成地图
    setTimeout(() => {
      this.generateStaticRouteMap();
      wx.hideLoading();
    }, 500);

    wx.showToast({
      title: '正在重新生成地图',
      icon: 'loading',
      duration: 2000
    });
  },

  // 切换调试信息显示
  toggleDebugInfo: function() {
    this.setData({
      showDebugInfo: !this.data.showDebugInfo
    });

    if (this.data.showDebugInfo) {
      console.log('显示调试信息');
      console.log('当前完整状态:', this.data);
    }
  },

  // 页面卸载时清理资源
  onUnload: function() {
    // 清理GPS监听定时器
    if (this.data.navigationInterval) {
      clearInterval(this.data.navigationInterval);
      console.log('GPS监听定时器已清理');
    }
    
    // 停止导航
    this.setData({
      isNavigating: false,
      isRealNavigation: false,
      isAutoDemo: false,
      navigationInterval: null
    });
    
    console.log('路线指引页面资源清理完成');
  },

  // ✅ 新增：页面隐藏时暂停GPS监听（省电）
  onHide: function() {
    if (this.data.navigationInterval && this.data.isRealNavigation) {
      clearInterval(this.data.navigationInterval);
      this.setData({
        navigationInterval: null
      });
      console.log('页面隐藏，GPS监听已暂停');
    }
  },

  // ✅ 新增：真实GPS导航功能
  startRealNavigation: function() {
    console.log('开始真实GPS导航');
    
    // 询问用户选择导航模式
    wx.showModal({
      title: '选择导航模式',
      content: '请选择导航模式：\n\n🛰️ GPS导航：基于实时位置自动导航\n🎮 演示模式：手动体验导航功能',
      cancelText: '演示模式',
      confirmText: 'GPS导航',
      success: (res) => {
        if (res.confirm) {
          this.initRealGPSNavigation();
        } else {
          this.initDemoNavigation();
        }
      }
    });
  },

  // ✅ 新增：初始化真实GPS导航
  initRealGPSNavigation: function() {
    this.setData({
      isNavigating: true,
      isRealNavigation: true,
      isAutoDemo: false,
      currentGPSLocation: null,
      navigationInterval: null,
      offRouteAlerts: 0
    });

    // 检查GPS权限
    this.checkGPSPermission()
      .then(() => {
        // 开始GPS监听
        this.startGPSMonitoring();
        
        wx.showToast({
          title: 'GPS导航已启动',
          icon: 'success',
          duration: 2000
        });
      })
      .catch(error => {
        console.error('GPS权限检查失败:', error);
        wx.showModal({
          title: 'GPS权限',
          content: 'GPS导航需要位置权限，是否前往设置开启？',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting();
            } else {
              this.initDemoNavigation();
            }
          }
        });
      });
  },

  // ✅ 使用amapManager中的权限检查
  checkGPSPermission: function() {
    const amapManager = require('../../utils/amapManager.js');
    return amapManager.checkLocationPermission()
      .then(result => {
        if (!result.authorized) {
          return amapManager.requestLocationPermission();
        }
        return result;
      });
  },

  // ✅ 新增：开始GPS监听
  startGPSMonitoring: function() {
    const that = this;
    
    // 立即获取一次位置
    this.updateGPSLocation();
    
    // 每3秒更新一次GPS位置
    const interval = setInterval(() => {
      if (!that.data.isNavigating || !that.data.isRealNavigation) {
        clearInterval(interval);
        return;
      }
      that.updateGPSLocation();
    }, 3000);
    
    this.setData({
      navigationInterval: interval
    });
  },

  // ✅ 新增：更新GPS位置
  updateGPSLocation: function() {
    const that = this;
    
    wx.getLocation({
      type: 'gcj02',
      accuracy: 'best',
      success: function(location) {
        console.log('GPS位置更新:', location);
        
        const currentLocation = {
          longitude: location.longitude,
          latitude: location.latitude,
          accuracy: location.accuracy,
          timestamp: Date.now()
        };
        
        that.setData({
          currentGPSLocation: currentLocation
        });
        
        // 处理GPS位置更新
        that.processGPSUpdate(currentLocation);
      },
      fail: function(error) {
        console.error('GPS位置获取失败:', error);
        that.handleGPSError(error);
      }
    });
  },

  // ✅ 新增：处理GPS位置更新
  processGPSUpdate: function(currentLocation) {
    const routeSteps = this.data.routeSteps;
    const currentStepIndex = this.data.currentStepIndex;
    
    if (!routeSteps || routeSteps.length === 0) {
      console.warn('没有路线步骤数据');
      return;
    }
    
    // 检查是否到达当前步骤终点
    const currentStep = routeSteps[currentStepIndex];
    if (currentStep && currentStep.end_location) {
      const stepEndPoint = {
        longitude: parseFloat(currentStep.end_location.lng || currentStep.end_location.longitude),
        latitude: parseFloat(currentStep.end_location.lat || currentStep.end_location.latitude)
      };
      
      const distance = this.calculateDistance(currentLocation, stepEndPoint);
      console.log(`到当前步骤终点距离: ${distance}米`);
      
      // 如果距离小于20米，认为已到达，进入下一步
      if (distance < 20) {
        this.autoAdvanceToNextStep();
      }
      // 如果距离大于100米，检查是否偏离路线
      else if (distance > 100) {
        this.checkOffRoute(currentLocation);
      }
    }
    
    // 更新剩余距离显示
    this.updateRemainingDistance(currentLocation);
  },

  // ✅ 新增：自动前进到下一步
  autoAdvanceToNextStep: function() {
    const currentIndex = this.data.currentStepIndex;
    const totalSteps = this.data.routeSteps.length;
    
    if (currentIndex + 1 < totalSteps) {
      console.log(`GPS检测到到达步骤 ${currentIndex + 1}，自动前进到下一步`);
      
      this.updateCurrentStep(currentIndex + 1);
      
      // 播放到达提示音
      wx.showToast({
        title: '已到达转向点',
        icon: 'success',
        duration: 1500
      });
      
      // 语音提示
      if (this.data.voiceEnabled) {
        const nextStep = this.data.routeSteps[currentIndex + 1];
        if (nextStep) {
          this.playVoiceInstruction(`已到达转向点，${nextStep.instruction}`);
        }
      }
    } else {
      // 到达目的地
      this.arriveDestination();
    }
  },

  // ✅ 新增：检查偏离路线
  checkOffRoute: function(currentLocation) {
    const offRouteCount = this.data.offRouteAlerts || 0;
    
    // 连续3次偏离才提示重新规划
    if (offRouteCount >= 2) {
      wx.showModal({
        title: '偏离路线',
        content: '检测到您已偏离预定路线，是否重新规划？',
        success: (res) => {
          if (res.confirm) {
            this.replanRoute(currentLocation);
          } else {
            // 重置偏离计数
            this.setData({ offRouteAlerts: 0 });
          }
        }
      });
    } else {
      this.setData({
        offRouteAlerts: offRouteCount + 1
      });
      
      wx.showToast({
        title: '可能偏离路线',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // ✅ 新增：重新规划路线
  replanRoute: function(currentLocation) {
    wx.showLoading({
      title: '重新规划路线中...'
    });
    
    // 使用当前位置作为新起点，重新规划到原目的地
    const newStartLocation = {
      longitude: currentLocation.longitude,
      latitude: currentLocation.latitude,
      name: '当前位置'
    };
    
    // 这里应该调用路线规划API重新规划
    // 暂时使用模拟数据
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '路线已重新规划',
        icon: 'success',
        duration: 2000
      });
      
      // 重置偏离计数和步骤
      this.setData({
        offRouteAlerts: 0,
        currentStepIndex: 0,
        startLocation: newStartLocation
      });
      
      // 重新生成地图
      this.generateStaticRouteMap();
    }, 2000);
  },

  // ✅ 新增：更新剩余距离
  updateRemainingDistance: function(currentLocation) {
    const routeSteps = this.data.routeSteps;
    const currentStepIndex = this.data.currentStepIndex;
    
    if (!routeSteps || currentStepIndex >= routeSteps.length) return;
    
    let totalRemainingDistance = 0;
    
    // 计算到当前步骤终点的距离
    const currentStep = routeSteps[currentStepIndex];
    if (currentStep && currentStep.end_location) {
      const stepEndPoint = {
        longitude: parseFloat(currentStep.end_location.lng || currentStep.end_location.longitude),
        latitude: parseFloat(currentStep.end_location.lat || currentStep.end_location.latitude)
      };
      
      const distanceToStepEnd = this.calculateDistance(currentLocation, stepEndPoint);
      totalRemainingDistance += distanceToStepEnd;
    }
    
    // 加上后续所有步骤的距离
    for (let i = currentStepIndex + 1; i < routeSteps.length; i++) {
      const step = routeSteps[i];
      if (step.distance && typeof step.distance === 'number') {
        totalRemainingDistance += step.distance;
      }
    }
    
    // 更新显示
    this.setData({
      'currentStep.remainingDistance': this.formatDistance(totalRemainingDistance)
    });
  },

  // ✅ 新增：计算两点距离（使用util.js中的函数）
  calculateDistance: function(point1, point2) {
    const util = require('../../utils/util.js');
    return util.calculateDistance(point1, point2);
  },

  // ✅ 使用utils/util.js中的格式化函数
  formatDistance: function(distance) {
    const { formatDistance } = require('../../utils/util.js');
    return formatDistance(distance);
  },

  // ✅ 新增：处理GPS错误
  handleGPSError: function(error) {
    console.error('GPS错误:', error);
    
    wx.showModal({
      title: 'GPS信号异常',
      content: 'GPS信号较弱或丢失，建议：\n1. 移动到空旷地带\n2. 检查手机定位设置\n3. 切换到演示模式',
      cancelText: '继续GPS',
      confirmText: '演示模式',
      success: (res) => {
        if (res.confirm) {
          this.switchToDemoMode();
        }
      }
    });
  },

  // ✅ 新增：切换到演示模式
  switchToDemoMode: function() {
    // 清理GPS监听
    if (this.data.navigationInterval) {
      clearInterval(this.data.navigationInterval);
    }
    
    this.setData({
      isRealNavigation: false,
      isAutoDemo: false,
      navigationInterval: null
    });
    
    wx.showToast({
      title: '已切换到演示模式',
      icon: 'success',
      duration: 2000
    });
  },

  // ✅ 修改：初始化演示导航（原有功能保持）
  initDemoNavigation: function() {
    this.setData({
      isNavigating: true,
      isRealNavigation: false,
      isAutoDemo: false
    });
    
    wx.showToast({
      title: '演示模式已启动',
      icon: 'success',
      duration: 2000
    });
  },

  // ✅ 新增：停止自动演示
  stopAutoDemo: function() {
    this.setData({
      isAutoDemo: false
    });
    
    wx.showToast({
      title: '已切换到手动模式',
      icon: 'success',
      duration: 1500
    });
  },

  // ✅ 新增：显示路线详情
  showRouteSteps: function() {
    this.setData({
      stepsExpanded: !this.data.stepsExpanded
    });
  },
});
