<!--route-guide.wxml-->
<view class="container">
  <!-- 顶部导航信息 -->
  <view class="route-header">
    <view class="route-info">
      <view class="route-title">{{routeType}}导航</view>
      <view class="route-summary">
        <text class="distance">{{totalDistance}}</text>
        <text class="duration">预计{{totalDuration}}</text>
      </view>
    </view>
    <view class="header-actions">
      <button class="action-btn" bindtap="toggleVoice">
        <text class="action-icon">{{voiceEnabled ? '🔊' : '🔇'}}</text>
      </button>
      <button class="action-btn" bindtap="exitNavigation">
        <text class="action-icon">❌</text>
      </button>
    </view>
  </view>

  <!-- 当前指引信息 -->
  <view class="current-instruction">
    <view class="instruction-icon">
      <text class="direction-arrow">{{currentStep.arrow}}</text>
    </view>
    <view class="instruction-content">
      <view class="instruction-text">{{currentStep.instruction}}</view>
      <view class="instruction-detail">{{currentStep.road || currentStep.distance}}</view>
    </view>
    <view class="remaining-distance">
      <text class="distance-text">{{currentStep.remainingDistance}}</text>
    </view>
  </view>

  <!-- 下一步预览 -->
  <view class="next-instruction" wx:if="{{nextStep}}">
    <view class="next-icon">
      <text class="next-arrow">{{nextStep.arrow}}</text>
    </view>
    <view class="next-content">
      <text class="next-text">然后 {{nextStep.instruction}}</text>
    </view>
  </view>

  <!-- 高清地图区域 -->
  <view class="map-container">
    <!-- 高清静态地图 -->
    <view class="static-map-container" wx:if="{{useStaticMap}}">
      <!-- 地图状态提示 -->
      <view class="map-status-overlay" wx:if="{{mapStatus && !staticMapUrl}}">
        <view class="status-content">
          <view class="loading-spinner"></view>
          <text class="status-text">{{mapStatus}}</text>
        </view>
      </view>

      <image
        class="static-route-map"
        src="{{staticMapUrl}}"
        mode="aspectFit"
        bindload="onStaticMapLoad"
        binderror="onStaticMapError"
        show-menu-by-longpress="{{false}}"
        wx:if="{{staticMapUrl}}"
      />

      <!-- 地图加载失败提示 -->
      <view class="map-error-placeholder" wx:if="{{!staticMapUrl && mapStatus}}">
        <view class="error-icon">📍</view>
        <text class="error-text">{{mapStatus}}</text>
        <button class="retry-button" bindtap="generateStaticRouteMap" size="mini">重新生成</button>
      </view>

      <!-- 转向标识覆盖层 -->
      <view class="route-markers-overlay" wx:if="{{staticMapUrl}}">
        <view
          class="route-marker"
          wx:for="{{routeMarkers}}"
          wx:key="index"
          style="left: {{item.x}}px; top: {{item.y}}px;">
          <view class="marker-arrow">{{item.arrow}}</view>
          <view class="marker-step">{{item.step}}</view>
        </view>
      </view>

      <!-- 当前位置指示器 -->
      <view class="current-position-indicator" wx:if="{{staticMapUrl}}" style="left: {{currentPositionX}}px; top: {{currentPositionY}}px;">
        <view class="position-dot"></view>
        <view class="position-pulse"></view>
      </view>
    </view>

    <!-- 动态地图（备用） -->
    <map
      id="routeMap"
      class="route-map {{useStaticMap ? 'hidden' : ''}}"
      longitude="{{mapCenter.longitude}}"
      latitude="{{mapCenter.latitude}}"
      scale="{{mapScale}}"
      markers="{{markers}}"
      polyline="{{polylines}}"
      show-location="{{true}}"
      enable-rotate="{{false}}"
      enable-overlooking="{{false}}"
      bindregionchange="onMapRegionChange">

      <!-- 地图控件 -->
      <cover-view class="map-controls">
        <cover-view class="control-btn" bindtap="toggleMapMode">
          <cover-text class="control-icon">{{useStaticMap ? '🗺️' : '📷'}}</cover-text>
        </cover-view>
        <cover-view class="control-btn" bindtap="centerToLocation">
          <cover-text class="control-icon">📍</cover-text>
        </cover-view>
        <cover-view class="control-btn" bindtap="zoomIn">
          <cover-text class="control-icon">+</cover-text>
        </cover-view>
        <cover-view class="control-btn" bindtap="zoomOut">
          <cover-text class="control-icon">-</cover-text>
        </cover-view>
      </cover-view>
    </map>

    <!-- 地图模式切换按钮 -->
    <view class="map-mode-switch">
      <button class="mode-btn {{useStaticMap ? 'active' : ''}}" bindtap="switchToStaticMap">
        <text class="mode-text">高清路线图</text>
      </button>
      <button class="mode-btn {{!useStaticMap ? 'active' : ''}}" bindtap="switchToDynamicMap">
        <text class="mode-text">动态地图</text>
      </button>
    </view>
  </view>

  <!-- 路线步骤列表 -->
  <view class="steps-panel {{stepsExpanded ? 'expanded' : ''}}">
    <view class="panel-header" bindtap="toggleStepsPanel">
      <text class="panel-title">路线详情 ({{routeSteps.length}}步)</text>
      <text class="panel-toggle">{{stepsExpanded ? '▼' : '▲'}}</text>
    </view>
    
    <scroll-view class="steps-scroll" scroll-y="true" wx:if="{{stepsExpanded}}">
      <view 
        class="step-item {{index === currentStepIndex ? 'current' : ''}}" 
        wx:for="{{routeSteps}}" 
        wx:key="index"
        bindtap="jumpToStep"
        data-index="{{index}}">
        
        <view class="step-number">{{index + 1}}</view>
        
        <view class="step-arrow">
          <text class="arrow-icon">{{item.arrow}}</text>
        </view>
        
        <view class="step-content">
          <view class="step-instruction">{{item.instruction}}</view>
          <view class="step-details">
            <text class="step-road" wx:if="{{item.road}}">{{item.road}}</text>
            <text class="step-distance">{{item.distance}}</text>
            <text class="step-duration" wx:if="{{item.duration}}">{{item.duration}}</text>
          </view>
        </view>
        
        <view class="step-status">
          <text class="status-icon" wx:if="{{index < currentStepIndex}}">✅</text>
          <text class="status-icon" wx:elif="{{index === currentStepIndex}}">📍</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 底部控制栏 -->
  <view class="bottom-controls">
    <!-- GPS导航状态显示 -->
    <view class="gps-status" wx:if="{{isNavigating && isRealNavigation}}">
      <view class="status-header">
        <text class="status-title">🛰️ GPS导航模式</text>
        <button class="control-button small secondary" bindtap="switchToDemoMode">
          <text class="button-text">切换演示</text>
        </button>
      </view>
      
      <view class="gps-info">
        <view class="info-row" wx:if="{{currentGPSLocation}}">
          <text class="info-label">GPS精度:</text>
          <text class="info-value">{{currentGPSLocation.accuracy}}米</text>
        </view>
        <view class="info-row" wx:if="{{offRouteAlerts > 0}}">
          <text class="info-label">路线状态:</text>
          <text class="info-value warning">可能偏离 ({{offRouteAlerts}}/3)</text>
        </view>
        <view class="info-row" wx:if="{{lastGPSUpdate}}">
          <text class="info-label">更新时间:</text>
          <text class="info-value">刚刚</text>
        </view>
      </view>
    </view>

    <!-- 导航控制按钮 -->
    <view class="navigation-controls" wx:if="{{isNavigating && !isRealNavigation && !isAutoDemo}}">
      <button class="control-button nav-btn" bindtap="manualPrevStep" disabled="{{currentStepIndex <= 0}}">
        <text class="button-text">← 上一步</text>
      </button>
      
      <button class="control-button nav-btn primary" bindtap="manualNextStep" disabled="{{currentStepIndex >= routeSteps.length - 1}}">
        <text class="button-text">下一步 →</text>
      </button>
    </view>

    <!-- 自动演示状态显示 -->
    <view class="auto-demo-status" wx:if="{{isNavigating && isAutoDemo}}">
      <text class="status-text">🤖 自动演示进行中...</text>
      <button class="control-button secondary small" bindtap="stopAutoDemo">
        <text class="button-text">切换手动</text>
      </button>
    </view>

    <!-- 演示模式状态显示 -->
    <view class="demo-status" wx:if="{{isNavigating && !isRealNavigation && !isAutoDemo}}">
      <view class="status-header">
        <text class="status-title">🎮 演示模式</text>
        <button class="control-button small primary" bindtap="initRealGPSNavigation">
          <text class="button-text">启用GPS</text>
        </button>
      </view>
      <text class="demo-hint">使用上一步/下一步按钮手动体验导航</text>
    </view>

    <!-- 通用控制按钮 -->
    <view class="common-controls">
      <button class="control-button secondary" bindtap="toggleVoice">
        <text class="button-text">{{voiceEnabled ? '🔊 语音开' : '🔇 语音关'}}</text>
      </button>

      <button class="control-button secondary" bindtap="showVoiceSettings">
        <text class="button-text">⚙️ 语音设置</text>
      </button>
      
      <button class="control-button secondary" bindtap="showRouteSteps">
        <text class="button-text">📋 路线详情</text>
      </button>
      
      <button class="control-button danger" bindtap="exitNavigation">
        <text class="button-text">退出导航</text>
      </button>
    </view>
  </view>

  <!-- 路线选项弹窗 -->
  <view class="route-options-modal" wx:if="{{showOptions}}" bindtap="hideRouteOptions">
    <view class="options-content" catchtap="preventClose">
      <view class="options-header">
        <text class="options-title">路线选项</text>
        <button class="close-btn" bindtap="hideRouteOptions">×</button>
      </view>
      
      <view class="options-list">
        <view class="option-item" bindtap="switchToWalking">
          <text class="option-icon">🚶</text>
          <text class="option-text">切换到步行</text>
        </view>
        <view class="option-item" bindtap="switchToBicycling">
          <text class="option-icon">🚴</text>
          <text class="option-text">切换到骑行</text>
        </view>
        <view class="option-item" bindtap="avoidCongestion">
          <text class="option-icon">🚫</text>
          <text class="option-text">避开拥堵</text>
        </view>
        <view class="option-item" bindtap="shareRoute">
          <text class="option-icon">📤</text>
          <text class="option-text">分享路线</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 语音提示 -->
  <view class="voice-notification" wx:if="{{showVoiceNotification}}">
    <view class="voice-content">
      <text class="voice-icon">🔊</text>
      <text class="voice-text">{{voiceText}}</text>
    </view>
  </view>

  <!-- 语音设置面板 -->
  <view class="voice-settings-modal" wx:if="{{showVoiceSettings}}">
    <view class="modal-overlay" bindtap="hideVoiceSettings"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">语音设置</text>
        <button class="close-btn" bindtap="hideVoiceSettings">×</button>
      </view>

      <view class="settings-content">
        <!-- 语音开关 -->
        <view class="setting-item">
          <text class="setting-label">语音播报</text>
          <switch checked="{{voiceEnabled}}" bindchange="onVoiceEnabledChange"/>
        </view>

        <!-- 音量设置 -->
        <view class="setting-item">
          <text class="setting-label">音量 ({{voiceVolume}}%)</text>
          <slider value="{{voiceVolume}}" min="0" max="100" step="10"
                  bindchange="onVolumeChange" show-value/>
        </view>

        <!-- 语速设置 -->
        <view class="setting-item">
          <text class="setting-label">语速 ({{voiceRate}}x)</text>
          <slider value="{{voiceRate}}" min="0.5" max="2.0" step="0.1"
                  bindchange="onRateChange" show-value/>
        </view>

        <!-- 播报类型设置 -->
        <view class="setting-item">
          <text class="setting-label">播报内容</text>
          <view class="checkbox-group">
            <label class="checkbox-item">
              <checkbox checked="{{voiceSettings.navigation}}" value="navigation"/>
              <text>导航指令</text>
            </label>
            <label class="checkbox-item">
              <checkbox checked="{{voiceSettings.battery}}" value="battery"/>
              <text>电量提醒</text>
            </label>
            <label class="checkbox-item">
              <checkbox checked="{{voiceSettings.speed}}" value="speed"/>
              <text>限速提醒</text>
            </label>
            <label class="checkbox-item">
              <checkbox checked="{{voiceSettings.charging}}" value="charging"/>
              <text>充电站推荐</text>
            </label>
          </view>
        </view>

        <!-- 测试按钮 -->
        <view class="setting-item">
          <button class="test-voice-btn" bindtap="testVoice">测试语音</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 调试信息 -->
  <view class="debug-info" wx:if="{{showDebugInfo}}">
    <view class="debug-title">调试信息</view>
    <view class="debug-item">地图模式: {{useStaticMap ? '静态地图' : '动态地图'}}</view>
    <view class="debug-item">地图URL: {{staticMapUrl ? '已生成' : '未生成'}}</view>
    <view class="debug-item">路线步骤: {{routeSteps.length}}步</view>
    <view class="debug-item">转向标记: {{routeMarkers.length}}个</view>
    <view class="debug-item">起点: {{startLocation ? startLocation.name : '无'}}</view>
    <view class="debug-item">终点: {{endLocation ? endLocation.name : '无'}}</view>
    <button class="debug-btn" bindtap="forceShowDynamicMap">强制显示动态地图</button>
    <button class="debug-btn" bindtap="toggleDebugInfo">关闭调试</button>
  </view>

  <!-- 调试按钮 -->
  <view class="debug-toggle" wx:if="{{!showDebugInfo}}" bindtap="toggleDebugInfo">
    <text class="debug-icon">🐛</text>
  </view>
</view>
