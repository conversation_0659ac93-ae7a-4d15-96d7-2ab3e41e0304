/* route-guide.wxss */
page {
  height: 100vh;
  background-color: #f5f5f5;
}

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部导航信息 */
.route-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;  /* 减少上下内边距 */
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  color: white;
  flex-shrink: 0;  /* 防止被压缩 */
}

.route-info {
  flex: 1;
}

.route-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.route-summary {
  display: flex;
  gap: 20rpx;
}

.distance {
  font-size: 26rpx;
  font-weight: bold;
}

.duration {
  font-size: 24rpx;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 24rpx;
  color: white;
}

/* 当前指引信息 */
.current-instruction {
  display: flex;
  align-items: center;
  padding: 20rpx;  /* 减少内边距 */
  background: white;
  border-bottom: 1rpx solid #e9ecef;
  flex-shrink: 0;  /* 防止被压缩 */
}

.instruction-icon {
  width: 60rpx;   /* 减小图标尺寸 */
  height: 60rpx;
  background: #007aff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx;  /* 减少右边距 */
}

.direction-arrow {
  font-size: 32rpx;  /* 减小箭头字体 */
  color: white;
  font-weight: bold;
}

.instruction-content {
  flex: 1;
}

.instruction-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.instruction-detail {
  font-size: 26rpx;
  color: #666;
}

.remaining-distance {
  text-align: right;
}

.distance-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
}

/* 下一步预览 */
.next-instruction {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;  /* 减少内边距 */
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
  flex-shrink: 0;  /* 防止被压缩 */
}

.next-icon {
  width: 40rpx;   /* 减小尺寸 */
  height: 40rpx;
  background: #6c757d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;  /* 减少边距 */
}

.next-arrow {
  font-size: 24rpx;
  color: white;
}

.next-content {
  flex: 1;
}

.next-text {
  font-size: 24rpx;
  color: #666;
}

/* 地图区域 */
.map-container {
  flex: 1;
  position: relative;
  min-height: 400rpx;
  max-height: calc(100vh - 400rpx); /* 为顶部信息和底部控件预留空间 */
  overflow: hidden;
}

/* 高清静态地图 */
.static-map-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.static-route-map {
  width: 100%;
  height: 100%;
  min-height: 400rpx;
  max-height: 600rpx;  /* 限制最大高度，确保不超出屏幕 */
  object-fit: contain;
  background: white;
}

/* 转向标识覆盖层 */
.route-markers-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.route-marker {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.marker-arrow {
  width: 50rpx;   /* 稍微减小尺寸 */
  height: 50rpx;
  background: #007aff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;  /* 减小字体 */
  color: white;
  font-weight: bold;
  box-shadow: 0 3rpx 10rpx rgba(0, 122, 255, 0.4);
  border: 3rpx solid white;  /* 减小边框 */
}

.marker-step {
  margin-top: 6rpx;  /* 减少间距 */
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 3rpx 10rpx;  /* 减少内边距 */
  border-radius: 10rpx;
  font-size: 18rpx;  /* 减小字体 */
  white-space: nowrap;
}

/* 当前位置指示器 */
.current-position-indicator {
  position: absolute;
  transform: translate(-50%, -50%);
  z-index: 15;
}

.position-dot {
  width: 20rpx;   /* 稍微减小 */
  height: 20rpx;
  background: #4caf50;
  border-radius: 50%;
  border: 3rpx solid white;  /* 减小边框 */
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.3);
}

.position-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 35rpx;   /* 稍微减小 */
  height: 35rpx;
  background: rgba(76, 175, 80, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* 地图状态覆盖层 */
.map-status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 30;
  border-radius: 15rpx;
}

.status-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.status-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 地图错误占位符 */
.map-error-placeholder {
  width: 100%;
  height: 400rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 15rpx;
  gap: 20rpx;
}

.error-icon {
  font-size: 80rpx;
  opacity: 0.5;
}

.error-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  max-width: 80%;
}

.retry-button {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
}

/* 动态地图 */
.route-map {
  width: 100%;
  height: 100%;
  min-height: 400rpx;
  max-height: 600rpx;  /* 限制最大高度 */
}

.route-map.hidden {
  display: none;
}

/* 地图模式切换 */
.map-mode-switch {
  position: absolute;
  top: 15rpx;   /* 稍微上移 */
  left: 15rpx;
  display: flex;
  background: rgba(255, 255, 255, 0.95);  /* 增加透明度 */
  border-radius: 20rpx;  /* 减小圆角 */
  padding: 6rpx;  /* 减少内边距 */
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.15);
  z-index: 20;
}

.mode-btn {
  padding: 8rpx 16rpx;  /* 减少内边距 */
  border: none;
  border-radius: 15rpx;  /* 减小圆角 */
  font-size: 22rpx;  /* 减小字体 */
  background: transparent;
  color: #666;
  transition: all 0.3s ease;
}

.mode-btn.active {
  background: #007aff;
  color: white;
}

.mode-text {
  font-size: 22rpx;  /* 减小字体 */
}

.map-controls {
  position: absolute;
  right: 15rpx;  /* 稍微靠近边缘 */
  top: 40%;      /* 稍微上移 */
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 8rpx;     /* 减少间距 */
}

.control-btn {
  width: 50rpx;   /* 减小尺寸 */
  height: 50rpx;
  background: rgba(255, 255, 255, 0.95);  /* 增加透明度 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.2);
}

.control-icon {
  font-size: 20rpx;  /* 减小字体 */
  color: #333;
  font-weight: bold;
}

/* 路线步骤列表 */
.steps-panel {
  background: white;
  border-top: 1rpx solid #e9ecef;
  max-height: 200rpx;  /* 减少默认高度 */
  transition: max-height 0.3s ease;
  flex-shrink: 0;  /* 防止被压缩 */
}

.steps-panel.expanded {
  max-height: 300rpx;  /* 减少展开高度 */
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;  /* 减少上下内边距 */
  border-bottom: 1rpx solid #f0f0f0;
}

.panel-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.panel-toggle {
  font-size: 20rpx;
  color: #666;
}

.steps-scroll {
  max-height: 250rpx;  /* 减少滚动区域高度 */
}

.step-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.step-item.current {
  background: #e3f2fd;
  border-left: 4rpx solid #007aff;
}

.step-item:active {
  background: #f5f5f5;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: #e9ecef;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  color: #666;
  margin-right: 15rpx;
}

.step-item.current .step-number {
  background: #007aff;
  color: white;
}

.step-arrow {
  width: 50rpx;
  height: 50rpx;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx;
}

.arrow-icon {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.step-content {
  flex: 1;
}

.step-instruction {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.step-details {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.step-road,
.step-distance,
.step-duration {
  font-size: 24rpx;
  color: #666;
  background: #f8f9fa;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.step-status {
  width: 40rpx;
  text-align: center;
}

.status-icon {
  font-size: 20rpx;
}

/* 底部控制栏 */
.bottom-controls {
  position: sticky;
  bottom: 0;
  padding: 20rpx;
  background: white;
  border-top: 1rpx solid #e9ecef;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* ✅ 新增：导航控制按钮区域 */
.navigation-controls {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 10rpx;
  padding: 15rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 15rpx;
  border: 2rpx solid #007aff;
}

.nav-btn {
  flex: 1;
  max-width: 200rpx;
  height: 70rpx;
  border-radius: 35rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.nav-btn.primary {
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  color: white;
  border: none;
}

.nav-btn:not(.primary) {
  background: white;
  color: #007aff;
  border: 2rpx solid #007aff;
}

.nav-btn[disabled] {
  background: #f5f5f5 !important;
  color: #ccc !important;
  border-color: #e9ecef !important;
  box-shadow: none !important;
}

/* ✅ 新增：自动演示状态显示 */
.auto-demo-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15rpx 20rpx;
  background: linear-gradient(135deg, #28a745, #20c997);
  border-radius: 15rpx;
  margin-bottom: 10rpx;
}

.status-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.small {
  padding: 8rpx 16rpx !important;
  font-size: 24rpx !important;
  height: 50rpx !important;
  border-radius: 25rpx !important;
}

/* 其他控制按钮区域 */
.bottom-controls > button:not(.nav-btn) {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  margin: 0;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.control-button.primary {
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.control-button.secondary {
  background: #f8f9fa;
  color: #007aff;
  border: 1rpx solid #dee2e6;
}

.control-button:active {
  transform: scale(0.95);
}

.button-text {
  font-size: inherit;
  font-weight: inherit;
}

/* 路线选项弹窗 */
.route-options-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.options-content {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.options-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 50rpx;
  height: 50rpx;
  background: #f0f0f0;
  border: none;
  border-radius: 50%;
  font-size: 24rpx;
  color: #666;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
}

.option-item:active {
  background: #e9ecef;
}

.option-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

/* 语音提示 */
.voice-notification {
  position: fixed;
  top: 200rpx;
  left: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx;
  border-radius: 15rpx;
  z-index: 999;
  animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  20%, 80% { opacity: 1; }
}

.voice-content {
  display: flex;
  align-items: center;
}

.voice-icon {
  font-size: 24rpx;
  margin-right: 15rpx;
}

.voice-text {
  flex: 1;
  font-size: 26rpx;
}

/* 调试信息 */
.debug-info {
  position: fixed;
  bottom: 200rpx;
  left: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20rpx;
  border-radius: 15rpx;
  z-index: 1000;
  font-size: 24rpx;
}

.debug-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  color: #00ff00;
}

.debug-item {
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.debug-btn {
  margin-top: 15rpx;
  padding: 8rpx 16rpx;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.debug-toggle {
  position: fixed;
  bottom: 150rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.debug-icon {
  font-size: 24rpx;
  color: white;
}

/* ✅ 新增：GPS导航状态样式 */
.gps-status {
  background: linear-gradient(135deg, #e8f5e8, #f0f8ff);
  border: 2rpx solid #28a745;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.status-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #28a745;
}

.gps-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.info-value {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
}

.info-value.warning {
  color: #ff6b6b;
}

/* ✅ 新增：演示模式状态样式 */
.demo-status {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border: 2rpx solid #ffc107;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
}

.demo-hint {
  font-size: 24rpx;
  color: #856404;
  text-align: center;
  margin-top: 10rpx;
  font-style: italic;
}

/* ✅ 新增：通用控制按钮区域 */
.common-controls {
  display: flex;
  justify-content: space-between;
  gap: 15rpx;
  margin-top: 15rpx;
}

.common-controls .control-button {
  flex: 1;
  max-width: none;
  height: 65rpx;
  font-size: 24rpx;
}

/* ✅ 新增：小按钮样式 */
.control-button.small {
  height: 50rpx;
  padding: 0 20rpx;
  font-size: 22rpx;
  min-width: 120rpx;
}

/* ✅ 新增：危险按钮样式 */
.control-button.danger {
  background: linear-gradient(135deg, #dc3545, #ff6b6b);
  color: white;
  border: none;
}

.control-button.danger:active {
  background: linear-gradient(135deg, #c82333, #e55555);
}

/* ✅ 修改：自动演示状态样式 */
.auto-demo-status {
  background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
  border: 2rpx solid #2196f3;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.auto-demo-status .status-text {
  font-size: 26rpx;
  color: #1976d2;
  font-weight: bold;
}
