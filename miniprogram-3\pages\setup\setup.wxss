/* setup.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
  padding-bottom: 120rpx;
}

.header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}

/* 通用区块样式 */
.status-section,
.steps-section,
.params-section,
.example-section,
.result-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 状态检查样式 */
.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 28rpx;
  color: #666;
}

.status-value {
  font-size: 26rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.status-value.success {
  color: #4caf50;
  background: #e8f5e8;
}

.status-value.error {
  color: #f44336;
  background: #ffebee;
}

.status-value.warning {
  color: #ff9800;
  background: #fff3e0;
}

/* 步骤样式 */
.step-item {
  display: flex;
  margin-bottom: 30rpx;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 50rpx;
  height: 50rpx;
  background: #007aff;
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 50rpx;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.step-action {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.action-btn {
  padding: 12rpx 24rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.action-btn.secondary {
  background: white;
  color: #007aff;
  border: 2rpx solid #007aff;
}

/* 域名框样式 */
.domain-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 15rpx;
  margin: 15rpx 0;
}

.domain-text {
  flex: 1;
  font-family: monospace;
  font-size: 24rpx;
  color: #333;
}

.copy-btn {
  padding: 8rpx 16rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 15rpx;
  font-size: 22rpx;
  margin-left: 15rpx;
}

/* 参数说明样式 */
.param-group {
  margin-bottom: 25rpx;
}

.param-group:last-child {
  margin-bottom: 0;
}

.param-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.param-item {
  display: flex;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.param-item:last-child {
  border-bottom: none;
}

.param-name {
  min-width: 120rpx;
  font-family: monospace;
  font-size: 24rpx;
  color: #007aff;
  font-weight: bold;
}

.param-desc {
  flex: 1;
  font-size: 24rpx;
  color: #666;
}

/* 示例URL样式 */
.example-url {
  display: flex;
  align-items: flex-start;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
}

.url-text {
  flex: 1;
  font-family: monospace;
  font-size: 22rpx;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
}

/* 测试结果样式 */
.result-content {
  padding: 20rpx;
  border-radius: 8rpx;
}

.result-content.success {
  background: #e8f5e8;
  border: 1rpx solid #4caf50;
}

.result-content.error {
  background: #ffebee;
  border: 1rpx solid #f44336;
}

.result-text {
  font-size: 28rpx;
  font-weight: bold;
  color: inherit;
}

.result-detail {
  margin-top: 15rpx;
  padding-top: 15rpx;
  border-top: 1rpx solid rgba(0,0,0,0.1);
}

.detail-text {
  font-size: 24rpx;
  color: inherit;
  opacity: 0.8;
  line-height: 1.5;
}

/* 底部按钮 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  display: flex;
  gap: 20rpx;
}

.footer-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  border: 2rpx solid #007aff;
  background: white;
  color: #007aff;
}

.footer-btn.primary {
  background: #007aff;
  color: white;
}
