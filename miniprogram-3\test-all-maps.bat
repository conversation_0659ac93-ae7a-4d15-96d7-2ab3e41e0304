@echo off
echo ================================
echo 全面地图显示问题诊断脚本
echo ================================
echo.

echo 1. 检查基础配置...
if exist "utils\config.js" (
    echo ✓ 配置文件存在
    findstr /c:"AMAP_KEY" utils\config.js >nul
    if %errorlevel%==0 (
        echo ✓ 配置文件包含AMAP_KEY
    ) else (
        echo ✗ 配置文件缺少AMAP_KEY
    )
) else (
    echo ✗ 配置文件不存在
)

echo.
echo 2. 检查app.js地图初始化...
findstr /c:"initMapConfig" app.js >nul
if %errorlevel%==0 (
    echo ✓ app.js已添加地图初始化
) else (
    echo ✗ app.js缺少地图初始化
)

echo.
echo 3. 检查导航页面地图...
findstr /c:"createMapContext" pages\navigation\navigation.js >nul
if %errorlevel%==0 (
    echo ✓ 导航页面有地图上下文创建
) else (
    echo ✗ 导航页面缺少地图上下文创建
)

echo.
echo 4. 检查路线引导页面地图...
findstr /c:"createMapContext" pages\route-guide\route-guide.js >nul
if %errorlevel%==0 (
    echo ✓ 路线引导页面有地图上下文创建
) else (
    echo ✗ 路线引导页面缺少地图上下文创建
)

echo.
echo 5. 检查地图测试页面...
if exist "pages\map-test\map-test.js" (
    echo ✓ 地图测试页面存在
) else (
    echo ✗ 地图测试页面不存在
)

echo.
echo 6. 检查权限配置...
findstr /c:"getLocation" app.json >nul
if %errorlevel%==0 (
    echo ✓ app.json包含位置权限
) else (
    echo ✗ app.json缺少位置权限
)

echo.
echo ================================
echo 诊断完成！测试步骤：
echo ================================
echo 1. 在微信开发者工具中打开项目
echo 2. 首先测试"地图测试"页面
echo 3. 如果地图测试页面正常，再测试导航页面
echo 4. 最后测试路线引导页面
echo.
echo 预期结果：
echo - 地图测试页面：应该显示地图和当前位置
echo - 导航页面：应该显示地图和标记
echo - 路线引导页面：应该显示路线地图
echo.
echo 如果地图仍不显示，请检查：
echo - 微信开发者工具的位置权限设置
echo - 网络连接是否正常
echo - API密钥是否有效
echo.
pause
