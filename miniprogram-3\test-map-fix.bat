@echo off
echo ================================
echo 地图修复测试脚本
echo ================================
echo.

echo 1. 检查配置文件...
if exist "utils\config.js" (
    echo ✓ 配置文件存在
) else (
    echo ✗ 配置文件不存在
    pause
    exit /b 1
)

echo.
echo 2. 检查地图测试页面...
if exist "pages\map-test\map-test.js" (
    echo ✓ 地图测试页面已创建
) else (
    echo ✗ 地图测试页面不存在
    pause
    exit /b 1
)

echo.
echo 3. 检查导航页面修复...
findstr /c:"onReady" pages\navigation\navigation.js >nul
if %errorlevel%==0 (
    echo ✓ 导航页面已添加onReady方法
) else (
    echo ✗ 导航页面缺少onReady方法
)

echo.
echo 4. 检查app.js修复...
findstr /c:"initMapConfig" app.js >nul
if %errorlevel%==0 (
    echo ✓ app.js已添加地图初始化
) else (
    echo ✗ app.js缺少地图初始化
)

echo.
echo ================================
echo 修复完成！请按以下步骤测试：
echo ================================
echo 1. 在微信开发者工具中打开项目
echo 2. 点击主页面的"地图测试"按钮
echo 3. 检查地图是否正常显示
echo 4. 测试导航页面的地图功能
echo.
echo 如果仍有问题，请查看控制台错误信息
echo 详细说明请参考: docs\solution\map-fix-guide.md
echo.
pause
