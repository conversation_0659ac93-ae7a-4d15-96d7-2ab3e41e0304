// bluetooth-test.js - 蓝牙管理器功能测试
const bluetoothManager = require('../utils/bluetoothManager.js');

/**
 * 测试蓝牙管理器基础功能
 */
function testBluetoothManager() {
  console.log('开始测试蓝牙管理器...');
  
  // 测试1: 检查蓝牙权限
  console.log('测试1: 检查蓝牙权限');
  bluetoothManager.checkBluetoothPermission()
    .then(result => {
      console.log('✅ 蓝牙权限检查成功:', result);
      
      if (result.available) {
        // 测试2: 获取蓝牙实例
        console.log('测试2: 获取蓝牙实例');
        return bluetoothManager.getBluetoothInstance();
      } else {
        console.log('❌ 蓝牙不可用，跳过后续测试');
        return Promise.reject('蓝牙不可用');
      }
    })
    .then(instance => {
      console.log('✅ 蓝牙实例获取成功:', instance);
      
      // 测试3: 搜索设备
      console.log('测试3: 搜索蓝牙设备');
      return bluetoothManager.searchDevices({
        timeout: 5000,
        deviceName: null // 搜索所有设备
      });
    })
    .then(result => {
      console.log('✅ 设备搜索完成:', result);
      
      if (result.devices && result.devices.length > 0) {
        console.log(`找到 ${result.devices.length} 个设备:`);
        result.devices.forEach((device, index) => {
          console.log(`  ${index + 1}. ${device.name || '未知设备'} (${device.deviceId})`);
        });
        
        // 测试4: 连接第一个设备（仅作为示例，实际使用时需要选择正确的设备）
        const targetDevice = result.devices[0];
        console.log(`测试4: 尝试连接设备 ${targetDevice.name || targetDevice.deviceId}`);
        
        return bluetoothManager.connectDevice(targetDevice.deviceId);
      } else {
        console.log('⚠️ 未找到任何设备，跳过连接测试');
        return Promise.resolve({ success: false, message: '无设备可连接' });
      }
    })
    .then(result => {
      if (result.success) {
        console.log('✅ 设备连接成功:', result);
        
        // 测试5: 获取连接状态
        console.log('测试5: 获取连接状态');
        const state = bluetoothManager.getConnectionState();
        console.log('✅ 连接状态:', state);
        
        // 测试6: 断开连接
        console.log('测试6: 断开设备连接');
        return bluetoothManager.disconnect();
      } else {
        console.log('⚠️ 设备连接失败，跳过后续测试');
        return Promise.resolve({ success: true, message: '跳过断开测试' });
      }
    })
    .then(result => {
      console.log('✅ 设备断开成功:', result);
      
      // 测试7: 清理资源
      console.log('测试7: 清理蓝牙资源');
      bluetoothManager.destroyInstance();
      console.log('✅ 资源清理完成');
      
      console.log('🎉 蓝牙管理器测试完成！');
    })
    .catch(error => {
      console.error('❌ 测试过程中发生错误:', error);
      
      // 确保清理资源
      bluetoothManager.destroyInstance();
    });
}

/**
 * 测试safeCall方法
 */
function testSafeCall() {
  console.log('开始测试safeCall方法...');
  
  // 测试安全调用
  bluetoothManager.safeCall('checkBluetoothPermission')
    .then(result => {
      console.log('✅ safeCall测试成功:', result);
    })
    .catch(error => {
      console.log('❌ safeCall测试失败:', error);
    });
}

/**
 * 测试错误处理
 */
function testErrorHandling() {
  console.log('开始测试错误处理...');
  
  // 测试连接不存在的设备
  bluetoothManager.connectDevice('invalid-device-id')
    .then(result => {
      console.log('⚠️ 意外成功:', result);
    })
    .catch(error => {
      console.log('✅ 错误处理测试成功，正确捕获错误:', error);
    });
}

/**
 * 测试数据传输功能（需要实际设备连接）
 */
function testDataTransmission() {
  console.log('开始测试数据传输功能...');
  
  const state = bluetoothManager.getConnectionState();
  if (!state.connected) {
    console.log('⚠️ 设备未连接，跳过数据传输测试');
    return;
  }
  
  // 测试读取数据
  bluetoothManager.readData()
    .then(result => {
      console.log('✅ 数据读取成功:', result);
      
      // 测试发送模式切换指令
      return bluetoothManager.sendModeCommand(1); // 切换到成人模式
    })
    .then(result => {
      console.log('✅ 模式切换指令发送成功:', result);
    })
    .catch(error => {
      console.log('❌ 数据传输测试失败:', error);
    });
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始运行蓝牙管理器完整测试套件...');
  console.log('='.repeat(50));
  
  // 基础功能测试
  testBluetoothManager();
  
  // 延迟执行其他测试，避免冲突
  setTimeout(() => {
    console.log('\n' + '='.repeat(50));
    testSafeCall();
  }, 2000);
  
  setTimeout(() => {
    console.log('\n' + '='.repeat(50));
    testErrorHandling();
  }, 4000);
  
  setTimeout(() => {
    console.log('\n' + '='.repeat(50));
    testDataTransmission();
  }, 6000);
}

// 导出测试函数
module.exports = {
  testBluetoothManager,
  testSafeCall,
  testErrorHandling,
  testDataTransmission,
  runAllTests
};

// 如果直接运行此文件，执行所有测试
if (typeof module !== 'undefined' && require.main === module) {
  runAllTests();
}
