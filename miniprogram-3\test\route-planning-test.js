// 路径规划2.0测试脚本
const navigationConfig = require('../utils/navigationConfig.js');

// 测试数据
const testData = {
  // 北京测试点
  beijing: {
    start: { longitude: 116.397428, latitude: 39.90923, name: '天安门' },
    end: { longitude: 116.407526, latitude: 39.904030, name: '王府井' }
  },
  // 上海测试点
  shanghai: {
    start: { longitude: 121.473701, latitude: 31.230416, name: '人民广场' },
    end: { longitude: 121.499809, latitude: 31.239666, name: '外滩' }
  },
  // 广州测试点
  guangzhou: {
    start: { longitude: 113.264385, latitude: 23.129163, name: '天河城' },
    end: { longitude: 113.280637, latitude: 23.125178, name: '珠江新城' }
  }
};

// 测试路径规划2.0功能
function testRoutePlanning() {
  console.log('=== 路径规划2.0测试开始 ===');
  
  // 测试电动车路线规划
  testElectrobikeRoute();
  
  // 测试多路线方案
  testAlternativeRoutes();
  
  // 测试路线质量评估
  testRouteQuality();
  
  console.log('=== 路径规划2.0测试完成 ===');
}

// 测试电动车路线规划
function testElectrobikeRoute() {
  console.log('\n--- 测试电动车路线规划 ---');
  
  const testCase = testData.beijing;
  const options = {
    alternative_route: 1,
    cartype: 0
  };
  
  return navigationConfig.planRoute('electrobike', testCase.start, testCase.end, options)
    .then(result => {
      console.log('✅ 电动车路线规划成功');
      console.log('路线数量:', result.data.routes.length);
      
      if (result.data.routes.length > 0) {
        const route = result.data.routes[0];
        console.log('主路线信息:');
        console.log('- 距离:', (route.distance / 1000).toFixed(2) + 'km');
        console.log('- 时间:', Math.round(route.duration / 60) + '分钟');
        console.log('- 红绿灯:', route.traffic_lights + '个');
        console.log('- Polyline长度:', route.polyline ? route.polyline.length : 0);
        
        // 验证polyline数据
        if (route.polyline && route.polyline.length > 0) {
          const points = route.polyline.split(';');
          console.log('- 路径点数量:', points.length);
          console.log('✅ Polyline数据有效');
        } else {
          console.log('❌ Polyline数据无效');
        }
        
        // 验证导航指令
        if (route.navi && route.navi.length > 0) {
          console.log('- 导航指令数量:', route.navi.length);
          console.log('✅ 导航指令有效');
        } else {
          console.log('❌ 导航指令无效');
        }
      }
      
      return result;
    })
    .catch(error => {
      console.log('❌ 电动车路线规划失败:', error.message);
      return null;
    });
}

// 测试多路线方案
function testAlternativeRoutes() {
  console.log('\n--- 测试多路线方案 ---');
  
  const testCase = testData.shanghai;
  const options = {
    alternative_route: 1
  };
  
  return navigationConfig.planRoute('electrobike', testCase.start, testCase.end, options)
    .then(result => {
      if (result && result.data.routes.length > 1) {
        console.log('✅ 多路线方案获取成功');
        console.log('备选路线数量:', result.data.routes.length);
        
        result.data.routes.forEach((route, index) => {
          console.log(`路线${index + 1}:`);
          console.log('- 距离:', (route.distance / 1000).toFixed(2) + 'km');
          console.log('- 时间:', Math.round(route.duration / 60) + '分钟');
          console.log('- 费用:', route.cost ? route.cost.toFixed(2) + '元' : '无');
        });
      } else {
        console.log('⚠️ 只获取到单一路线');
      }
      
      return result;
    })
    .catch(error => {
      console.log('❌ 多路线方案测试失败:', error.message);
      return null;
    });
}

// 测试路线质量评估
function testRouteQuality() {
  console.log('\n--- 测试路线质量评估 ---');
  
  const testCase = testData.guangzhou;
  
  return navigationConfig.planRoute('electrobike', testCase.start, testCase.end, {})
    .then(result => {
      if (result && result.data.routes.length > 0) {
        const route = result.data.routes[0];
        
        console.log('✅ 路线质量评估:');
        console.log('- 基础信息: 距离' + (route.distance / 1000).toFixed(2) + 'km, 时间' + Math.round(route.duration / 60) + '分钟');
        
        if (route.route_quality) {
          console.log('- 拥堵程度:', route.route_quality.congestion_level);
          console.log('- 道路质量:', route.route_quality.road_quality);
          console.log('- 安全等级:', route.route_quality.safety_level);
        }
        
        if (route.restriction) {
          console.log('- 限行信息:', Object.keys(route.restriction).length > 0 ? '有限制' : '无限制');
        }
        
        if (route.cities && route.cities.length > 0) {
          console.log('- 途经城市:', route.cities.join(', '));
        }
      }
      
      return result;
    })
    .catch(error => {
      console.log('❌ 路线质量评估失败:', error.message);
      return null;
    });
}

// 测试地图polyline转换
function testPolylineConversion() {
  console.log('\n--- 测试Polyline转换 ---');
  
  // 模拟路线数据
  const mockRouteData = {
    routes: [{
      id: 0,
      distance: 5000,
      duration: 900,
      polyline: '116.397428,39.90923;116.398428,39.91023;116.399428,39.91123',
      traffic_lights: 3
    }]
  };
  
  try {
    const polylines = navigationConfig.convertToMapPolyline(mockRouteData, 'electrobike');
    
    if (polylines && polylines.length > 0) {
      console.log('✅ Polyline转换成功');
      console.log('- Polyline数量:', polylines.length);
      console.log('- 点数量:', polylines[0].points.length);
      console.log('- 颜色:', polylines[0].color);
      console.log('- 宽度:', polylines[0].width);
    } else {
      console.log('❌ Polyline转换失败');
    }
  } catch (error) {
    console.log('❌ Polyline转换异常:', error.message);
  }
}

// 测试URL构建
function testUrlBuilding() {
  console.log('\n--- 测试URL构建 ---');
  
  const options = {
    origin: '116.397428,39.90923',
    destination: '116.407526,39.904030',
    alternative_route: 1,
    cartype: 0
  };
  
  try {
    const url = navigationConfig.buildRouteUrl('electrobike', options);
    console.log('✅ URL构建成功');
    console.log('URL:', url);
    
    // 验证URL包含必要参数
    if (url.includes('v5/direction/electrobike') && 
        url.includes('show_fields') && 
        url.includes('alternative_route')) {
      console.log('✅ URL参数验证通过');
    } else {
      console.log('❌ URL参数验证失败');
    }
  } catch (error) {
    console.log('❌ URL构建失败:', error.message);
  }
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 开始运行路径规划2.0测试套件');
  
  // 测试URL构建
  testUrlBuilding();
  
  // 测试Polyline转换
  testPolylineConversion();
  
  // 测试路径规划（需要网络请求）
  if (typeof wx !== 'undefined') {
    testRoutePlanning();
  } else {
    console.log('⚠️ 非微信小程序环境，跳过网络请求测试');
  }
  
  console.log('✅ 测试套件运行完成');
}

// 导出测试函数
module.exports = {
  runAllTests,
  testElectrobikeRoute,
  testAlternativeRoutes,
  testRouteQuality,
  testPolylineConversion,
  testUrlBuilding
};

// 如果直接运行此文件，执行所有测试
if (typeof module !== 'undefined' && require.main === module) {
  runAllTests();
}
