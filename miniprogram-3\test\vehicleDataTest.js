// vehicleDataTest.js - 车辆数据解析测试
const vehicleDataParser = require('../utils/vehicleDataParser.js');

// 测试数据（基于您提供的JSON格式）
const testData = {
  "user": [
    {
      "id": 1,
      "role": "admin",
      "username": "admin",
      "password": "admin123",
      "_id": 0
    }
  ],
  "gps_data": [
    {
      "id": 1,
      "longitude": 52014141414,
      "latitude": 43.923,
      "_id": 0
    }
  ],
  "car_data": [
    {
      "id": 1,
      "speed": 123,
      "power": 87,
      "mod": 1,
      "_id": 0
    }
  ]
};

/**
 * 测试数据解析功能
 */
function testDataParsing() {
  console.log('=== 车辆数据解析测试 ===');
  
  // 测试1: JSON解析
  console.log('\n1. 测试JSON解析:');
  const jsonString = JSON.stringify(testData);
  const parseResult = vehicleDataParser.parseVehicleDataJSON(jsonString);
  console.log('解析结果:', parseResult);
  
  // 测试2: 数据验证
  console.log('\n2. 测试数据验证:');
  const validation = vehicleDataParser.validateData(testData);
  console.log('验证结果:', validation);
  
  // 测试3: 转换为标准格式
  console.log('\n3. 测试标准格式转换:');
  const standardData = vehicleDataParser.convertToStandardFormat(parseResult);
  console.log('标准格式数据:', standardData);
  
  // 测试4: 格式化显示
  console.log('\n4. 测试格式化显示:');
  const displayData = vehicleDataParser.formatForDisplay(standardData);
  console.log('显示格式数据:', displayData);
  
  // 测试5: 生成控制指令
  console.log('\n5. 测试控制指令生成:');
  
  // 切换模式指令
  const modeCommand = vehicleDataParser.generateControlCommand('switch_mode', { mode: 2 });
  console.log('模式切换指令:', modeCommand);
  
  // 速度限制指令
  const speedCommand = vehicleDataParser.generateControlCommand('set_speed_limit', { speedLimit: 20 });
  console.log('速度限制指令:', speedCommand);
  
  // 紧急停车指令
  const stopCommand = vehicleDataParser.generateControlCommand('emergency_stop');
  console.log('紧急停车指令:', stopCommand);
}

/**
 * 测试边界情况
 */
function testEdgeCases() {
  console.log('\n=== 边界情况测试 ===');
  
  // 测试1: 空数据
  console.log('\n1. 测试空数据:');
  const emptyData = { user: [], gps_data: [], car_data: [] };
  const emptyResult = vehicleDataParser.parseVehicleDataJSON(JSON.stringify(emptyData));
  console.log('空数据解析:', emptyResult);
  
  // 测试2: 无效JSON
  console.log('\n2. 测试无效JSON:');
  const invalidResult = vehicleDataParser.parseVehicleDataJSON('invalid json');
  console.log('无效JSON解析:', invalidResult);
  
  // 测试3: 超出范围的数据
  console.log('\n3. 测试超出范围数据:');
  const outOfRangeData = {
    user: [{ id: 1, username: "test", role: "user", _id: 0 }],
    gps_data: [{ id: 1, longitude: 116.397428, latitude: 39.90923, _id: 0 }],
    car_data: [{ id: 1, speed: 999, power: 150, mod: 5, _id: 0 }] // 超出范围
  };
  const rangeValidation = vehicleDataParser.validateData(outOfRangeData);
  console.log('范围验证结果:', rangeValidation);
  
  // 测试4: 缺少字段
  console.log('\n4. 测试缺少字段:');
  const incompleteData = {
    user: [{ id: 1, username: "test" }], // 缺少role
    gps_data: [{ id: 1 }], // 缺少坐标
    car_data: [{ id: 1, speed: 20 }] // 缺少power和mod
  };
  const incompleteResult = vehicleDataParser.parseVehicleDataJSON(JSON.stringify(incompleteData));
  const incompleteStandard = vehicleDataParser.convertToStandardFormat(incompleteResult);
  console.log('不完整数据处理:', incompleteStandard);
}

/**
 * 测试实时数据模拟
 */
function testRealTimeSimulation() {
  console.log('\n=== 实时数据模拟测试 ===');
  
  let simulationData = { ...testData };
  
  // 模拟数据变化
  for (let i = 0; i < 5; i++) {
    console.log(`\n--- 时刻 ${i + 1} ---`);
    
    // 模拟速度变化
    simulationData.car_data[0].speed = Math.floor(Math.random() * 30);
    
    // 模拟电量变化
    simulationData.car_data[0].power = Math.max(0, simulationData.car_data[0].power - Math.floor(Math.random() * 5));
    
    // 模拟位置变化
    simulationData.gps_data[0].latitude += (Math.random() - 0.5) * 0.001;
    simulationData.gps_data[0].longitude += (Math.random() - 0.5) * 0.001;
    
    // 解析和显示
    const parseResult = vehicleDataParser.parseVehicleDataJSON(JSON.stringify(simulationData));
    const standardData = vehicleDataParser.convertToStandardFormat(parseResult);
    const displayData = vehicleDataParser.formatForDisplay(standardData);
    
    console.log('当前状态:');
    console.log(`  速度: ${displayData.speed.current} (${displayData.speed.status})`);
    console.log(`  电量: ${displayData.battery.level} (${displayData.battery.status})`);
    console.log(`  模式: ${displayData.mode.name}`);
    console.log(`  位置: ${displayData.location.coordinates}`);
    
    // 检查警告
    if (standardData.system.warnings.length > 0) {
      console.log('  警告:');
      standardData.system.warnings.forEach(warning => {
        console.log(`    - ${warning.message}`);
      });
    }
  }
}

/**
 * 测试模式切换逻辑
 */
function testModeSwitch() {
  console.log('\n=== 模式切换测试 ===');
  
  const modes = [
    { code: 1, name: '青少年模式', maxSpeed: 15 },
    { code: 2, name: '成人模式', maxSpeed: 25 },
    { code: 3, name: '老人模式', maxSpeed: 12 }
  ];
  
  modes.forEach(mode => {
    console.log(`\n测试切换到 ${mode.name}:`);
    
    // 生成切换指令
    const command = vehicleDataParser.generateControlCommand('switch_mode', { mode: mode.code });
    console.log('控制指令:', command);
    
    // 解析指令验证
    const commandData = JSON.parse(command);
    console.log('指令验证:', {
      模式代码: commandData.car_data[0].mod,
      预期代码: mode.code,
      匹配: commandData.car_data[0].mod === mode.code
    });
    
    // 获取最大速度
    const maxSpeed = vehicleDataParser.getModeMaxSpeed(mode.code);
    console.log('最大速度:', maxSpeed, 'km/h');
  });
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('开始车辆数据解析器测试...\n');
  
  try {
    testDataParsing();
    testEdgeCases();
    testRealTimeSimulation();
    testModeSwitch();
    
    console.log('\n=== 测试完成 ===');
    console.log('所有测试已完成，请检查输出结果。');
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 导出测试函数
module.exports = {
  runAllTests,
  testDataParsing,
  testEdgeCases,
  testRealTimeSimulation,
  testModeSwitch
};

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests();
}
