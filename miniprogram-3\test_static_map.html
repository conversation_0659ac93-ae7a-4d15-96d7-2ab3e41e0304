<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德静态地图测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .map-container {
            text-align: center;
            margin: 20px 0;
        }
        .map-image {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        .url-display {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 8px;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
            margin: 20px 0;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">高德静态地图API测试</h1>
        
        <div class="info">
            <strong>API密钥:</strong> b27cdb743832c88b5747b96580df8062<br>
            <strong>测试位置:</strong> 北京天安门广场 (116.397428, 39.90923)
        </div>
        
        <div class="map-container">
            <h3>静态地图显示</h3>
            <img id="mapImage" class="map-image" alt="静态地图加载中..." />
            <div id="mapStatus">正在加载地图...</div>
        </div>
        
        <div class="url-display" id="mapUrl">
            生成地图URL中...
        </div>
        
        <div style="text-align: center;">
            <button onclick="loadMap()">重新加载地图</button>
            <button onclick="copyUrl()">复制URL</button>
            <button onclick="testDifferentLocation()">测试其他位置</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        const API_KEY = 'b27cdb743832c88b5747b96580df8062';
        let currentMapUrl = '';

        function buildStaticMapUrl(options = {}) {
            const baseUrl = 'https://restapi.amap.com/v3/staticmap';
            const params = {
                key: API_KEY,
                location: options.location || '116.397428,39.90923',
                zoom: options.zoom || 13,  // 适中缩放级别，scale=2时会自动变为14级
                size: options.size || '512*400',  // 基础尺寸，scale=2时会变为1024*800
                scale: options.scale || 2,  // 高清图
                markers: options.markers || 'mid,0xFC6054,A:116.397428,39.90923'  // 官方默认样式
            };
            
            const queryString = Object.entries(params)
                .filter(([key, value]) => value !== '' && value !== null && value !== undefined)
                .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
                .join('&');
                
            return `${baseUrl}?${queryString}`;
        }

        function loadMap() {
            const mapUrl = buildStaticMapUrl();
            currentMapUrl = mapUrl;
            
            document.getElementById('mapUrl').textContent = mapUrl;
            document.getElementById('mapStatus').textContent = '正在加载地图...';
            document.getElementById('result').innerHTML = '';
            
            const img = document.getElementById('mapImage');
            
            img.onload = function() {
                document.getElementById('mapStatus').textContent = '地图加载成功！';
                document.getElementById('result').innerHTML = '<div class="success">✅ 静态地图API工作正常！</div>';
            };
            
            img.onerror = function() {
                document.getElementById('mapStatus').textContent = '地图加载失败';
                document.getElementById('result').innerHTML = '<div class="error">❌ 静态地图API调用失败，可能是API密钥问题或网络问题</div>';
            };
            
            img.src = mapUrl;
        }

        function copyUrl() {
            if (currentMapUrl) {
                navigator.clipboard.writeText(currentMapUrl).then(function() {
                    alert('URL已复制到剪贴板');
                }).catch(function() {
                    prompt('请手动复制URL:', currentMapUrl);
                });
            }
        }

        function testDifferentLocation() {
            // 测试上海外滩
            const shanghaiUrl = buildStaticMapUrl({
                location: '121.499763,31.239580',
                markers: 'mid,0x00FF00,B:121.499763,31.239580',
                zoom: 16
            });
            
            currentMapUrl = shanghaiUrl;
            document.getElementById('mapUrl').textContent = shanghaiUrl;
            document.getElementById('mapStatus').textContent = '正在加载上海外滩地图...';
            
            const img = document.getElementById('mapImage');
            img.src = shanghaiUrl;
        }

        // 页面加载时自动加载地图
        window.onload = function() {
            loadMap();
        };
    </script>
</body>
</html>
