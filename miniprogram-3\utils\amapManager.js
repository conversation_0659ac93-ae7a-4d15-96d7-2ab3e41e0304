// amapManager.js - 高德地图SDK统一管理器
const AMapWX = require('../libs/amap-wx.130.js');
const CONFIG = require('./config.js');

// SDK实例管理
let amapInstance = null;
let initializationPromise = null;

/**
 * 获取高德地图SDK实例（单例模式）
 * @returns {Promise} 返回SDK实例的Promise
 */
function getAmapInstance() {
  // 如果已经有实例，直接返回
  if (amapInstance) {
    return Promise.resolve(amapInstance);
  }
  
  // 如果正在初始化中，返回初始化Promise
  if (initializationPromise) {
    return initializationPromise;
  }
  
  // 开始初始化
  initializationPromise = new Promise((resolve, reject) => {
    try {
      const instance = new AMapWX.AMapWX({
        key: CONFIG.AMAP_KEY
      });
      
      console.log('高德地图SDK初始化成功');
      console.log('使用密钥:', CONFIG.AMAP_KEY);
      
      amapInstance = instance;
      initializationPromise = null; // 清除初始化Promise
      resolve(instance);
      
    } catch (error) {
      console.error('高德地图SDK初始化失败:', error);
      initializationPromise = null; // 清除初始化Promise
      reject({
        success: false,
        message: 'SDK初始化失败',
        error: error
      });
    }
  });
  
  return initializationPromise;
}

/**
 * 测试SDK连接状态
 * @returns {Promise} 测试结果
 */
function testConnection() {
  return getAmapInstance()
    .then(instance => {
      return new Promise((resolve, reject) => {
        // 使用获取天气API测试连接（不需要定位权限）
        instance.getWeather({
          city: '北京',
          success: function(data) {
            console.log('SDK连接测试成功:', data);
            resolve({
              success: true,
              message: 'SDK连接正常',
              data: data
            });
          },
          fail: function(error) {
            console.error('SDK连接测试失败:', error);
            
            let errorMessage = 'SDK连接失败';
            if (error.errMsg && error.errMsg.includes('USERKEY_PLAT_NOMATCH')) {
              errorMessage = 'API密钥与微信小程序平台不匹配';
            } else if (error.errMsg && error.errMsg.includes('INVALID_USER_KEY')) {
              errorMessage = 'API密钥无效';
            } else if (error.errMsg && error.errMsg.includes('DAILY_QUERY_OVER_LIMIT')) {
              errorMessage = 'API调用次数超限';
            }
            
            reject({
              success: false,
              message: errorMessage,
              error: error
            });
          }
        });
      });
    });
}

/**
 * 检查定位权限
 * @returns {Promise} 权限检查结果
 */
function checkLocationPermission() {
  return new Promise((resolve, reject) => {
    wx.getSetting({
      success: function(res) {
        const locationAuth = res.authSetting['scope.userLocation'];
        
        if (locationAuth === true) {
          // 已授权
          resolve({
            success: true,
            authorized: true,
            message: '定位权限已授权'
          });
        } else if (locationAuth === false) {
          // 用户拒绝授权
          resolve({
            success: true,
            authorized: false,
            message: '用户已拒绝定位权限'
          });
        } else {
          // 未询问过权限
          resolve({
            success: true,
            authorized: null,
            message: '尚未询问定位权限'
          });
        }
      },
      fail: function(error) {
        reject({
          success: false,
          message: '检查权限失败',
          error: error
        });
      }
    });
  });
}

/**
 * 请求定位权限
 * @returns {Promise} 权限请求结果
 */
function requestLocationPermission() {
  return new Promise((resolve, reject) => {
    wx.authorize({
      scope: 'scope.userLocation',
      success: function() {
        resolve({
          success: true,
          message: '定位权限授权成功'
        });
      },
      fail: function(error) {
        if (error.errMsg && error.errMsg.includes('deny')) {
          resolve({
            success: false,
            message: '用户拒绝授权定位权限',
            needOpenSetting: true
          });
        } else {
          reject({
            success: false,
            message: '权限请求失败',
            error: error
          });
        }
      }
    });
  });
}

/**
 * 销毁SDK实例（页面销毁时调用）
 */
function destroyInstance() {
  if (amapInstance) {
    console.log('清理高德地图SDK实例');
    amapInstance = null;
  }
  
  if (initializationPromise) {
    initializationPromise = null;
  }
}

/**
 * 安全调用SDK方法（带权限检查和错误处理）
 * @param {string} method SDK方法名
 * @param {Object} options 调用参数
 * @returns {Promise} 调用结果
 */
function safeCall(method, options = {}) {
  // 需要定位权限的方法列表
  const locationMethods = ['getRegeo', 'getPoiAround'];
  
  // 检查是否需要定位权限
  const needLocation = locationMethods.includes(method);
  
  return Promise.resolve()
    .then(() => {
      // 如果需要定位权限，先检查权限
      if (needLocation) {
        return checkLocationPermission()
          .then(result => {
            if (!result.authorized) {
              return requestLocationPermission();
            }
            return result;
          });
      }
      return { success: true };
    })
    .then(() => {
      // 获取SDK实例
      return getAmapInstance();
    })
    .then(instance => {
      // 调用SDK方法
      return new Promise((resolve, reject) => {
        if (typeof instance[method] !== 'function') {
          reject({
            success: false,
            message: `SDK方法 ${method} 不存在`
          });
          return;
        }
        
        const callOptions = {
          ...options,
          success: function(data) {
            resolve({
              success: true,
              data: data
            });
          },
          fail: function(error) {
            reject({
              success: false,
              message: `${method} 调用失败`,
              error: error
            });
          }
        };
        
        instance[method](callOptions);
      });
    });
}

module.exports = {
  getAmapInstance,
  testConnection,
  checkLocationPermission,
  requestLocationPermission,
  destroyInstance,
  safeCall
}; 