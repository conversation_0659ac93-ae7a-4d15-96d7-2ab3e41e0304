// apiKeyDiagnostic.js - API密钥诊断工具
const CONFIG = require('./config.js');

/**
 * 高德地图API密钥诊断工具
 */
class ApiKeyDiagnostic {
  
  /**
   * 检查API密钥配置
   */
  static checkApiKeyConfig() {
    console.log('=== API密钥配置检查 ===');
    
    const results = {
      sdkKey: CONFIG.AMAP_KEY,
      webKey: CONFIG.AMAP_WEB_KEY,
      keysMatch: CONFIG.AMAP_KEY === CONFIG.AMAP_WEB_KEY,
      keyLength: CONFIG.AMAP_KEY ? CONFIG.AMAP_KEY.length : 0,
      keyFormat: this.validateKeyFormat(CONFIG.AMAP_KEY)
    };
    
    console.log('SDK密钥:', results.sdkKey);
    console.log('Web密钥:', results.webKey);
    console.log('密钥是否一致:', results.keysMatch);
    console.log('密钥长度:', results.keyLength);
    console.log('密钥格式正确:', results.keyFormat);
    
    return results;
  }
  
  /**
   * 验证密钥格式
   */
  static validateKeyFormat(key) {
    if (!key) return false;
    
    // 高德API密钥通常是32位十六进制字符串
    const keyPattern = /^[a-f0-9]{32}$/i;
    return keyPattern.test(key);
  }
  
  /**
   * 测试静态地图API
   */
  static testStaticMapApi() {
    return new Promise((resolve, reject) => {
      console.log('=== 静态地图API测试 ===');
      
      // 构建最简单的测试URL
      const testUrl = this.buildTestUrl();
      console.log('测试URL:', testUrl);
      
      // 使用wx.request测试API
      wx.request({
        url: testUrl,
        method: 'GET',
        timeout: 10000,
        success: (res) => {
          console.log('API测试成功:', res);
          
          const result = {
            success: true,
            statusCode: res.statusCode,
            contentType: res.header['content-type'] || res.header['Content-Type'],
            dataSize: res.data ? JSON.stringify(res.data).length : 0,
            message: '静态地图API调用成功'
          };
          
          // 检查返回内容类型
          if (result.contentType && result.contentType.includes('image')) {
            result.message = '返回图片数据，API正常';
          } else if (res.data && typeof res.data === 'object') {
            // 可能是错误响应
            result.error = res.data;
            result.message = 'API返回错误信息';
          }
          
          resolve(result);
        },
        fail: (error) => {
          console.error('API测试失败:', error);
          
          const result = {
            success: false,
            error: error,
            message: this.analyzeError(error)
          };
          
          reject(result);
        }
      });
    });
  }
  
  /**
   * 构建测试URL
   */
  static buildTestUrl() {
    const baseUrl = 'https://restapi.amap.com/v3/staticmap';
    const params = {
      key: CONFIG.AMAP_WEB_KEY,
      location: '116.397428,39.90923', // 北京天安门
      zoom: 10,
      size: '400*300',
      markers: 'mid,0xFF0000,A:116.397428,39.90923'
    };
    
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
      
    return `${baseUrl}?${queryString}`;
  }
  
  /**
   * 分析错误信息
   */
  static analyzeError(error) {
    if (!error) return '未知错误';
    
    // 网络错误
    if (error.errMsg) {
      if (error.errMsg.includes('timeout')) {
        return '请求超时，请检查网络连接';
      }
      if (error.errMsg.includes('fail')) {
        return '网络请求失败，请检查网络连接和域名配置';
      }
    }
    
    // HTTP状态码错误
    if (error.statusCode) {
      switch (error.statusCode) {
        case 400:
          return 'API参数错误，请检查密钥和参数格式';
        case 401:
          return 'API密钥无效或未授权';
        case 403:
          return 'API密钥权限不足或配额用完';
        case 404:
          return 'API接口不存在';
        case 500:
          return '高德服务器内部错误';
        default:
          return `HTTP错误：${error.statusCode}`;
      }
    }
    
    return error.errMsg || '未知错误';
  }
  
  /**
   * 检查域名配置
   */
  static checkDomainConfig() {
    console.log('=== 域名配置检查 ===');
    
    const requiredDomains = [
      'https://restapi.amap.com',
      'https://webapi.amap.com'
    ];
    
    console.log('需要配置的合法域名:');
    requiredDomains.forEach(domain => {
      console.log(`- ${domain}`);
    });
    
    return {
      requiredDomains: requiredDomains,
      instructions: [
        '1. 登录微信公众平台 https://mp.weixin.qq.com',
        '2. 进入开发 -> 开发管理 -> 开发设置',
        '3. 在"服务器域名"中添加上述域名到"request合法域名"',
        '4. 保存配置并等待生效（2-5分钟）'
      ]
    };
  }
  
  /**
   * 检查安全密钥配置
   */
  static checkSecurityKey() {
    console.log('=== 安全密钥检查 ===');
    
    const instructions = [
      '高德地图API安全密钥配置步骤：',
      '',
      '1. 登录高德开放平台控制台：',
      '   https://console.amap.com/dev/key/app',
      '',
      '2. 找到您的应用密钥：63431c51b3983f3712144e7f0c45a047',
      '',
      '3. 点击"设置"按钮，进入密钥配置页面',
      '',
      '4. 配置安全密钥：',
      '   - 服务平台：选择"Web服务"',
      '   - 安全密钥：可以留空或设置自定义密钥',
      '   - 白名单：可以设置IP白名单（可选）',
      '',
      '5. 如果设置了安全密钥，需要在请求中添加sig参数',
      '',
      '6. 检查配额使用情况：',
      '   - 确认每日调用量未超限',
      '   - 确认服务状态为"正常"',
      '',
      '7. 常见问题解决：',
      '   - 如果提示"INVALID_USER_KEY"：密钥无效',
      '   - 如果提示"INSUFFICIENT_PRIVILEGES"：权限不足',
      '   - 如果提示"DAILY_QUERY_OVER_LIMIT"：配额用完'
    ];
    
    console.log(instructions.join('\n'));
    
    return {
      currentKey: CONFIG.AMAP_WEB_KEY,
      consoleUrl: 'https://console.amap.com/dev/key/app',
      instructions: instructions
    };
  }
  
  /**
   * 运行完整诊断
   */
  static async runFullDiagnostic() {
    console.log('开始API密钥完整诊断...\n');
    
    try {
      // 1. 检查配置
      const configResult = this.checkApiKeyConfig();
      
      // 2. 检查域名
      const domainResult = this.checkDomainConfig();
      
      // 3. 检查安全密钥
      const securityResult = this.checkSecurityKey();
      
      // 4. 测试API
      console.log('\n开始API测试...');
      const apiResult = await this.testStaticMapApi();
      
      // 5. 生成诊断报告
      const report = {
        timestamp: new Date().toLocaleString(),
        config: configResult,
        domain: domainResult,
        security: securityResult,
        apiTest: apiResult,
        recommendations: this.generateRecommendations(configResult, apiResult)
      };
      
      console.log('\n=== 诊断完成 ===');
      console.log('诊断报告:', report);
      
      return report;
      
    } catch (error) {
      console.error('诊断过程中发生错误:', error);
      
      return {
        success: false,
        error: error,
        recommendations: [
          '1. 检查网络连接',
          '2. 确认微信开发者工具中已关闭域名校验',
          '3. 检查API密钥是否正确',
          '4. 联系技术支持'
        ]
      };
    }
  }
  
  /**
   * 生成修复建议
   */
  static generateRecommendations(configResult, apiResult) {
    const recommendations = [];
    
    // 密钥格式检查
    if (!configResult.keyFormat) {
      recommendations.push('❌ API密钥格式不正确，请检查密钥是否为32位十六进制字符串');
    }
    
    // API测试结果检查
    if (apiResult && !apiResult.success) {
      if (apiResult.error && apiResult.error.statusCode === 401) {
        recommendations.push('❌ API密钥无效，请检查高德控制台中的密钥配置');
      } else if (apiResult.error && apiResult.error.statusCode === 403) {
        recommendations.push('❌ API权限不足或配额用完，请检查高德控制台中的服务配置');
      } else {
        recommendations.push('❌ API调用失败，请检查网络连接和域名配置');
      }
    } else if (apiResult && apiResult.success) {
      recommendations.push('✅ API调用成功，密钥配置正确');
    }
    
    // 通用建议
    recommendations.push('💡 确保在微信公众平台中配置了正确的request合法域名');
    recommendations.push('💡 在开发阶段可以在微信开发者工具中关闭域名校验');
    
    return recommendations;
  }
}

module.exports = ApiKeyDiagnostic;
