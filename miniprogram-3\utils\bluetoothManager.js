// bluetoothManager.js - 蓝牙统一管理器
const errorHandler = require('./errorHandler.js');

// 蓝牙实例管理
let bluetoothInstance = null;
let connectedDevice = null;
let initializationPromise = null;
let connectionState = 'disconnected'; // disconnected | connecting | connected | error
let deviceServices = [];
let characteristicId = null;

// 蓝牙错误类型扩展
const BLUETOOTH_ERROR_TYPES = {
  BLUETOOTH_NOT_AVAILABLE: {
    title: '蓝牙不可用',
    message: '设备不支持蓝牙或蓝牙已关闭',
    suggestions: [
      '请检查设备蓝牙设置',
      '确认设备支持蓝牙功能',
      '尝试重新开启蓝牙'
    ]
  },
  
  DEVICE_NOT_FOUND: {
    title: '设备未找到',
    message: '未找到指定的车载设备',
    suggestions: [
      '请确认设备已开启并在范围内',
      '检查设备是否处于可发现状态',
      '尝试重新搜索设备'
    ]
  },
  
  CONNECTION_FAILED: {
    title: '连接失败',
    message: '无法连接到车载设备',
    suggestions: [
      '请重试连接操作',
      '检查设备状态和距离',
      '确认设备未被其他应用占用'
    ]
  },
  
  PERMISSION_DENIED: {
    title: '蓝牙权限被拒绝',
    message: '无法使用蓝牙功能，权限未授权',
    suggestions: [
      '请在设置中开启蓝牙权限',
      '重新启动小程序并允许蓝牙访问',
      '检查系统蓝牙设置'
    ]
  },
  
  DATA_TRANSMISSION_ERROR: {
    title: '数据传输失败',
    message: '蓝牙数据传输过程中发生错误',
    suggestions: [
      '请检查连接稳定性',
      '尝试重新连接设备',
      '确认设备距离在有效范围内'
    ]
  }
};

/**
 * 获取蓝牙适配器实例（单例模式）
 * @returns {Promise} 返回蓝牙适配器初始化结果的Promise
 */
function getBluetoothInstance() {
  // 如果已经有实例，直接返回
  if (bluetoothInstance) {
    return Promise.resolve(bluetoothInstance);
  }
  
  // 如果正在初始化中，返回初始化Promise
  if (initializationPromise) {
    return initializationPromise;
  }
  
  // 开始初始化
  initializationPromise = new Promise((resolve, reject) => {
    wx.openBluetoothAdapter({
      success: function(res) {
        console.log('蓝牙适配器初始化成功:', res);
        bluetoothInstance = res;
        connectionState = 'ready';
        initializationPromise = null; // 清除初始化Promise
        resolve(res);
      },
      fail: function(error) {
        console.error('蓝牙适配器初始化失败:', error);
        connectionState = 'error';
        initializationPromise = null; // 清除初始化Promise
        
        let errorType = 'BLUETOOTH_NOT_AVAILABLE';
        if (error.errMsg && error.errMsg.includes('not available')) {
          errorType = 'BLUETOOTH_NOT_AVAILABLE';
        } else if (error.errMsg && error.errMsg.includes('not authorized')) {
          errorType = 'PERMISSION_DENIED';
        }
        
        reject({
          success: false,
          message: '蓝牙适配器初始化失败',
          errorType: errorType,
          error: error
        });
      }
    });
  });
  
  return initializationPromise;
}

/**
 * 检查蓝牙权限和状态
 * @returns {Promise} 权限检查结果
 */
function checkBluetoothPermission() {
  return new Promise((resolve, reject) => {
    // 检查蓝牙适配器状态
    wx.getBluetoothAdapterState({
      success: function(res) {
        if (res.available) {
          resolve({
            success: true,
            available: true,
            discovering: res.discovering,
            message: '蓝牙适配器可用'
          });
        } else {
          resolve({
            success: false,
            available: false,
            message: '蓝牙适配器不可用'
          });
        }
      },
      fail: function(error) {
        // 如果获取状态失败，尝试初始化适配器
        getBluetoothInstance()
          .then(() => {
            resolve({
              success: true,
              available: true,
              message: '蓝牙适配器已初始化'
            });
          })
          .catch(initError => {
            reject({
              success: false,
              message: '蓝牙权限检查失败',
              error: initError
            });
          });
      }
    });
  });
}

/**
 * 搜索蓝牙设备
 * @param {Object} options 搜索选项
 * @returns {Promise} 搜索结果
 */
function searchDevices(options = {}) {
  const { 
    timeout = 10000, 
    deviceName = null,
    services = [] 
  } = options;
  
  return new Promise((resolve, reject) => {
    const foundDevices = [];
    let searchTimeout;
    
    // 设置搜索超时
    searchTimeout = setTimeout(() => {
      wx.stopBluetoothDevicesDiscovery();
      
      if (foundDevices.length === 0) {
        reject({
          success: false,
          message: '未找到任何设备',
          errorType: 'DEVICE_NOT_FOUND'
        });
      } else {
        resolve({
          success: true,
          devices: foundDevices,
          message: `找到 ${foundDevices.length} 个设备`
        });
      }
    }, timeout);
    
    // 监听设备发现事件
    wx.onBluetoothDeviceFound(function(res) {
      res.devices.forEach(device => {
        // 过滤设备
        if (deviceName && device.name !== deviceName) {
          return;
        }
        
        // 避免重复添加
        const exists = foundDevices.find(d => d.deviceId === device.deviceId);
        if (!exists) {
          foundDevices.push(device);
          console.log('发现蓝牙设备:', device);
          
          // 如果指定了设备名称且找到匹配设备，立即返回
          if (deviceName && device.name === deviceName) {
            clearTimeout(searchTimeout);
            wx.stopBluetoothDevicesDiscovery();
            resolve({
              success: true,
              devices: [device],
              message: '找到指定设备'
            });
          }
        }
      });
    });
    
    // 开始搜索
    wx.startBluetoothDevicesDiscovery({
      services: services,
      success: function() {
        console.log('开始搜索蓝牙设备');
      },
      fail: function(error) {
        clearTimeout(searchTimeout);
        reject({
          success: false,
          message: '启动设备搜索失败',
          error: error
        });
      }
    });
  });
}

/**
 * 连接蓝牙设备
 * @param {string} deviceId 设备ID
 * @returns {Promise} 连接结果
 */
function connectDevice(deviceId) {
  return new Promise((resolve, reject) => {
    if (!deviceId) {
      reject({
        success: false,
        message: '设备ID不能为空'
      });
      return;
    }
    
    connectionState = 'connecting';
    
    wx.createBLEConnection({
      deviceId: deviceId,
      success: function(res) {
        console.log('蓝牙设备连接成功:', res);
        connectedDevice = { deviceId: deviceId };
        connectionState = 'connected';
        
        // 获取设备服务
        getDeviceServices(deviceId)
          .then(services => {
            deviceServices = services;
            resolve({
              success: true,
              deviceId: deviceId,
              services: services,
              message: '设备连接成功'
            });
          })
          .catch(error => {
            reject({
              success: false,
              message: '获取设备服务失败',
              error: error
            });
          });
      },
      fail: function(error) {
        console.error('蓝牙设备连接失败:', error);
        connectionState = 'error';
        connectedDevice = null;
        
        reject({
          success: false,
          message: '设备连接失败',
          errorType: 'CONNECTION_FAILED',
          error: error
        });
      }
    });
  });
}

/**
 * 获取设备服务列表
 * @param {string} deviceId 设备ID
 * @returns {Promise} 服务列表
 */
function getDeviceServices(deviceId) {
  return new Promise((resolve, reject) => {
    wx.getBLEDeviceServices({
      deviceId: deviceId,
      success: function(res) {
        console.log('获取设备服务成功:', res.services);
        resolve(res.services);
      },
      fail: function(error) {
        console.error('获取设备服务失败:', error);
        reject(error);
      }
    });
  });
}

/**
 * 断开蓝牙连接
 * @returns {Promise} 断开结果
 */
function disconnect() {
  return new Promise((resolve, reject) => {
    if (!connectedDevice) {
      resolve({
        success: true,
        message: '设备未连接'
      });
      return;
    }
    
    wx.closeBLEConnection({
      deviceId: connectedDevice.deviceId,
      success: function(res) {
        console.log('蓝牙设备断开成功:', res);
        connectedDevice = null;
        connectionState = 'disconnected';
        deviceServices = [];
        characteristicId = null;
        
        resolve({
          success: true,
          message: '设备断开成功'
        });
      },
      fail: function(error) {
        console.error('蓝牙设备断开失败:', error);
        reject({
          success: false,
          message: '设备断开失败',
          error: error
        });
      }
    });
  });
}

/**
 * 销毁蓝牙实例（页面销毁时调用）
 */
function destroyInstance() {
  // 断开连接
  if (connectedDevice) {
    disconnect().catch(error => {
      console.error('断开连接失败:', error);
    });
  }
  
  // 关闭蓝牙适配器
  if (bluetoothInstance) {
    wx.closeBluetoothAdapter({
      success: function() {
        console.log('蓝牙适配器已关闭');
      },
      fail: function(error) {
        console.error('关闭蓝牙适配器失败:', error);
      }
    });
  }
  
  // 清理实例
  bluetoothInstance = null;
  connectedDevice = null;
  initializationPromise = null;
  connectionState = 'disconnected';
  deviceServices = [];
  characteristicId = null;
}

/**
 * 安全调用蓝牙方法（带权限检查和错误处理）
 * @param {string} method 方法名
 * @param {Object} options 调用参数
 * @returns {Promise} 调用结果
 */
function safeCall(method, options = {}) {
  // 需要连接的方法列表
  const connectionMethods = ['readData', 'writeData', 'getDeviceServices'];
  
  // 检查是否需要设备连接
  const needConnection = connectionMethods.includes(method);
  
  return Promise.resolve()
    .then(() => {
      // 检查蓝牙权限和状态
      return checkBluetoothPermission();
    })
    .then(() => {
      // 获取蓝牙适配器实例
      return getBluetoothInstance();
    })
    .then(() => {
      // 如果需要设备连接，检查连接状态
      if (needConnection && connectionState !== 'connected') {
        throw {
          success: false,
          message: '设备未连接',
          errorType: 'CONNECTION_FAILED'
        };
      }
      
      // 调用指定方法
      if (typeof this[method] !== 'function') {
        throw {
          success: false,
          message: `方法 ${method} 不存在`
        };
      }
      
      return this[method](options);
    })
    .catch(error => {
      // 使用统一错误处理
      return errorHandler.handleApiError(error, {
        showModal: options.showModal !== false,
        showToast: options.showToast === true,
        autoRetry: options.autoRetry === true,
        retryFunction: options.retryFunction
      });
    });
}

/**
 * 获取设备特征值列表
 * @param {string} deviceId 设备ID
 * @param {string} serviceId 服务ID
 * @returns {Promise} 特征值列表
 */
function getDeviceCharacteristics(deviceId, serviceId) {
  return new Promise((resolve, reject) => {
    wx.getBLEDeviceCharacteristics({
      deviceId: deviceId,
      serviceId: serviceId,
      success: function(res) {
        console.log('获取设备特征值成功:', res.characteristics);

        // 查找可读写的特征值
        const readWriteCharacteristic = res.characteristics.find(char =>
          char.properties.read && char.properties.write
        );

        if (readWriteCharacteristic) {
          characteristicId = readWriteCharacteristic.uuid;
        }

        resolve(res.characteristics);
      },
      fail: function(error) {
        console.error('获取设备特征值失败:', error);
        reject(error);
      }
    });
  });
}

/**
 * 读取蓝牙数据
 * @param {Object} options 读取选项
 * @returns {Promise} 读取结果
 */
function readData(options = {}) {
  return new Promise((resolve, reject) => {
    if (!connectedDevice || connectionState !== 'connected') {
      reject({
        success: false,
        message: '设备未连接',
        errorType: 'CONNECTION_FAILED'
      });
      return;
    }

    if (!characteristicId) {
      reject({
        success: false,
        message: '未找到可用的特征值',
        errorType: 'DATA_TRANSMISSION_ERROR'
      });
      return;
    }

    // 启用特征值变化通知
    wx.notifyBLECharacteristicValueChange({
      deviceId: connectedDevice.deviceId,
      serviceId: deviceServices[0].uuid, // 使用第一个服务
      characteristicId: characteristicId,
      state: true,
      success: function() {
        console.log('启用特征值通知成功');

        // 监听特征值变化
        wx.onBLECharacteristicValueChange(function(res) {
          console.log('接收到蓝牙数据:', res);
          resolve({
            success: true,
            data: res.value,
            deviceId: res.deviceId,
            serviceId: res.serviceId,
            characteristicId: res.characteristicId
          });
        });

        // 读取特征值
        wx.readBLECharacteristicValue({
          deviceId: connectedDevice.deviceId,
          serviceId: deviceServices[0].uuid,
          characteristicId: characteristicId,
          success: function() {
            console.log('读取特征值请求发送成功');
          },
          fail: function(error) {
            console.error('读取特征值失败:', error);
            reject({
              success: false,
              message: '读取数据失败',
              errorType: 'DATA_TRANSMISSION_ERROR',
              error: error
            });
          }
        });
      },
      fail: function(error) {
        console.error('启用特征值通知失败:', error);
        reject({
          success: false,
          message: '启用数据通知失败',
          errorType: 'DATA_TRANSMISSION_ERROR',
          error: error
        });
      }
    });
  });
}

/**
 * 写入蓝牙数据
 * @param {ArrayBuffer} data 要写入的数据
 * @param {Object} options 写入选项
 * @returns {Promise} 写入结果
 */
function writeData(data, options = {}) {
  return new Promise((resolve, reject) => {
    if (!connectedDevice || connectionState !== 'connected') {
      reject({
        success: false,
        message: '设备未连接',
        errorType: 'CONNECTION_FAILED'
      });
      return;
    }

    if (!characteristicId) {
      reject({
        success: false,
        message: '未找到可用的特征值',
        errorType: 'DATA_TRANSMISSION_ERROR'
      });
      return;
    }

    wx.writeBLECharacteristicValue({
      deviceId: connectedDevice.deviceId,
      serviceId: deviceServices[0].uuid, // 使用第一个服务
      characteristicId: characteristicId,
      value: data,
      success: function(res) {
        console.log('写入蓝牙数据成功:', res);
        resolve({
          success: true,
          message: '数据写入成功'
        });
      },
      fail: function(error) {
        console.error('写入蓝牙数据失败:', error);
        reject({
          success: false,
          message: '数据写入失败',
          errorType: 'DATA_TRANSMISSION_ERROR',
          error: error
        });
      }
    });
  });
}

/**
 * 发送驾驶模式切换指令
 * @param {number} modeId 驾驶模式ID (0:青少年, 1:成人, 2:老人)
 * @returns {Promise} 发送结果
 */
function sendModeCommand(modeId) {
  // 构建模式切换指令数据包
  const commandData = new ArrayBuffer(5);
  const dataView = new DataView(commandData);

  dataView.setUint8(0, 0xAA); // 起始位
  dataView.setUint8(1, 0x03); // 数据类型：驾驶模式
  dataView.setUint8(2, 0x01); // 数据长度
  dataView.setUint8(3, modeId); // 模式ID
  dataView.setUint8(4, 0x55); // 结束位

  return writeData(commandData, { commandType: 'modeSwitch' });
}

/**
 * 获取连接状态
 * @returns {Object} 连接状态信息
 */
function getConnectionState() {
  return {
    state: connectionState,
    connected: connectionState === 'connected',
    device: connectedDevice,
    services: deviceServices,
    characteristicId: characteristicId
  };
}

module.exports = {
  getBluetoothInstance,
  checkBluetoothPermission,
  searchDevices,
  connectDevice,
  getDeviceCharacteristics,
  readData,
  writeData,
  sendModeCommand,
  disconnect,
  destroyInstance,
  safeCall,
  getConnectionState,
  BLUETOOTH_ERROR_TYPES
};
