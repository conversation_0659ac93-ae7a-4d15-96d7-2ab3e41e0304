// config.js - 统一配置文件
module.exports = {
  // 高德地图API密钥配置
  // 微信小程序SDK专用密钥（用于AMapWX SDK）
  AMAP_KEY: 'fa8516b9618f8fc794edbb1c26893bc2',

  // Web服务API专用密钥（用于HTTP请求：静态地图、路线规划、POI搜索等）
  AMAP_WEB_KEY: '6dcf54a0b06c091e3764140865088543',
  
  // 地图默认配置
  MAP_CONFIG: {
    DEFAULT_LONGITUDE: 116.397428, // 默认经度（北京天安门）
    DEFAULT_LATITUDE: 39.90923,    // 默认纬度
    DEFAULT_SCALE: 16,             // 默认缩放级别
    MARKER_WIDTH: 30,              // 标记点宽度
    MARKER_HEIGHT: 30              // 标记点高度
  },
  
  // POI搜索配置
  POI_CONFIG: {
    SEARCH_RADIUS: 1000,           // 搜索半径（米）
    SEARCH_TYPES: '050000|060000|070000', // 餐饮、购物、生活服务
    SEARCH_KEYWORDS: '餐厅|商店|银行|医院|加油站'
  },
  
  // 应用配置
  APP_CONFIG: {
    NAME: '定位导航小程序',
    VERSION: '1.0.0'
  }
};
