// httpDataManager.js - HTTP API数据管理器
// 替代MQTT连接，通过HTTP API获取车辆数据

const errorHandler = require('./errorHandler.js');

// API配置
const API_CONFIG = {
  // 开发环境 - 需要在小程序开发工具中启用"不校验合法域名"
  baseUrl: 'http://localhost:3000/api',
  
  // 生产环境 - 需要配置HTTPS域名并在微信公众平台添加白名单
  // baseUrl: 'https://your-domain.com/api',
  
  timeout: 10000, // 请求超时时间
  retryCount: 3,  // 重试次数
  retryDelay: 1000 // 重试延迟
};

// 数据管理
let vehicleData = {
  user: '',
  gps: { longitude: 0, latitude: 0 },
  car: { speed: 0, power: 0, mode: 'adult', connected: false },
  _id: '',
  timestamp: ''
};

let isConnected = false;
let dataUpdateInterval = null;
let eventHandlers = new Map();

/**
 * HTTP请求封装
 * @param {string} url 请求URL
 * @param {Object} options 请求选项
 * @returns {Promise} 请求结果
 */
function httpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      url: `${API_CONFIG.baseUrl}${url}`,
      method: options.method || 'GET',
      timeout: API_CONFIG.timeout,
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      success: function(res) {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject({
            success: false,
            message: `HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`,
            statusCode: res.statusCode,
            data: res.data
          });
        }
      },
      fail: function(error) {
        console.error('HTTP请求失败:', error);
        reject({
          success: false,
          message: '网络请求失败',
          error: error
        });
      }
    };
    
    if (options.data) {
      requestOptions.data = options.data;
    }
    
    wx.request(requestOptions);
  });
}

/**
 * 带重试的HTTP请求
 * @param {string} url 请求URL
 * @param {Object} options 请求选项
 * @returns {Promise} 请求结果
 */
async function httpRequestWithRetry(url, options = {}) {
  let lastError = null;
  
  for (let i = 0; i < API_CONFIG.retryCount; i++) {
    try {
      const result = await httpRequest(url, options);
      return result;
    } catch (error) {
      lastError = error;
      console.warn(`HTTP请求失败，第${i + 1}次重试:`, error);
      
      if (i < API_CONFIG.retryCount - 1) {
        await new Promise(resolve => setTimeout(resolve, API_CONFIG.retryDelay));
      }
    }
  }
  
  throw lastError;
}

/**
 * 初始化HTTP数据管理器
 * @returns {Promise} 初始化结果
 */
function initialize() {
  return new Promise((resolve, reject) => {
    console.log('初始化HTTP数据管理器...');
    
    // 测试API连接
    testConnection()
      .then(() => {
        isConnected = true;
        console.log('HTTP数据管理器初始化成功');
        
        // 启动数据更新
        startDataUpdate();
        
        // 触发连接成功事件
        triggerEvent('connected', { message: 'HTTP API连接成功' });
        
        resolve({
          success: true,
          message: 'HTTP数据管理器初始化成功',
          data: vehicleData
        });
      })
      .catch(error => {
        console.error('HTTP数据管理器初始化失败:', error);
        isConnected = false;
        
        // 启动模拟数据（降级方案）
        startMockData();
        
        reject({
          success: false,
          message: 'HTTP API连接失败，已启用模拟数据',
          error: error
        });
      });
  });
}

/**
 * 测试API连接
 * @returns {Promise} 连接测试结果
 */
async function testConnection() {
  try {
    const result = await httpRequest('/vehicle/status');
    console.log('API连接测试成功:', result);
    return result;
  } catch (error) {
    console.error('API连接测试失败:', error);
    throw error;
  }
}

/**
 * 启动数据更新
 */
function startDataUpdate() {
  console.log('启动车辆数据更新...');
  
  // 清理之前的定时器
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
  }
  
  // 立即获取一次数据
  fetchVehicleData();
  
  // 每3秒更新一次数据
  dataUpdateInterval = setInterval(() => {
    fetchVehicleData();
  }, 3000);
}

/**
 * 获取车辆数据
 */
async function fetchVehicleData() {
  try {
    const result = await httpRequest('/vehicle/data');
    
    if (result.success && result.data) {
      // 更新本地数据
      vehicleData = {
        ...vehicleData,
        ...result.data
      };
      
      console.log('车辆数据更新:', vehicleData);
      
      // 触发数据更新事件
      triggerEvent('dataUpdate', vehicleData);
    }
  } catch (error) {
    console.warn('获取车辆数据失败:', error);
    
    // 连续失败时启动模拟数据
    if (!isConnected) {
      startMockData();
    }
  }
}

/**
 * 设置车辆模式
 * @param {string} mode 模式 (youth|adult|elderly)
 * @returns {Promise} 设置结果
 */
async function setVehicleMode(mode) {
  try {
    const result = await httpRequestWithRetry('/vehicle/mode', {
      method: 'POST',
      data: { mode }
    });
    
    console.log('模式设置成功:', result);
    
    // 立即更新数据
    setTimeout(() => fetchVehicleData(), 500);
    
    return result;
  } catch (error) {
    console.error('模式设置失败:', error);
    throw error;
  }
}

/**
 * 启动模拟数据（降级方案）
 */
function startMockData() {
  console.log('启动模拟车辆数据...');
  
  // 清理之前的定时器
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
  }
  
  // 初始化模拟数据
  vehicleData = {
    user: 'admin',
    password: 'admin123',
    gps: {
      longitude: 116.397428,
      latitude: 39.90923
    },
    car: {
      speed: 0,
      power: 85,
      mode: 'adult',
      connected: false // 标识为模拟数据
    },
    _id: Date.now().toString(),
    timestamp: new Date().toISOString()
  };
  
  // 每3秒生成模拟数据
  dataUpdateInterval = setInterval(() => {
    generateMockData();
  }, 3000);
  
  // 立即生成一次
  generateMockData();
}

/**
 * 生成模拟数据
 */
function generateMockData() {
  // 模拟电量变化
  if (Math.random() < 0.1) {
    vehicleData.car.power = Math.max(0, vehicleData.car.power - 1);
  }
  
  // 模拟速度变化
  const speedChange = (Math.random() - 0.5) * 8;
  vehicleData.car.speed = Math.max(0, Math.min(25, vehicleData.car.speed + speedChange));
  vehicleData.car.speed = Math.round(vehicleData.car.speed * 10) / 10;
  
  // 模拟GPS位置变化
  vehicleData.gps.longitude += (Math.random() - 0.5) * 0.001;
  vehicleData.gps.latitude += (Math.random() - 0.5) * 0.001;
  
  // 更新时间戳
  vehicleData.timestamp = new Date().toISOString();
  vehicleData._id = Date.now().toString();
  
  console.log('模拟数据更新:', vehicleData);
  
  // 触发数据更新事件
  triggerEvent('dataUpdate', vehicleData);
}

/**
 * 停止数据更新
 */
function stopDataUpdate() {
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
    dataUpdateInterval = null;
    console.log('数据更新已停止');
  }
}

/**
 * 获取当前车辆数据
 * @returns {Object} 车辆数据
 */
function getVehicleData() {
  return { ...vehicleData };
}

/**
 * 获取连接状态
 * @returns {Object} 连接状态
 */
function getConnectionState() {
  return {
    connected: isConnected,
    dataSource: isConnected ? 'http_api' : 'mock',
    lastUpdate: vehicleData.timestamp,
    config: {
      baseUrl: API_CONFIG.baseUrl,
      timeout: API_CONFIG.timeout
    }
  };
}

/**
 * 设置事件监听器
 * @param {string} event 事件名称
 * @param {function} handler 事件处理函数
 */
function addEventListener(event, handler) {
  if (!eventHandlers.has(event)) {
    eventHandlers.set(event, []);
  }
  eventHandlers.get(event).push(handler);
}

/**
 * 移除事件监听器
 * @param {string} event 事件名称
 * @param {function} handler 事件处理函数
 */
function removeEventListener(event, handler) {
  if (eventHandlers.has(event)) {
    const handlers = eventHandlers.get(event);
    const index = handlers.indexOf(handler);
    if (index > -1) {
      handlers.splice(index, 1);
    }
  }
}

/**
 * 触发事件
 * @param {string} event 事件名称
 * @param {*} data 事件数据
 */
function triggerEvent(event, data) {
  if (eventHandlers.has(event)) {
    eventHandlers.get(event).forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error(`事件处理器执行失败 [${event}]:`, error);
      }
    });
  }
}

/**
 * 销毁数据管理器
 */
function destroy() {
  stopDataUpdate();
  eventHandlers.clear();
  isConnected = false;
  console.log('HTTP数据管理器已销毁');
}

module.exports = {
  initialize,
  setVehicleMode,
  getVehicleData,
  getConnectionState,
  addEventListener,
  removeEventListener,
  destroy,
  
  // 兼容性接口（与原MQTT接口保持一致）
  connect: initialize,
  disconnect: destroy,
  subscribe: addEventListener,
  publish: setVehicleMode
};
