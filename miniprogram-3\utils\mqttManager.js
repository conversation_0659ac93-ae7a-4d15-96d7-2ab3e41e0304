// mqttManager.js - MQTT统一管理器
const errorHandler = require('./errorHandler.js');

// MQTT连接配置
const MQTT_CONFIG = {
  host: 's3.v100.vip',
  port: 33880,
  username: 'mdfk',
  password: '1HUJIFDAHSOUF',
  clientId: 'mdfk124xfasrf',
  // WebSocket路径（通常MQTT服务器的WebSocket路径）
  path: '/mqtt',
  // 连接选项
  keepalive: 60,
  clean: true,
  reconnectPeriod: 1000,
  connectTimeout: 30000
};

// MQTT实例管理
let mqttClient = null;
let connectionState = 'disconnected'; // disconnected | connecting | connected | error
let subscriptions = new Map(); // 订阅管理
let messageHandlers = new Map(); // 消息处理器

// MQTT错误类型
const MQTT_ERROR_TYPES = {
  CONNECTION_FAILED: {
    title: 'MQTT连接失败',
    message: '无法连接到车载数据服务器',
    suggestions: [
      '检查网络连接状态',
      '确认服务器地址和端口正确',
      '联系技术支持检查服务器状态'
    ]
  },
  
  SUBSCRIPTION_FAILED: {
    title: '订阅失败',
    message: '无法订阅车辆数据主题',
    suggestions: [
      '检查主题名称是否正确',
      '确认连接状态正常',
      '重试订阅操作'
    ]
  },
  
  PUBLISH_FAILED: {
    title: '消息发送失败',
    message: '无法发送指令到车载设备',
    suggestions: [
      '检查连接状态',
      '确认消息格式正确',
      '重试发送操作'
    ]
  }
};

/**
 * 连接MQTT服务器
 * @returns {Promise} 连接结果
 */
function connect() {
  return new Promise((resolve, reject) => {
    if (connectionState === 'connected') {
      resolve({
        success: true,
        message: '已连接到MQTT服务器'
      });
      return;
    }
    
    if (connectionState === 'connecting') {
      reject({
        success: false,
        message: '正在连接中，请稍候'
      });
      return;
    }
    
    connectionState = 'connecting';
    
    // 构建WebSocket URL
    const wsUrl = `wss://${MQTT_CONFIG.host}:${MQTT_CONFIG.port}${MQTT_CONFIG.path}`;
    
    // 微信小程序使用WebSocket连接MQTT
    mqttClient = wx.connectSocket({
      url: wsUrl,
      protocols: ['mqtt'],
      success: function() {
        console.log('WebSocket连接建立成功');
      },
      fail: function(error) {
        console.error('WebSocket连接失败:', error);
        connectionState = 'error';
        reject({
          success: false,
          message: 'WebSocket连接失败',
          errorType: 'CONNECTION_FAILED',
          error: error
        });
      }
    });
    
    // 监听连接打开
    mqttClient.onOpen(function() {
      console.log('MQTT WebSocket连接已打开');
      
      // 发送MQTT CONNECT包
      const connectPacket = buildConnectPacket();
      mqttClient.send({
        data: connectPacket,
        success: function() {
          console.log('MQTT CONNECT包发送成功');
        },
        fail: function(error) {
          console.error('MQTT CONNECT包发送失败:', error);
          connectionState = 'error';
          reject({
            success: false,
            message: 'MQTT握手失败',
            error: error
          });
        }
      });
    });
    
    // 监听消息接收
    mqttClient.onMessage(function(res) {
      handleMqttMessage(res.data);
    });
    
    // 监听连接关闭
    mqttClient.onClose(function() {
      console.log('MQTT连接已关闭');
      connectionState = 'disconnected';
      mqttClient = null;
    });
    
    // 监听连接错误
    mqttClient.onError(function(error) {
      console.error('MQTT连接错误:', error);
      connectionState = 'error';
      reject({
        success: false,
        message: 'MQTT连接错误',
        errorType: 'CONNECTION_FAILED',
        error: error
      });
    });
    
    // 设置连接超时
    setTimeout(() => {
      if (connectionState === 'connecting') {
        connectionState = 'error';
        reject({
          success: false,
          message: '连接超时',
          errorType: 'CONNECTION_FAILED'
        });
      }
    }, MQTT_CONFIG.connectTimeout);
  });
}

/**
 * 构建MQTT CONNECT数据包
 * @returns {ArrayBuffer} CONNECT数据包
 */
function buildConnectPacket() {
  // 简化的MQTT CONNECT包构建
  // 实际项目中建议使用专业的MQTT.js库
  const clientIdBytes = new TextEncoder().encode(MQTT_CONFIG.clientId);
  const usernameBytes = new TextEncoder().encode(MQTT_CONFIG.username);
  const passwordBytes = new TextEncoder().encode(MQTT_CONFIG.password);
  
  // 计算总长度
  const totalLength = 14 + clientIdBytes.length + usernameBytes.length + passwordBytes.length;
  const buffer = new ArrayBuffer(totalLength);
  const view = new DataView(buffer);
  
  let offset = 0;
  
  // 固定头部
  view.setUint8(offset++, 0x10); // CONNECT消息类型
  view.setUint8(offset++, totalLength - 2); // 剩余长度
  
  // 协议名称 "MQTT"
  view.setUint16(offset, 4); offset += 2;
  view.setUint8(offset++, 0x4D); // M
  view.setUint8(offset++, 0x51); // Q
  view.setUint8(offset++, 0x54); // T
  view.setUint8(offset++, 0x54); // T
  
  // 协议版本
  view.setUint8(offset++, 0x04); // MQTT 3.1.1
  
  // 连接标志
  view.setUint8(offset++, 0xC2); // 用户名+密码+clean session
  
  // Keep Alive
  view.setUint16(offset, MQTT_CONFIG.keepalive); offset += 2;
  
  // Client ID
  view.setUint16(offset, clientIdBytes.length); offset += 2;
  for (let i = 0; i < clientIdBytes.length; i++) {
    view.setUint8(offset++, clientIdBytes[i]);
  }
  
  // Username
  view.setUint16(offset, usernameBytes.length); offset += 2;
  for (let i = 0; i < usernameBytes.length; i++) {
    view.setUint8(offset++, usernameBytes[i]);
  }
  
  // Password
  view.setUint16(offset, passwordBytes.length); offset += 2;
  for (let i = 0; i < passwordBytes.length; i++) {
    view.setUint8(offset++, passwordBytes[i]);
  }
  
  return buffer;
}

/**
 * 处理MQTT消息
 * @param {ArrayBuffer} data 消息数据
 */
function handleMqttMessage(data) {
  try {
    // 解析MQTT消息包
    const view = new DataView(data);
    const messageType = (view.getUint8(0) >> 4) & 0x0F;
    
    switch (messageType) {
      case 2: // CONNACK
        handleConnAck(view);
        break;
      case 3: // PUBLISH
        handlePublish(view, data);
        break;
      case 9: // SUBACK
        handleSubAck(view);
        break;
      default:
        console.log('收到未处理的MQTT消息类型:', messageType);
    }
  } catch (error) {
    console.error('MQTT消息处理错误:', error);
  }
}

/**
 * 处理CONNACK消息
 * @param {DataView} view 数据视图
 */
function handleConnAck(view) {
  const returnCode = view.getUint8(3);
  
  if (returnCode === 0) {
    console.log('MQTT连接成功');
    connectionState = 'connected';
    
    // 触发连接成功事件
    if (typeof onConnected === 'function') {
      onConnected();
    }
  } else {
    console.error('MQTT连接失败，返回码:', returnCode);
    connectionState = 'error';
  }
}

/**
 * 处理PUBLISH消息
 * @param {DataView} view 数据视图
 * @param {ArrayBuffer} data 完整数据
 */
function handlePublish(view, data) {
  try {
    let offset = 2; // 跳过固定头部
    
    // 读取主题长度
    const topicLength = view.getUint16(offset);
    offset += 2;
    
    // 读取主题
    const topicBytes = new Uint8Array(data, offset, topicLength);
    const topic = new TextDecoder().decode(topicBytes);
    offset += topicLength;
    
    // 读取消息内容
    const messageBytes = new Uint8Array(data, offset);
    const message = new TextDecoder().decode(messageBytes);
    
    console.log('收到MQTT消息:', { topic, message });
    
    // 调用消息处理器
    const handler = messageHandlers.get(topic);
    if (handler) {
      handler(topic, message);
    }
    
    // 广播消息事件
    if (typeof onMessage === 'function') {
      onMessage(topic, message);
    }
  } catch (error) {
    console.error('PUBLISH消息解析错误:', error);
  }
}

/**
 * 处理SUBACK消息
 * @param {DataView} view 数据视图
 */
function handleSubAck(view) {
  console.log('订阅确认收到');
  // 可以在这里处理订阅确认逻辑
}

/**
 * 订阅主题
 * @param {string} topic 主题名称
 * @param {function} handler 消息处理函数
 * @returns {Promise} 订阅结果
 */
function subscribe(topic, handler) {
  return new Promise((resolve, reject) => {
    if (connectionState !== 'connected') {
      reject({
        success: false,
        message: '未连接到MQTT服务器',
        errorType: 'SUBSCRIPTION_FAILED'
      });
      return;
    }
    
    // 构建SUBSCRIBE包
    const subscribePacket = buildSubscribePacket(topic);
    
    mqttClient.send({
      data: subscribePacket,
      success: function() {
        console.log('订阅请求发送成功:', topic);
        subscriptions.set(topic, true);
        
        if (handler) {
          messageHandlers.set(topic, handler);
        }
        
        resolve({
          success: true,
          message: `成功订阅主题: ${topic}`
        });
      },
      fail: function(error) {
        console.error('订阅请求发送失败:', error);
        reject({
          success: false,
          message: '订阅请求发送失败',
          errorType: 'SUBSCRIPTION_FAILED',
          error: error
        });
      }
    });
  });
}

/**
 * 构建SUBSCRIBE数据包
 * @param {string} topic 主题名称
 * @returns {ArrayBuffer} SUBSCRIBE数据包
 */
function buildSubscribePacket(topic) {
  const topicBytes = new TextEncoder().encode(topic);
  const totalLength = 5 + topicBytes.length;
  const buffer = new ArrayBuffer(totalLength);
  const view = new DataView(buffer);
  
  let offset = 0;
  
  // 固定头部
  view.setUint8(offset++, 0x82); // SUBSCRIBE消息类型
  view.setUint8(offset++, totalLength - 2); // 剩余长度
  
  // 消息ID
  view.setUint16(offset, 1); offset += 2;
  
  // 主题长度
  view.setUint16(offset, topicBytes.length); offset += 2;
  
  // 主题内容
  for (let i = 0; i < topicBytes.length; i++) {
    view.setUint8(offset++, topicBytes[i]);
  }
  
  // QoS级别
  view.setUint8(offset++, 0x00); // QoS 0
  
  return buffer;
}

/**
 * 发布消息
 * @param {string} topic 主题名称
 * @param {string} message 消息内容
 * @returns {Promise} 发布结果
 */
function publish(topic, message) {
  return new Promise((resolve, reject) => {
    if (connectionState !== 'connected') {
      reject({
        success: false,
        message: '未连接到MQTT服务器',
        errorType: 'PUBLISH_FAILED'
      });
      return;
    }
    
    // 构建PUBLISH包
    const publishPacket = buildPublishPacket(topic, message);
    
    mqttClient.send({
      data: publishPacket,
      success: function() {
        console.log('消息发布成功:', { topic, message });
        resolve({
          success: true,
          message: '消息发布成功'
        });
      },
      fail: function(error) {
        console.error('消息发布失败:', error);
        reject({
          success: false,
          message: '消息发布失败',
          errorType: 'PUBLISH_FAILED',
          error: error
        });
      }
    });
  });
}

/**
 * 构建PUBLISH数据包
 * @param {string} topic 主题名称
 * @param {string} message 消息内容
 * @returns {ArrayBuffer} PUBLISH数据包
 */
function buildPublishPacket(topic, message) {
  const topicBytes = new TextEncoder().encode(topic);
  const messageBytes = new TextEncoder().encode(message);
  const totalLength = 3 + topicBytes.length + messageBytes.length;
  const buffer = new ArrayBuffer(totalLength);
  const view = new DataView(buffer);
  
  let offset = 0;
  
  // 固定头部
  view.setUint8(offset++, 0x30); // PUBLISH消息类型
  view.setUint8(offset++, totalLength - 2); // 剩余长度
  
  // 主题长度
  view.setUint16(offset, topicBytes.length); offset += 2;
  
  // 主题内容
  for (let i = 0; i < topicBytes.length; i++) {
    view.setUint8(offset++, topicBytes[i]);
  }
  
  // 消息内容
  for (let i = 0; i < messageBytes.length; i++) {
    view.setUint8(offset++, messageBytes[i]);
  }
  
  return buffer;
}

/**
 * 断开连接
 * @returns {Promise} 断开结果
 */
function disconnect() {
  return new Promise((resolve) => {
    if (mqttClient) {
      mqttClient.close({
        success: function() {
          console.log('MQTT连接已断开');
          connectionState = 'disconnected';
          mqttClient = null;
          subscriptions.clear();
          messageHandlers.clear();
          
          resolve({
            success: true,
            message: 'MQTT连接已断开'
          });
        },
        fail: function(error) {
          console.error('断开连接失败:', error);
          // 即使断开失败，也清理状态
          connectionState = 'disconnected';
          mqttClient = null;
          subscriptions.clear();
          messageHandlers.clear();
          
          resolve({
            success: true,
            message: 'MQTT连接已强制断开'
          });
        }
      });
    } else {
      resolve({
        success: true,
        message: 'MQTT未连接'
      });
    }
  });
}

/**
 * 获取连接状态
 * @returns {Object} 连接状态信息
 */
function getConnectionState() {
  return {
    state: connectionState,
    connected: connectionState === 'connected',
    subscriptions: Array.from(subscriptions.keys()),
    config: {
      host: MQTT_CONFIG.host,
      port: MQTT_CONFIG.port,
      clientId: MQTT_CONFIG.clientId
    }
  };
}

// 事件回调函数（可由外部设置）
let onConnected = null;
let onMessage = null;
let onDisconnected = null;

/**
 * 设置事件监听器
 * @param {Object} listeners 事件监听器对象
 */
function setEventListeners(listeners) {
  onConnected = listeners.onConnected || null;
  onMessage = listeners.onMessage || null;
  onDisconnected = listeners.onDisconnected || null;
}

module.exports = {
  connect,
  disconnect,
  subscribe,
  publish,
  getConnectionState,
  setEventListeners,
  MQTT_ERROR_TYPES
};
