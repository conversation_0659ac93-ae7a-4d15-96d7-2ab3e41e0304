// navigationConfig.js - 微信小程序导航功能配置
const CONFIG = require('./config.js');

// 导航配置常量 - 升级到路径规划2.0
const NAVIGATION_CONFIG = {
  // 路线规划API基础URL - 使用2.0版本
  ROUTE_PLANNING_BASE_URL: 'https://restapi.amap.com/v5/direction',

  // 路线规划类型
  ROUTE_TYPES: {
    WALKING: 'walking',      // 步行
    DRIVING: 'driving',      // 驾车
    TRANSIT: 'transit/integrated', // 公交
    BICYCLING: 'bicycling',  // 骑行
    ELECTROBIKE: 'electrobike' // 电动车（专用于电动车导航）
  },
  
  // 驾车策略
  DRIVING_STRATEGIES: {
    FASTEST: 10,        // 躲避拥堵，路程较短，尽量缩短时间（推荐）
    AVOID_CONGESTION: 12, // 躲避拥堵
    NO_HIGHWAY: 13,     // 不走高速
    AVOID_TOLL: 14,     // 避免收费
    HIGHWAY_FIRST: 19,  // 高速优先
    COMPREHENSIVE: 20   // 躲避拥堵&高速优先
  },
  
  // 公交策略
  TRANSIT_STRATEGIES: {
    FASTEST: 0,         // 最快捷模式
    CHEAPEST: 1,        // 最经济模式
    LEAST_TRANSFER: 2,  // 最少换乘模式
    LEAST_WALK: 3,      // 最少步行模式
    NO_SUBWAY: 5        // 不乘地铁模式
  },
  
  // 默认配置
  DEFAULTS: {
    STRATEGY: 10,       // 默认使用最优策略
    EXTENSIONS: 'all',  // 返回详细信息
    OUTPUT: 'JSON'      // 返回JSON格式
  }
};

// 构建路线规划URL - 路径规划2.0优化
function buildRouteUrl(routeType, options = {}) {
  let baseUrl = NAVIGATION_CONFIG.ROUTE_PLANNING_BASE_URL;

  // 根据路线类型选择合适的API版本
  if (routeType === NAVIGATION_CONFIG.ROUTE_TYPES.BICYCLING) {
    baseUrl = 'https://restapi.amap.com/v4/direction';
  } else if (routeType === NAVIGATION_CONFIG.ROUTE_TYPES.ELECTROBIKE) {
    // 电动车专用v5版本API
    baseUrl = 'https://restapi.amap.com/v5/direction';
  } else if (routeType === NAVIGATION_CONFIG.ROUTE_TYPES.WALKING ||
             routeType === NAVIGATION_CONFIG.ROUTE_TYPES.DRIVING) {
    // 步行和驾车也使用v5版本获得更好的路径规划
    baseUrl = 'https://restapi.amap.com/v5/direction';
  }

  const url = `${baseUrl}/${routeType}`;

  // 构建基础参数
  const params = {
    key: CONFIG.AMAP_WEB_KEY, // 使用Web服务API密钥
    origin: options.origin,
    destination: options.destination,
    output: NAVIGATION_CONFIG.DEFAULTS.OUTPUT
  };

  // 添加特定类型的参数
  switch (routeType) {
    case NAVIGATION_CONFIG.ROUTE_TYPES.DRIVING:
      if (options.strategy) params.strategy = options.strategy;
      if (options.waypoints) params.waypoints = options.waypoints;
      if (options.avoidpolygons) params.avoidpolygons = options.avoidpolygons;
      params.extensions = NAVIGATION_CONFIG.DEFAULTS.EXTENSIONS;
      // v5版本增强参数
      params.show_fields = 'cost,navi,polyline,cities';
      if (options.alternative_route !== undefined) params.alternative_route = options.alternative_route;
      break;

    case NAVIGATION_CONFIG.ROUTE_TYPES.TRANSIT:
      if (options.city) params.city = options.city;
      if (options.cityd) params.cityd = options.cityd;
      if (options.strategy) params.strategy = options.strategy;
      if (options.nightflag !== undefined) params.nightflag = options.nightflag;
      params.extensions = NAVIGATION_CONFIG.DEFAULTS.EXTENSIONS;
      break;

    case NAVIGATION_CONFIG.ROUTE_TYPES.WALKING:
      // 步行路径规划v5版本增强参数
      params.show_fields = 'polyline,navi';
      if (options.alternative_route !== undefined) params.alternative_route = options.alternative_route;
      break;

    case NAVIGATION_CONFIG.ROUTE_TYPES.BICYCLING:
      // 骑行路径规划使用v4 API，参数较简单
      break;

    case NAVIGATION_CONFIG.ROUTE_TYPES.ELECTROBIKE:
      // 电动车路径规划使用v5 API - 路径规划2.0核心功能
      params.show_fields = 'cost,navi,polyline,cities,tmcs,restriction'; // 获取完整信息
      // 多路线方案
      if (options.alternative_route !== undefined) {
        params.alternative_route = options.alternative_route;
      } else {
        params.alternative_route = 1; // 默认返回备选路线
      }
      // 电动车特有参数
      if (options.plate_number) params.plate_number = options.plate_number; // 车牌号
      if (options.cartype !== undefined) params.cartype = options.cartype; // 车辆类型
      break;
  }
  
  // 构建查询字符串
  const queryString = Object.entries(params)
    .filter(([key, value]) => value !== undefined && value !== null && value !== '')
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');
    
  return `${url}?${queryString}`;
}

// 路线规划请求
function planRoute(routeType, startPoint, endPoint, options = {}) {
  return new Promise((resolve, reject) => {
    // 构建起点和终点字符串
    const origin = `${startPoint.longitude},${startPoint.latitude}`;
    const destination = `${endPoint.longitude},${endPoint.latitude}`;
    
    // 构建请求URL
    const requestOptions = {
      origin: origin,
      destination: destination,
      ...options
    };
    
    const url = buildRouteUrl(routeType, requestOptions);
    
    console.log('路线规划请求URL:', url);
    
    // 发送请求
    wx.request({
      url: url,
      method: 'GET',
      success: function(res) {
        console.log('路线规划响应:', res);
        
        if (res.statusCode === 200) {
          const data = res.data;
          
          // 检查API返回状态
          if (data.status === '1' || data.errcode === 0) {
            // 解析路线数据
            const routeData = parseRouteData(routeType, data);
            resolve({
              success: true,
              data: routeData,
              rawData: data
            });
          } else {
            reject({
              success: false,
              message: data.info || data.errmsg || '路线规划失败',
              code: data.infocode || data.errcode
            });
          }
        } else {
          reject({
            success: false,
            message: `HTTP错误: ${res.statusCode}`,
            code: res.statusCode
          });
        }
      },
      fail: function(error) {
        reject({
          success: false,
          message: '网络请求失败',
          error: error
        });
      }
    });
  });
}

// 解析路线数据
function parseRouteData(routeType, apiData) {
  const result = {
    type: routeType,
    routes: [],
    summary: {}
  };
  
  try {
    switch (routeType) {
      case NAVIGATION_CONFIG.ROUTE_TYPES.DRIVING:
        result.routes = parseDrivingRoutes(apiData.route);
        break;
        
      case NAVIGATION_CONFIG.ROUTE_TYPES.WALKING:
        result.routes = parseWalkingRoutes(apiData.route);
        break;
        
      case NAVIGATION_CONFIG.ROUTE_TYPES.TRANSIT:
        result.routes = parseTransitRoutes(apiData.route);
        break;
        
      case NAVIGATION_CONFIG.ROUTE_TYPES.BICYCLING:
        result.routes = parseBicyclingRoutes(apiData.data);
        break;

      case NAVIGATION_CONFIG.ROUTE_TYPES.ELECTROBIKE:
        result.routes = parseElectrobikeRoutes(apiData.data);
        break;
    }
    
    // 生成路线摘要
    if (result.routes.length > 0) {
      const firstRoute = result.routes[0];
      result.summary = {
        distance: firstRoute.distance,
        duration: firstRoute.duration,
        routeCount: result.routes.length
      };
    }
    
  } catch (error) {
    console.error('解析路线数据失败:', error);
    throw new Error('路线数据解析失败');
  }
  
  return result;
}

// 解析驾车路线
function parseDrivingRoutes(routeData) {
  if (!routeData || !routeData.paths) return [];
  
  return routeData.paths.map((path, index) => ({
    id: index,
    distance: parseInt(path.distance),
    duration: parseInt(path.duration),
    tolls: parseFloat(path.tolls || 0),
    trafficLights: parseInt(path.traffic_lights || 0),
    strategy: path.strategy,
    polyline: path.steps ? path.steps.map(step => step.polyline).join(';') : '',
    steps: path.steps || []
  }));
}

// 解析步行路线
function parseWalkingRoutes(routeData) {
  if (!routeData || !routeData.paths) return [];
  
  return routeData.paths.map((path, index) => ({
    id: index,
    distance: parseInt(path.distance),
    duration: parseInt(path.duration),
    polyline: path.steps ? path.steps.map(step => step.polyline).join(';') : '',
    steps: path.steps || []
  }));
}

// 解析公交路线
function parseTransitRoutes(routeData) {
  if (!routeData || !routeData.transits) return [];
  
  return routeData.transits.map((transit, index) => ({
    id: index,
    distance: parseInt(transit.walking_distance || 0),
    duration: parseInt(transit.duration),
    cost: parseFloat(transit.cost || 0),
    walkingDistance: parseInt(transit.walking_distance || 0),
    segments: transit.segments || []
  }));
}

// 解析骑行路线
function parseBicyclingRoutes(routeData) {
  if (!routeData || !routeData.paths) return [];

  return routeData.paths.map((path, index) => ({
    id: index,
    distance: parseInt(path.distance),
    duration: parseInt(path.duration),
    polyline: path.steps ? path.steps.map(step => step.polyline).join(';') : '',
    steps: path.steps || []
  }));
}

// 解析电动车路线 - 路径规划2.0增强版
function parseElectrobikeRoutes(routeData) {
  if (!routeData || !routeData.paths) return [];

  return routeData.paths.map((path, index) => {
    // 处理polyline数据 - 路径规划2.0提供更精确的坐标
    let polylineString = '';
    if (path.polyline) {
      polylineString = path.polyline;
    } else if (path.steps && path.steps.length > 0) {
      polylineString = path.steps.map(step => step.polyline).filter(p => p).join(';');
    }

    // 处理导航指令 - 路径规划2.0提供更详细的导航信息
    const navigationInstructions = [];
    if (path.steps && path.steps.length > 0) {
      path.steps.forEach((step, stepIndex) => {
        if (step.instruction) {
          navigationInstructions.push({
            index: stepIndex,
            instruction: step.instruction,
            orientation: step.orientation || '',
            distance: parseInt(step.distance || 0),
            duration: parseInt(step.duration || 0),
            road_name: step.road_name || '',
            action: step.action || ''
          });
        }
      });
    }

    return {
      id: index,
      distance: parseInt(path.distance || 0),
      duration: parseInt(path.duration || 0),
      cost: parseFloat(path.cost || 0), // 电动车路径费用
      polyline: polylineString,
      steps: path.steps || [],
      navi: navigationInstructions, // 详细导航指令
      restriction: path.restriction || {}, // 限行信息
      tolls: parseFloat(path.tolls || 0), // 过路费
      traffic_lights: parseInt(path.traffic_lights || 0), // 红绿灯数量
      // 路径规划2.0新增字段
      cities: path.cities || [], // 途经城市
      tmcs: path.tmcs || [], // 交通状况信息
      taxi_cost: parseFloat(path.taxi_cost || 0), // 打车费用参考
      // 路线质量评估
      route_quality: {
        congestion_level: path.congestion_level || 0, // 拥堵程度
        road_quality: path.road_quality || 0, // 道路质量
        safety_level: path.safety_level || 0 // 安全等级
      }
    };
  });
}

// 将路线数据转换为微信小程序地图polyline格式 - 路径规划2.0优化
function convertToMapPolyline(routeData, routeType = 'walking') {
  const polylines = [];

  // 根据路线类型设置不同的颜色和样式 - 增强视觉效果
  const routeStyles = {
    walking: {
      color: '#0091ff',
      width: 6,
      borderColor: '#ffffff',
      borderWidth: 2,
      dottedLine: false
    },
    bicycling: {
      color: '#52c41a',
      width: 6,
      borderColor: '#ffffff',
      borderWidth: 2,
      dottedLine: false
    },
    electrobike: {
      color: '#ff4757', // 更鲜明的红色
      width: 10, // 更宽的线条
      borderColor: '#ffffff',
      borderWidth: 3,
      dottedLine: false
    },
    driving: {
      color: '#3742fa',
      width: 8,
      borderColor: '#ffffff',
      borderWidth: 2,
      dottedLine: false
    }
  };

  const style = routeStyles[routeType] || routeStyles.walking;

  routeData.routes.forEach((route, index) => {
    if (route.polyline && route.polyline.trim()) {
      try {
        // 解析polyline坐标 - 支持路径规划2.0的高精度坐标
        const points = [];
        const polylineSegments = route.polyline.split(';');

        polylineSegments.forEach(segment => {
          if (segment && segment.includes(',')) {
            const [lng, lat] = segment.split(',');
            const longitude = parseFloat(lng);
            const latitude = parseFloat(lat);

            // 验证坐标有效性
            if (!isNaN(longitude) && !isNaN(latitude) &&
                longitude >= -180 && longitude <= 180 &&
                latitude >= -90 && latitude <= 90) {
              points.push({
                longitude: longitude,
                latitude: latitude
              });
            }
          }
        });

        if (points.length >= 2) {
          // 主路线
          const polylineConfig = {
            points: points,
            color: style.color,
            width: style.width,
            arrowLine: true,
            borderColor: style.borderColor,
            borderWidth: style.borderWidth,
            dottedLine: style.dottedLine
          };

          // 为电动车路线添加特殊效果
          if (routeType === 'electrobike') {
            polylineConfig.arrowIconPath = '/images/arrow-icon.png'; // 自定义箭头图标
            polylineConfig.colorList = [
              { color: '#ff4757', colorIndex: 0 },
              { color: '#ff6b6b', colorIndex: Math.floor(points.length * 0.3) },
              { color: '#ff4757', colorIndex: Math.floor(points.length * 0.6) },
              { color: '#ff3838', colorIndex: points.length - 1 }
            ]; // 渐变色效果
          }

          polylines.push(polylineConfig);

          // 如果是备选路线，添加虚线样式
          if (index > 0) {
            polylines.push({
              points: points,
              color: style.color,
              width: style.width - 2,
              arrowLine: false,
              borderColor: style.borderColor,
              borderWidth: 1,
              dottedLine: true // 备选路线使用虚线
            });
          }
        }
      } catch (error) {
        console.error('解析polyline失败:', error, 'polyline:', route.polyline);
      }
    }
  });

  return polylines;
}

module.exports = {
  NAVIGATION_CONFIG,
  buildRouteUrl,
  planRoute,
  parseRouteData,
  convertToMapPolyline
};
