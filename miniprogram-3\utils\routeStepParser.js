// routeStepParser.js - 路线步骤解析工具
// 用于解析高德地图API返回的路线步骤，生成导航指引

// 方向动作映射
const DIRECTION_ACTIONS = {
  // 基本方向
  '直行': { action: 'straight', arrow: '↑' },
  '左转': { action: 'turn-left', arrow: '←' },
  '右转': { action: 'turn-right', arrow: '→' },
  '掉头': { action: 'uturn', arrow: '↩' },
  
  // 细分方向
  '稍向左转': { action: 'turn-slight-left', arrow: '↖' },
  '稍向右转': { action: 'turn-slight-right', arrow: '↗' },
  '急左转': { action: 'turn-sharp-left', arrow: '⬅' },
  '急右转': { action: 'turn-sharp-right', arrow: '➡' },
  
  // 特殊动作
  '出发': { action: 'start', arrow: '🚀' },
  '到达': { action: 'end', arrow: '🏁' },
  '继续': { action: 'continue', arrow: '↑' },
  '进入': { action: 'enter', arrow: '↑' },
  '离开': { action: 'exit', arrow: '↗' }
};

// 路况类型映射
const ROAD_TYPES = {
  '高速公路': 'highway',
  '城市快速路': 'expressway',
  '主干道': 'arterial',
  '次干道': 'secondary',
  '支路': 'local',
  '步行道': 'walkway',
  '自行车道': 'bikeway'
};

// 解析路线步骤
function parseRouteSteps(routeData, routeType = 'walking') {
  if (!routeData || !routeData.routes || routeData.routes.length === 0) {
    return [];
  }
  
  const route = routeData.routes[0];
  const steps = route.steps || [];
  const parsedSteps = [];
  
  steps.forEach((step, index) => {
    const parsedStep = parseStep(step, index, routeType);
    if (parsedStep) {
      parsedSteps.push(parsedStep);
    }
  });
  
  // 添加起点和终点步骤
  if (parsedSteps.length > 0) {
    // 修改第一步为出发
    parsedSteps[0] = {
      ...parsedSteps[0],
      action: 'start',
      arrow: '🚀',
      instruction: '开始导航'
    };
    
    // 添加到达终点步骤
    parsedSteps.push({
      index: parsedSteps.length,
      action: 'end',
      arrow: '🏁',
      instruction: '到达目的地',
      road: '',
      distance: '0m',
      duration: '',
      polyline: '',
      orientation: ''
    });
  }
  
  return parsedSteps;
}

// 解析单个步骤
function parseStep(step, index, routeType) {
  const instruction = step.instruction || '';
  const road = step.road || '';
  const distance = parseInt(step.distance) || 0;
  const duration = parseInt(step.duration) || 0;
  
  // 解析指令中的方向动作
  const directionInfo = parseDirection(instruction);
  
  // 生成完整的导航指令
  const fullInstruction = generateInstruction(directionInfo, road, distance, routeType);
  
  return {
    index: index,
    action: directionInfo.action,
    arrow: directionInfo.arrow,
    instruction: fullInstruction,
    road: road,
    distance: formatDistance(distance),
    duration: formatDuration(duration),
    polyline: step.polyline || '',
    orientation: step.orientation || '',
    rawInstruction: instruction
  };
}

// 解析方向信息
function parseDirection(instruction) {
  // 默认值
  let result = { action: 'straight', arrow: '↑' };
  
  // 遍历方向动作映射，查找匹配的关键词
  for (const [keyword, actionInfo] of Object.entries(DIRECTION_ACTIONS)) {
    if (instruction.includes(keyword)) {
      result = actionInfo;
      break;
    }
  }
  
  // 特殊情况处理
  if (instruction.includes('向左') && !instruction.includes('稍向')) {
    result = DIRECTION_ACTIONS['左转'];
  } else if (instruction.includes('向右') && !instruction.includes('稍向')) {
    result = DIRECTION_ACTIONS['右转'];
  }
  
  return result;
}

// 生成导航指令
function generateInstruction(directionInfo, road, distance, routeType) {
  let instruction = '';
  
  // 基础指令
  switch (directionInfo.action) {
    case 'start':
      instruction = '开始导航';
      break;
    case 'end':
      instruction = '到达目的地';
      break;
    case 'straight':
      instruction = '直行';
      break;
    case 'turn-left':
      instruction = '左转';
      break;
    case 'turn-right':
      instruction = '右转';
      break;
    case 'turn-slight-left':
      instruction = '稍向左转';
      break;
    case 'turn-slight-right':
      instruction = '稍向右转';
      break;
    case 'turn-sharp-left':
      instruction = '急左转';
      break;
    case 'turn-sharp-right':
      instruction = '急右转';
      break;
    case 'uturn':
      instruction = '掉头';
      break;
    case 'continue':
      instruction = '继续前行';
      break;
    case 'enter':
      instruction = '进入';
      break;
    case 'exit':
      instruction = '离开';
      break;
    default:
      instruction = '继续前行';
  }
  
  // 添加道路信息
  if (road && directionInfo.action !== 'start' && directionInfo.action !== 'end') {
    if (directionInfo.action === 'enter') {
      instruction = `进入${road}`;
    } else if (directionInfo.action === 'exit') {
      instruction = `离开${road}`;
    } else {
      instruction += `，进入${road}`;
    }
  }
  
  // 添加距离信息（对于较长的路段）
  if (distance > 100 && directionInfo.action !== 'start' && directionInfo.action !== 'end') {
    instruction += `，行驶${formatDistance(distance)}`;
  }
  
  // 根据导航类型调整用词
  if (routeType === 'walking' || routeType === '步行') {
    instruction = instruction.replace('行驶', '步行');
  } else if (routeType === 'bicycling' || routeType === '骑行') {
    instruction = instruction.replace('行驶', '骑行');
  }
  
  return instruction;
}

// 根据角度获取方向箭头
function getArrowByOrientation(orientation) {
  if (!orientation) return '↑';
  
  const angle = parseInt(orientation);
  
  if (angle >= 337.5 || angle < 22.5) return '↑';      // 北
  else if (angle >= 22.5 && angle < 67.5) return '↗';  // 东北
  else if (angle >= 67.5 && angle < 112.5) return '→'; // 东
  else if (angle >= 112.5 && angle < 157.5) return '↘'; // 东南
  else if (angle >= 157.5 && angle < 202.5) return '↓'; // 南
  else if (angle >= 202.5 && angle < 247.5) return '↙'; // 西南
  else if (angle >= 247.5 && angle < 292.5) return '←'; // 西
  else if (angle >= 292.5 && angle < 337.5) return '↖'; // 西北
  
  return '↑';
}

// 格式化距离
function formatDistance(distance) {
  const dist = parseInt(distance);
  if (dist < 1000) {
    return dist + 'm';
  } else {
    return (dist / 1000).toFixed(1) + 'km';
  }
}

// 格式化时间
function formatDuration(duration) {
  const dur = parseInt(duration);
  if (dur < 60) {
    return dur + '秒';
  } else if (dur < 3600) {
    return Math.floor(dur / 60) + '分钟';
  } else {
    const hours = Math.floor(dur / 3600);
    const minutes = Math.floor((dur % 3600) / 60);
    return hours + '小时' + minutes + '分钟';
  }
}

// 计算剩余距离
function calculateRemainingDistance(steps, currentIndex) {
  let remaining = 0;
  for (let i = currentIndex; i < steps.length; i++) {
    const distance = parseDistance(steps[i].distance);
    remaining += distance;
  }
  return remaining;
}

// 解析距离字符串为数字
function parseDistance(distanceStr) {
  if (typeof distanceStr === 'number') return distanceStr;
  if (typeof distanceStr === 'string') {
    const match = distanceStr.match(/(\d+(?:\.\d+)?)/);
    if (match) {
      const value = parseFloat(match[1]);
      if (distanceStr.includes('km')) {
        return value * 1000;
      }
      return value;
    }
  }
  return 0;
}

// 生成语音播报文本
function generateVoiceText(step, remainingDistance, options = {}) {
  let voiceText = step.instruction;

  // 添加剩余距离信息
  if (remainingDistance > 1000) {
    voiceText += `，剩余${formatDistance(remainingDistance)}`;
  } else if (remainingDistance > 100) {
    voiceText += `，剩余${remainingDistance}米`;
  }

  // 电动车特有的播报增强
  if (options.vehicleType === 'electrobike') {
    // 添加电动车安全提醒
    if (step.action && step.action.includes('turn')) {
      voiceText += '，请减速慢行';
    }

    // 添加路况提醒
    if (step.road && step.road.includes('主路')) {
      voiceText += '，注意车流';
    }

    // 添加坡度提醒
    if (step.instruction.includes('上坡') || step.instruction.includes('下坡')) {
      voiceText += '，注意控制车速';
    }
  }

  // 简化语音文本
  voiceText = voiceText.replace('，进入', '进入');
  voiceText = voiceText.replace('，行驶', '');
  voiceText = voiceText.replace('，步行', '');
  voiceText = voiceText.replace('，骑行', '');

  // 添加距离播报的语音优化
  if (remainingDistance <= 50 && remainingDistance > 0) {
    voiceText = `即将${step.instruction}`;
  } else if (remainingDistance <= 200 && remainingDistance > 50) {
    voiceText = `前方${remainingDistance}米${step.instruction}`;
  }

  return voiceText;
}

// 生成电动车特有播报文本
function generateElectricVehicleVoiceText(type, data) {
  switch (type) {
    case 'lowBattery':
      if (data.level <= 10) {
        return `电量严重不足，仅剩${data.level}%，请立即寻找充电站`;
      } else if (data.level <= 20) {
        return `电量不足${data.level}%，建议尽快充电`;
      }
      return `电量${data.level}%，请注意电量状况`;

    case 'chargingStation':
      return `前方${data.distance}米有充电站"${data.name}"，是否前往充电？`;

    case 'speedLimit':
      return `当前限速${data.limit}公里每小时，请控制车速`;

    case 'restrictionZone':
      return `前方进入限行区域，电动车请注意绕行`;

    case 'weatherWarning':
      if (data.type === 'rain') {
        return '当前下雨天气，路面湿滑，请减速慢行';
      } else if (data.type === 'wind') {
        return '当前风力较大，请注意保持平衡';
      }
      return '天气状况不佳，请注意安全';

    case 'trafficJam':
      return `前方路段拥堵，预计延误${data.delay}分钟，建议选择其他路线`;

    case 'arrival':
      return '您已到达目的地，导航结束，感谢使用电动车导航，祝您生活愉快';

    default:
      return data.message || '';
  }
}

// 判断是否需要语音提醒
function shouldPlayVoice(step, previousStep) {
  // 起点和终点总是播报
  if (step.action === 'start' || step.action === 'end') {
    return true;
  }
  
  // 转向动作需要播报
  if (step.action.includes('turn') || step.action === 'uturn') {
    return true;
  }
  
  // 进入新道路需要播报
  if (step.road && previousStep && step.road !== previousStep.road) {
    return true;
  }
  
  // 长距离直行每隔一定距离播报
  const distance = parseDistance(step.distance);
  if (distance > 1000 && step.action === 'straight') {
    return true;
  }
  
  return false;
}

module.exports = {
  parseRouteSteps,
  parseStep,
  parseDirection,
  generateInstruction,
  getArrowByOrientation,
  formatDistance,
  formatDuration,
  calculateRemainingDistance,
  parseDistance,
  generateVoiceText,
  generateElectricVehicleVoiceText,
  shouldPlayVoice,
  DIRECTION_ACTIONS,
  ROAD_TYPES
};
