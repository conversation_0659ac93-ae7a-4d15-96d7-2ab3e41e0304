// staticMapConfig.js - 高德静态地图配置和工具
const CONFIG = require('./config.js');

// 地图缓存管理
const MAP_CACHE = {
  cache: new Map(),
  maxSize: 50,  // 最大缓存数量
  ttl: 10 * 60 * 1000,  // 缓存时间：10分钟

  // 生成缓存键
  generateKey: function(options) {
    const keyParts = [
      options.location || '',
      options.zoom || '',
      options.size || '',
      options.scale || '',
      options.traffic || '',
      options.markers || '',
      options.paths || ''
    ];
    return keyParts.join('|');
  },

  // 获取缓存
  get: function(key) {
    const item = this.cache.get(key);
    if (item && Date.now() - item.timestamp < this.ttl) {
      console.log('地图缓存命中:', key);
      return item.data;
    }
    if (item) {
      this.cache.delete(key);  // 删除过期缓存
    }
    return null;
  },

  // 设置缓存
  set: function(key, data) {
    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      data: data,
      timestamp: Date.now()
    });
    console.log('地图缓存设置:', key);
  },

  // 清除缓存
  clear: function() {
    this.cache.clear();
    console.log('地图缓存已清除');
  }
};

// 静态地图配置常量
const STATIC_MAP_CONFIG = {
  BASE_URL: 'https://restapi.amap.com/v3/staticmap',
  
  // 默认参数 - 根据官方文档优化
  DEFAULTS: {
    zoom: 15,           // 地图级别 [1,17] - 15级平衡清晰度和范围
    size: '600*450',    // 图片大小，优化为更好的宽高比
    scale: 2,           // 2:高清图 - 图片尺寸和zoom都会增加一倍
    traffic: 0          // 0:不显示路况 1:显示路况
  },

  // 电动车专用地图参数
  ELECTROBIKE_DEFAULTS: {
    zoom: 16,           // 电动车导航需要更高的缩放级别
    size: '600*450',    // 保持一致的尺寸
    scale: 2,           // 高清显示
    traffic: 1,         // 显示路况信息，对电动车很重要
    showRestrictions: true,  // 显示限行信息
    showChargingStations: true // 显示充电站
  },
  
  // 标记样式预设 - 根据官方文档规范
  MARKER_STYLES: {
    RED_A: 'mid,0xFF0000,A',           // 中等红色标记A
    GREEN_B: 'mid,0x008000,B',         // 中等绿色标记B
    BLUE_C: 'mid,0x0000FF,C',          // 中等蓝色标记C
    YELLOW_SMALL: 'small,0xFFFF00,1',  // 小号黄色标记1
    ORANGE_LARGE: 'large,0xffa500,2',  // 大号橙色标记2
    DEFAULT_CURRENT: 'mid,0xFC6054,A', // 默认当前位置标记（官方默认色）
    // 电动车专用标记
    ELECTROBIKE_START: 'mid,0xFF6B6B,E',    // 电动车起点标记E
    ELECTROBIKE_END: 'mid,0xFF6B6B,F',      // 电动车终点标记F
    CHARGING_STATION: 'small,0x00FF00,⚡',   // 充电站标记
    RESTRICTION_ZONE: 'small,0xFF0000,⚠',   // 限行区域标记
    SLOPE_WARNING: 'small,0xFFA500,▲'       // 坡度警告标记
  },
  
  // 常用位置
  LOCATIONS: {
    BEIJING_TIANANMEN: '116.397428,39.90923',
    SHANGHAI_BUND: '121.499763,31.239580',
    GUANGZHOU_TOWER: '113.324520,23.109722',
    SHENZHEN_CIVIC: '114.025974,22.546054'
  },

  // 路径样式配置
  PATH_STYLES: {
    WALKING: {
      width: 4,
      color: '0x4CAF50',    // 绿色 - 步行
      opacity: 1,
      description: '步行路径'
    },
    BICYCLING: {
      width: 5,
      color: '0x2196F3',    // 蓝色 - 骑行
      opacity: 1,
      description: '骑行路径'
    },
    DRIVING: {
      width: 6,
      color: '0xFF5722',    // 橙红色 - 驾车
      opacity: 1,
      description: '驾车路径'
    },
    ELECTROBIKE: {
      width: 7,
      color: '0xFF6B6B',    // 红色 - 电动车
      opacity: 1,
      description: '电动车路径',
      // 电动车专用样式特性
      specialFeatures: {
        showRestrictions: true,    // 显示限行区域
        showChargingStations: true, // 显示充电站
        showSlope: true,           // 显示坡度信息
        energyOptimized: true      // 能耗优化路径
      }
    },
    DEFAULT: {
      width: 5,
      color: '0x007AFF',    // 默认蓝色
      opacity: 1,
      description: '默认路径'
    }
  },

  // URL长度限制配置
  URL_LIMITS: {
    MAX_URL_LENGTH: 2048,     // 浏览器URL长度限制
    MAX_PATH_POINTS: 50,      // 最大路径点数
    MIN_PATH_POINTS: 2,       // 最小路径点数（起终点）
    SIMPLIFY_THRESHOLD: 30    // 超过此数量开始简化
  }
};

// 构建标准的静态地图URL（带缓存）
function buildStandardStaticMapUrl(options = {}) {
  // 生成缓存键
  const cacheKey = MAP_CACHE.generateKey(options);

  // 检查缓存
  const cachedUrl = MAP_CACHE.get(cacheKey);
  if (cachedUrl) {
    return cachedUrl;
  }

  const params = {
    key: CONFIG.AMAP_WEB_KEY, // 使用Web服务API密钥
    zoom: options.zoom || STATIC_MAP_CONFIG.DEFAULTS.zoom,
    size: options.size || STATIC_MAP_CONFIG.DEFAULTS.size
  };

  // 添加可选参数
  if (options.location) params.location = options.location;
  if (options.scale) params.scale = options.scale;
  if (options.markers) params.markers = options.markers;
  if (options.labels) params.labels = options.labels;
  if (options.paths) params.paths = options.paths;
  if (options.traffic !== undefined) params.traffic = options.traffic;

  // 构建URL
  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');

  const url = `${STATIC_MAP_CONFIG.BASE_URL}?${queryString}`;

  // 缓存URL
  MAP_CACHE.set(cacheKey, url);

  return url;
}

// 获取当前位置的静态地图
function getCurrentLocationMap(customOptions = {}) {
  return new Promise((resolve, reject) => {
    // 获取当前位置
    wx.getLocation({
      type: 'gcj02',
      success: function(location) {
        const longitude = location.longitude;
        const latitude = location.latitude;
        const locationStr = `${longitude},${latitude}`;
        
        // 构建地图参数 - 根据官方文档优化高清地图
        const mapOptions = {
          location: locationStr,
          zoom: 14,           // 适中缩放级别，scale=2时会自动变为15级
          size: '512*400',    // 基础尺寸，scale=2时会自动变为1024*800
          scale: 2,           // 高清图：图片尺寸和zoom都增加一倍
          markers: `mid,0xFC6054,A:${locationStr}`, // 使用官方默认颜色的中等标记
          ...customOptions
        };
        
        const mapUrl = buildStandardStaticMapUrl(mapOptions);
        
        console.log('当前位置静态地图URL:', mapUrl);
        
        resolve({
          success: true,
          mapUrl: mapUrl,
          location: {
            longitude: longitude,
            latitude: latitude
          },
          message: '当前位置地图生成成功'
        });
      },
      fail: function(error) {
        console.log('定位失败，使用默认位置:', error);
        
        // 使用默认位置（北京天安门）- 同样使用高清地图配置
        const defaultOptions = {
          location: STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN,
          zoom: 14,           // 适中缩放级别，scale=2时自动变为15级
          size: '512*400',    // 基础尺寸，scale=2时自动变为1024*800
          scale: 2,           // 高清图
          markers: `mid,0xFC6054,A:${STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN}`, // 官方默认样式
          ...customOptions
        };
        
        const mapUrl = buildStandardStaticMapUrl(defaultOptions);
        
        resolve({
          success: true,
          mapUrl: mapUrl,
          location: {
            longitude: 116.397428,
            latitude: 39.90923
          },
          message: '使用默认位置（北京天安门）',
          isDefault: true
        });
      }
    });
  });
}

// 获取指定位置的静态地图
function getLocationMap(longitude, latitude, options = {}) {
  const locationStr = `${longitude},${latitude}`;
  
  const mapOptions = {
    location: locationStr,
    zoom: options.zoom || 15,
    size: options.size || '400*400',
    scale: options.scale || 2,
    markers: options.markers || `mid,0xFF0000,A:${locationStr}`,
    ...options
  };
  
  return buildStandardStaticMapUrl(mapOptions);
}

// 测试静态地图URL是否有效
function testStaticMapUrl(url) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: url,
      method: 'GET',
      success: function(res) {
        console.log('静态地图URL测试结果:', res);
        
        if (res.statusCode === 200) {
          // 检查返回的内容类型
          const contentType = res.header['content-type'] || res.header['Content-Type'] || '';
          
          if (contentType.includes('image')) {
            resolve({
              success: true,
              message: '静态地图URL有效',
              url: url,
              contentType: contentType
            });
          } else {
            // 可能返回了错误信息
            reject({
              success: false,
              message: 'API返回非图片内容，可能是错误信息',
              url: url,
              data: res.data
            });
          }
        } else {
          reject({
            success: false,
            message: `HTTP错误: ${res.statusCode}`,
            url: url
          });
        }
      },
      fail: function(error) {
        reject({
          success: false,
          message: '网络请求失败',
          error: error,
          url: url
        });
      }
    });
  });
}

// 创建多种样式的地图示例 - 根据官方文档优化
function createMapExamples() {
  const examples = [];

  // 大范围地图（城市级别）
  examples.push({
    name: '大范围地图（城市级别）',
    url: buildStandardStaticMapUrl({
      location: STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN,
      zoom: 10,  // 城市级别缩放
      scale: 2,  // 高清图
      markers: STATIC_MAP_CONFIG.MARKER_STYLES.DEFAULT_CURRENT + ':' + STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN
    })
  });

  // 中等范围地图（区域级别）
  examples.push({
    name: '中等范围地图（区域级别）',
    url: buildStandardStaticMapUrl({
      location: STATIC_MAP_CONFIG.LOCATIONS.SHANGHAI_BUND,
      zoom: 13,  // 区域级别缩放
      scale: 2,  // 高清图
      markers: STATIC_MAP_CONFIG.MARKER_STYLES.GREEN_B + ':' + STATIC_MAP_CONFIG.LOCATIONS.SHANGHAI_BUND
    })
  });

  // 小范围地图（街道级别）
  examples.push({
    name: '小范围地图（街道级别）',
    url: buildStandardStaticMapUrl({
      location: STATIC_MAP_CONFIG.LOCATIONS.GUANGZHOU_TOWER,
      zoom: 15,  // 街道级别缩放
      scale: 2,  // 高清图
      markers: STATIC_MAP_CONFIG.MARKER_STYLES.BLUE_C + ':' + STATIC_MAP_CONFIG.LOCATIONS.GUANGZHOU_TOWER
    })
  });

  // 带路况的地图
  examples.push({
    name: '带路况地图',
    url: buildStandardStaticMapUrl({
      location: STATIC_MAP_CONFIG.LOCATIONS.SHENZHEN_CIVIC,
      zoom: 12,  // 适中缩放级别
      scale: 2,  // 高清图
      traffic: 1, // 显示路况
      markers: STATIC_MAP_CONFIG.MARKER_STYLES.ORANGE_LARGE + ':' + STATIC_MAP_CONFIG.LOCATIONS.SHENZHEN_CIVIC
    })
  });

  return examples;
}

// 构建自定义静态地图URL（支持路径和多标记）
function buildCustomStaticMapUrl(options = {}) {
  return new Promise((resolve, reject) => {
    try {
      const baseUrl = 'https://restapi.amap.com/v3/staticmap';

      // 基础参数
      const params = {
        key: CONFIG.AMAP_WEB_KEY, // 使用Web服务API密钥
        size: options.size || STATIC_MAP_CONFIG.DEFAULTS.size,
        scale: options.scale || STATIC_MAP_CONFIG.DEFAULTS.scale,
        zoom: options.zoom || STATIC_MAP_CONFIG.DEFAULTS.zoom,
        traffic: options.traffic || STATIC_MAP_CONFIG.DEFAULTS.traffic
      };

      // 添加中心点（如果提供）
      if (options.location) {
        params.location = options.location;
      }

      // 添加标记点
      if (options.markers) {
        params.markers = options.markers;
      }

      // 添加路径
      if (options.paths) {
        params.paths = options.paths;
      }

      // 添加标签
      if (options.labels) {
        params.labels = options.labels;
      }

      // 构建URL
      const queryString = Object.entries(params)
        .filter(([key, value]) => value !== undefined && value !== null && value !== '')
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&');

      const mapUrl = `${baseUrl}?${queryString}`;

      console.log('自定义静态地图URL:', mapUrl);

      resolve({
        success: true,
        mapUrl: mapUrl,
        params: params,
        message: '自定义静态地图URL生成成功'
      });

    } catch (error) {
      console.error('构建自定义静态地图URL失败:', error);
      reject({
        success: false,
        message: '构建静态地图URL失败: ' + error.message,
        error: error
      });
    }
  });
}

// 解析polyline坐标字符串为坐标数组
function parsePolylineToCoords(polyline) {
  try {
    if (!polyline || typeof polyline !== 'string') {
      console.warn('parsePolylineToCoords: 无效的polyline输入');
      return [];
    }

    // 高德API的polyline格式：经度,纬度;经度,纬度;经度,纬度...
    const coordPairs = polyline.split(';');
    const coords = [];

    for (let i = 0; i < coordPairs.length; i++) {
      const pair = coordPairs[i].trim();
      if (!pair) continue;

      const [lng, lat] = pair.split(',');

      // 验证坐标有效性
      const longitude = parseFloat(lng);
      const latitude = parseFloat(lat);

      if (isNaN(longitude) || isNaN(latitude)) {
        console.warn(`parsePolylineToCoords: 无效坐标对 "${pair}"`);
        continue;
      }

      // 验证坐标范围（中国境内大致范围）
      if (longitude < 73 || longitude > 135 || latitude < 3 || latitude > 54) {
        console.warn(`parsePolylineToCoords: 坐标超出合理范围 "${pair}"`);
        continue;
      }

      coords.push(`${longitude},${latitude}`);
    }

    console.log(`parsePolylineToCoords: 成功解析 ${coords.length} 个坐标点`);
    return coords;

  } catch (error) {
    console.error('parsePolylineToCoords: 解析失败', error);
    return [];
  }
}

// 构建优化的路径字符串
function buildOptimizedPathString(pathPoints, routeType = 'DEFAULT', options = {}) {
  try {
    if (!pathPoints || pathPoints.length < STATIC_MAP_CONFIG.URL_LIMITS.MIN_PATH_POINTS) {
      console.warn('buildOptimizedPathString: 路径点数量不足');
      return '';
    }

    // 获取路径样式配置
    const pathStyle = STATIC_MAP_CONFIG.PATH_STYLES[routeType.toUpperCase()] ||
                     STATIC_MAP_CONFIG.PATH_STYLES.DEFAULT;

    // 应用自定义样式选项
    const finalStyle = {
      width: options.width || pathStyle.width,
      color: options.color || pathStyle.color,
      opacity: options.opacity || pathStyle.opacity
    };

    // 智能简化路径点
    let optimizedPoints = pathPoints;
    const maxPoints = options.maxPoints || STATIC_MAP_CONFIG.URL_LIMITS.MAX_PATH_POINTS;

    if (pathPoints.length > STATIC_MAP_CONFIG.URL_LIMITS.SIMPLIFY_THRESHOLD) {
      optimizedPoints = simplifyPath(pathPoints, maxPoints);
      console.log(`buildOptimizedPathString: 路径简化 ${pathPoints.length} -> ${optimizedPoints.length} 个点`);
    }

    // 构建路径字符串：线宽,颜色,透明度,填充色,填充透明度:坐标点
    const pathString = `${finalStyle.width},${finalStyle.color},${finalStyle.opacity},,${finalStyle.opacity}:${optimizedPoints.join(';')}`;

    // 验证URL长度
    if (pathString.length > STATIC_MAP_CONFIG.URL_LIMITS.MAX_URL_LENGTH / 2) {
      console.warn('buildOptimizedPathString: 路径字符串可能过长，建议进一步简化');
    }

    console.log(`buildOptimizedPathString: 生成路径字符串，${optimizedPoints.length}个点，${pathStyle.description}`);
    return pathString;

  } catch (error) {
    console.error('buildOptimizedPathString: 构建失败', error);
    return '';
  }
}

// 验证路径有效性
function validatePathData(pathPoints, startLocation, endLocation) {
  const validation = {
    isValid: false,
    issues: [],
    recommendations: []
  };

  // 检查路径点数量
  if (!pathPoints || pathPoints.length === 0) {
    validation.issues.push('路径点数组为空');
    validation.recommendations.push('使用起终点连线作为降级方案');
    return validation;
  }

  if (pathPoints.length < STATIC_MAP_CONFIG.URL_LIMITS.MIN_PATH_POINTS) {
    validation.issues.push(`路径点数量不足，至少需要${STATIC_MAP_CONFIG.URL_LIMITS.MIN_PATH_POINTS}个点`);
  }

  // 检查起终点一致性
  if (startLocation && endLocation) {
    const firstPoint = pathPoints[0];
    const lastPoint = pathPoints[pathPoints.length - 1];

    const startCoord = `${startLocation.longitude},${startLocation.latitude}`;
    const endCoord = `${endLocation.longitude},${endLocation.latitude}`;

    if (firstPoint !== startCoord) {
      validation.issues.push('路径起点与指定起点不一致');
      validation.recommendations.push('检查路线数据的起点坐标');
    }

    if (lastPoint !== endCoord) {
      validation.issues.push('路径终点与指定终点不一致');
      validation.recommendations.push('检查路线数据的终点坐标');
    }
  }

  // 检查路径点密度
  if (pathPoints.length > STATIC_MAP_CONFIG.URL_LIMITS.MAX_PATH_POINTS * 2) {
    validation.issues.push('路径点过于密集');
    validation.recommendations.push('建议使用路径简化算法');
  }

  validation.isValid = validation.issues.length === 0;
  return validation;
}

// 构建路线静态地图URL
function buildRouteStaticMapUrl(startLocation, endLocation, routeData, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      if (!startLocation || !endLocation) {
        reject({
          success: false,
          message: '起点或终点信息缺失'
        });
        return;
      }

      // 计算地图中心点
      const centerLng = (startLocation.longitude + endLocation.longitude) / 2;
      const centerLat = (startLocation.latitude + endLocation.latitude) / 2;
      const centerLocation = `${centerLng},${centerLat}`;

      // 构建标记点字符串
      const markers = [
        `large,0x4caf50,S:${startLocation.longitude},${startLocation.latitude}`, // 起点
        `large,0xf44336,E:${endLocation.longitude},${endLocation.latitude}`     // 终点
      ];

      // 添加路径中的转向点标记
      if (routeData && routeData.routes && routeData.routes.length > 0) {
        const route = routeData.routes[0];
        const steps = route.steps || [];

        steps.forEach((step, index) => {
          // 只标记重要的转向点
          if (step.action && ['turn-left', 'turn-right', 'uturn'].includes(step.action)) {
            if (step.start_location) {
              const coords = step.start_location.split(',');
              if (coords.length === 2) {
                markers.push(`mid,0x007aff,${index + 1}:${coords[0]},${coords[1]}`);
              }
            }
          }
        });
      }

      // 构建优化的路径字符串（使用真实的路线polyline数据）
      let pathString = '';
      let pathValidation = { isValid: false, issues: [], recommendations: [] };

      if (routeData && routeData.routes && routeData.routes.length > 0) {
        const route = routeData.routes[0];
        if (route.polyline) {
          // 使用parsePolylineToCoords解析真实路径
          const pathPoints = parsePolylineToCoords(route.polyline);

          // 验证路径数据有效性
          pathValidation = validatePathData(pathPoints, startLocation, endLocation);

          if (pathPoints.length > 0) {
            // 确定路径类型（从选项或路线数据中推断）
            const routeType = options.routeType ||
                             (route.mode ? route.mode.toUpperCase() : 'DEFAULT');

            // 使用优化的路径构建函数
            pathString = buildOptimizedPathString(pathPoints, routeType, {
              maxPoints: options.maxPathPoints || STATIC_MAP_CONFIG.URL_LIMITS.MAX_PATH_POINTS,
              width: options.pathWidth,
              color: options.pathColor,
              opacity: options.pathOpacity
            });

            console.log(`buildRouteStaticMapUrl: 路径构建完成，类型: ${routeType}`);

            // 记录验证问题（如果有）
            if (!pathValidation.isValid) {
              console.warn('buildRouteStaticMapUrl: 路径验证发现问题:', pathValidation.issues);
              if (pathValidation.recommendations.length > 0) {
                console.info('buildRouteStaticMapUrl: 建议:', pathValidation.recommendations);
              }
            }
          } else {
            // 如果polyline解析失败，降级为起终点连线
            console.warn('buildRouteStaticMapUrl: polyline解析失败，使用起终点连线');
            const fallbackPoints = [`${startLocation.longitude},${startLocation.latitude}`,
                                   `${endLocation.longitude},${endLocation.latitude}`];
            pathString = buildOptimizedPathString(fallbackPoints, 'DEFAULT');
          }
        } else {
          console.info('buildRouteStaticMapUrl: 路线数据中无polyline字段，跳过路径绘制');
        }
      } else {
        console.info('buildRouteStaticMapUrl: 无路线数据，跳过路径绘制');
      }

      // 构建地图选项
      const mapOptions = {
        location: centerLocation,
        size: options.size || '750*600',
        scale: options.scale || 2,
        zoom: options.zoom || 15,
        markers: markers.join('|'),
        paths: pathString,
        traffic: options.traffic || 0
      };

      buildCustomStaticMapUrl(mapOptions)
        .then(result => {
          resolve({
            ...result,
            message: '路线静态地图生成成功',
            routeInfo: {
              startLocation: startLocation,
              endLocation: endLocation,
              centerLocation: centerLocation
            }
          });
        })
        .catch(error => {
          reject(error);
        });

    } catch (error) {
      console.error('构建路线静态地图失败:', error);
      reject({
        success: false,
        message: '构建路线静态地图失败: ' + error.message,
        error: error
      });
    }
  });
}

// 简化路径点数组，保留关键点以避免URL过长
function simplifyPath(pathPoints, maxPoints) {
  if (pathPoints.length <= maxPoints) {
    return pathPoints;
  }

  const simplified = [];
  const step = Math.floor(pathPoints.length / (maxPoints - 2)); // 保留起终点

  // 始终保留起点
  simplified.push(pathPoints[0]);

  // 按步长选择中间点
  for (let i = step; i < pathPoints.length - 1; i += step) {
    if (simplified.length < maxPoints - 1) {
      simplified.push(pathPoints[i]);
    }
  }

  // 始终保留终点
  if (pathPoints.length > 1) {
    simplified.push(pathPoints[pathPoints.length - 1]);
  }

  console.log(`simplifyPath: 从 ${pathPoints.length} 个点简化为 ${simplified.length} 个点`);
  return simplified;
}

// 构建电动车专用静态地图URL（带缓存）
function buildElectrobikeStaticMapUrl(options = {}) {
  // 为电动车地图生成特殊的缓存键
  const electrobikeOptions = {
    ...options,
    type: 'electrobike'  // 标识为电动车地图
  };
  const cacheKey = MAP_CACHE.generateKey(electrobikeOptions);

  // 检查缓存
  const cachedUrl = MAP_CACHE.get(cacheKey);
  if (cachedUrl) {
    return cachedUrl;
  }

  const electrobikeDefaults = STATIC_MAP_CONFIG.ELECTROBIKE_DEFAULTS;

  const params = {
    key: CONFIG.AMAP_WEB_KEY,
    zoom: options.zoom || electrobikeDefaults.zoom,
    size: options.size || electrobikeDefaults.size,
    scale: options.scale || electrobikeDefaults.scale,
    traffic: options.traffic !== undefined ? options.traffic : electrobikeDefaults.traffic
  };

  // 添加位置参数
  if (options.location) params.location = options.location;

  // 添加电动车专用标记
  const markers = [];
  if (options.startPoint) {
    markers.push(`${STATIC_MAP_CONFIG.MARKER_STYLES.ELECTROBIKE_START}:${options.startPoint}`);
  }
  if (options.endPoint) {
    markers.push(`${STATIC_MAP_CONFIG.MARKER_STYLES.ELECTROBIKE_END}:${options.endPoint}`);
  }

  // 添加充电站标记
  if (options.chargingStations && Array.isArray(options.chargingStations)) {
    options.chargingStations.forEach(station => {
      markers.push(`${STATIC_MAP_CONFIG.MARKER_STYLES.CHARGING_STATION}:${station}`);
    });
  }

  // 添加限行区域标记
  if (options.restrictionZones && Array.isArray(options.restrictionZones)) {
    options.restrictionZones.forEach(zone => {
      markers.push(`${STATIC_MAP_CONFIG.MARKER_STYLES.RESTRICTION_ZONE}:${zone}`);
    });
  }

  if (markers.length > 0) {
    params.markers = markers.join('|');
  }

  // 添加电动车路径
  if (options.paths) {
    const electrobikeStyle = STATIC_MAP_CONFIG.PATH_STYLES.ELECTROBIKE;
    const pathString = buildOptimizedPathString(options.paths, {
      width: electrobikeStyle.width,
      color: electrobikeStyle.color,
      opacity: electrobikeStyle.opacity
    });
    params.paths = pathString;
  }

  // 构建URL
  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');

  const url = `${STATIC_MAP_CONFIG.BASE_URL}?${queryString}`;

  // 缓存URL
  MAP_CACHE.set(cacheKey, url);

  console.log('电动车专用地图URL:', url);
  return url;
}

// 优化电动车路径显示
function optimizeElectrobikePathDisplay(pathData, options = {}) {
  if (!pathData || !Array.isArray(pathData)) {
    console.warn('optimizeElectrobikePathDisplay: 无效的路径数据');
    return null;
  }

  const optimizedOptions = {
    // 电动车路径优化参数
    maxPoints: options.maxPoints || STATIC_MAP_CONFIG.URL_LIMITS.MAX_PATH_POINTS,
    simplifyThreshold: options.simplifyThreshold || STATIC_MAP_CONFIG.URL_LIMITS.SIMPLIFY_THRESHOLD,
    showEnergyInfo: options.showEnergyInfo !== false,
    showRestrictions: options.showRestrictions !== false,
    showChargingStations: options.showChargingStations !== false
  };

  // 简化路径点
  const simplifiedPath = pathData.length > optimizedOptions.simplifyThreshold
    ? simplifyPath(pathData, optimizedOptions.maxPoints)
    : pathData;

  // 提取关键信息点
  const keyPoints = {
    chargingStations: [],
    restrictionZones: [],
    slopeWarnings: []
  };

  // 分析路径中的特殊点（这里可以根据实际API返回的数据进行解析）
  if (options.routeInfo) {
    // 解析充电站信息
    if (options.routeInfo.chargingStations) {
      keyPoints.chargingStations = options.routeInfo.chargingStations;
    }

    // 解析限行信息
    if (options.routeInfo.restrictions) {
      keyPoints.restrictionZones = options.routeInfo.restrictions;
    }

    // 解析坡度信息
    if (options.routeInfo.slopes) {
      keyPoints.slopeWarnings = options.routeInfo.slopes;
    }
  }

  return {
    optimizedPath: simplifiedPath,
    keyPoints: keyPoints,
    displayOptions: optimizedOptions
  };
}

// 带重试机制的地图URL获取
function getMapUrlWithRetry(buildFunction, options = {}, maxRetries = 3) {
  return new Promise((resolve, reject) => {
    let retryCount = 0;

    function attemptBuild() {
      try {
        const url = buildFunction(options);

        // 验证URL长度
        if (url.length > STATIC_MAP_CONFIG.URL_LIMITS.MAX_URL_LENGTH) {
          throw new Error(`URL长度超限: ${url.length} > ${STATIC_MAP_CONFIG.URL_LIMITS.MAX_URL_LENGTH}`);
        }

        console.log(`地图URL构建成功 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, url);
        resolve(url);

      } catch (error) {
        retryCount++;
        console.warn(`地图URL构建失败 (尝试 ${retryCount}/${maxRetries + 1}):`, error.message);

        if (retryCount <= maxRetries) {
          // 简化选项后重试
          const simplifiedOptions = {
            ...options,
            // 减少路径点数量
            maxPoints: Math.max(10, (options.maxPoints || 50) * 0.7),
            // 降低地图质量
            scale: 1,
            // 移除非必要参数
            traffic: 0
          };

          setTimeout(() => attemptBuild(), 1000 * retryCount); // 递增延迟
        } else {
          reject(new Error(`地图URL构建失败，已重试${maxRetries}次: ${error.message}`));
        }
      }
    }

    attemptBuild();
  });
}

// 智能地图URL构建器
function buildSmartMapUrl(options = {}) {
  const isElectrobike = options.type === 'electrobike' ||
                       (options.vehicleMode && [1, 2, 3].includes(options.vehicleMode));

  if (isElectrobike) {
    return getMapUrlWithRetry(buildElectrobikeStaticMapUrl, options);
  } else {
    return getMapUrlWithRetry(buildStandardStaticMapUrl, options);
  }
}

module.exports = {
  STATIC_MAP_CONFIG,
  buildStandardStaticMapUrl,
  buildCustomStaticMapUrl,
  buildRouteStaticMapUrl,
  buildElectrobikeStaticMapUrl,    // 电动车专用地图URL构建
  optimizeElectrobikePathDisplay,  // 电动车路径显示优化
  getMapUrlWithRetry,             // 带重试机制的地图URL获取
  buildSmartMapUrl,               // 智能地图URL构建器
  getCurrentLocationMap,
  getLocationMap,
  testStaticMapUrl,
  createMapExamples,
  parsePolylineToCoords,      // polyline解码函数
  buildOptimizedPathString,   // 优化路径构建函数
  validatePathData,           // 路径验证函数
  // 缓存管理功能
  clearMapCache: MAP_CACHE.clear.bind(MAP_CACHE),  // 清除地图缓存
  getMapCacheStats: function() {  // 获取缓存统计信息
    return {
      size: MAP_CACHE.cache.size,
      maxSize: MAP_CACHE.maxSize,
      ttl: MAP_CACHE.ttl
    };
  }
};
