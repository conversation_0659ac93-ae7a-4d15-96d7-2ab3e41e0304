// vehicleDataManager.js - 电动车数据管理器
const mqttManager = require('./mqttManager.js');
const config = require('./config.js');
const errorHandler = require('./errorHandler.js');

// 车辆数据状态
let vehicleData = {
  // 基础状态
  connected: false,
  lastUpdate: null,
  
  // 电池信息
  battery: {
    level: 0,        // 电量百分比 0-100
    voltage: 0,      // 电压 V
    current: 0,      // 电流 A
    temperature: 0,  // 温度 °C
    charging: false  // 是否充电中
  },
  
  // 速度信息
  speed: {
    current: 0,      // 当前速度 km/h
    max: 25,         // 最大速度限制 km/h
    average: 0       // 平均速度 km/h
  },
  
  // 位置信息
  location: {
    latitude: 0,     // 纬度
    longitude: 0,    // 经度
    altitude: 0,     // 海拔 m
    heading: 0,      // 方向角 0-360°
    accuracy: 0      // 精度 m
  },
  
  // 驾驶模式
  mode: {
    current: 'adult', // 当前模式: youth | adult | elderly
    available: ['youth', 'adult', 'elderly']
  },
  
  // 系统状态
  system: {
    temperature: 0,   // 系统温度 °C
    errors: [],       // 错误列表
    warnings: []      // 警告列表
  }
};

// 驾驶模式配置
const DRIVING_MODES = {
  youth: {
    name: '青少年模式',
    maxSpeed: 15,
    acceleration: 'slow',
    description: '限速15km/h，适合青少年使用'
  },
  adult: {
    name: '成人模式', 
    maxSpeed: 25,
    acceleration: 'normal',
    description: '限速25km/h，正常驾驶模式'
  },
  elderly: {
    name: '老人模式',
    maxSpeed: 12,
    acceleration: 'gentle',
    description: '限速12km/h，平稳舒适驾驶'
  }
};

// 数据监听器
let dataListeners = new Map();

/**
 * 初始化车辆数据管理器
 * @returns {Promise} 初始化结果
 */
function initialize() {
  return new Promise((resolve, reject) => {
    console.log('初始化车辆数据管理器...');
    
    // 设置MQTT事件监听器
    mqttManager.setEventListeners({
      onConnected: handleMqttConnected,
      onMessage: handleMqttMessage,
      onDisconnected: handleMqttDisconnected
    });
    
    // 连接MQTT服务器
    mqttManager.connect()
      .then(result => {
        console.log('车辆数据管理器初始化成功');
        resolve({
          success: true,
          message: '车辆数据管理器初始化成功',
          data: vehicleData
        });
      })
      .catch(error => {
        console.error('车辆数据管理器初始化失败:', error);
        reject({
          success: false,
          message: '车辆数据管理器初始化失败',
          error: error
        });
      });
  });
}

/**
 * MQTT连接成功处理
 */
function handleMqttConnected() {
  console.log('MQTT连接成功，开始订阅车辆数据主题...');
  
  // 订阅所有车辆数据主题
  const topics = config.VEHICLE_TOPICS;
  
  Promise.all([
    mqttManager.subscribe(topics.VEHICLE_STATUS, handleVehicleStatus),
    mqttManager.subscribe(topics.BATTERY_INFO, handleBatteryInfo),
    mqttManager.subscribe(topics.SPEED_INFO, handleSpeedInfo),
    mqttManager.subscribe(topics.LOCATION_INFO, handleLocationInfo)
  ])
  .then(() => {
    console.log('所有车辆数据主题订阅成功');
    vehicleData.connected = true;
    notifyDataListeners('connection', { connected: true });
  })
  .catch(error => {
    console.error('车辆数据主题订阅失败:', error);
    errorHandler.handleError('MQTT_SUBSCRIPTION_ERROR', error);
  });
}

/**
 * MQTT消息处理
 * @param {string} topic 主题
 * @param {string} message 消息内容
 */
function handleMqttMessage(topic, message) {
  console.log('收到车辆数据:', { topic, message });
  
  try {
    const data = JSON.parse(message);
    vehicleData.lastUpdate = new Date();
    
    // 根据主题分发处理
    switch (topic) {
      case config.VEHICLE_TOPICS.VEHICLE_STATUS:
        handleVehicleStatus(topic, message);
        break;
      case config.VEHICLE_TOPICS.BATTERY_INFO:
        handleBatteryInfo(topic, message);
        break;
      case config.VEHICLE_TOPICS.SPEED_INFO:
        handleSpeedInfo(topic, message);
        break;
      case config.VEHICLE_TOPICS.LOCATION_INFO:
        handleLocationInfo(topic, message);
        break;
      default:
        console.log('未处理的主题:', topic);
    }
  } catch (error) {
    console.error('车辆数据解析错误:', error);
  }
}

/**
 * 处理车辆状态数据
 * @param {string} topic 主题
 * @param {string} message 消息内容
 */
function handleVehicleStatus(topic, message) {
  try {
    const data = JSON.parse(message);
    
    // 更新车辆状态
    if (data.mode) {
      vehicleData.mode.current = data.mode;
    }
    
    if (data.system) {
      vehicleData.system = { ...vehicleData.system, ...data.system };
    }
    
    console.log('车辆状态更新:', vehicleData);
    notifyDataListeners('status', vehicleData);
    
  } catch (error) {
    console.error('车辆状态数据解析错误:', error);
  }
}

/**
 * 处理电池信息数据
 * @param {string} topic 主题
 * @param {string} message 消息内容
 */
function handleBatteryInfo(topic, message) {
  try {
    const data = JSON.parse(message);
    
    // 更新电池信息
    vehicleData.battery = { ...vehicleData.battery, ...data };
    
    console.log('电池信息更新:', vehicleData.battery);
    notifyDataListeners('battery', vehicleData.battery);
    
    // 检查电池警告
    if (data.level < 20) {
      notifyDataListeners('warning', {
        type: 'low_battery',
        message: `电池电量低：${data.level}%`,
        level: data.level
      });
    }
    
  } catch (error) {
    console.error('电池信息数据解析错误:', error);
  }
}

/**
 * 处理速度信息数据
 * @param {string} topic 主题
 * @param {string} message 消息内容
 */
function handleSpeedInfo(topic, message) {
  try {
    const data = JSON.parse(message);
    
    // 更新速度信息
    vehicleData.speed = { ...vehicleData.speed, ...data };
    
    console.log('速度信息更新:', vehicleData.speed);
    notifyDataListeners('speed', vehicleData.speed);
    
    // 检查超速警告
    const currentMode = DRIVING_MODES[vehicleData.mode.current];
    if (data.current > currentMode.maxSpeed) {
      notifyDataListeners('warning', {
        type: 'overspeed',
        message: `当前速度${data.current}km/h超过${currentMode.name}限速${currentMode.maxSpeed}km/h`,
        currentSpeed: data.current,
        maxSpeed: currentMode.maxSpeed
      });
    }
    
  } catch (error) {
    console.error('速度信息数据解析错误:', error);
  }
}

/**
 * 处理位置信息数据
 * @param {string} topic 主题
 * @param {string} message 消息内容
 */
function handleLocationInfo(topic, message) {
  try {
    const data = JSON.parse(message);
    
    // 更新位置信息
    vehicleData.location = { ...vehicleData.location, ...data };
    
    console.log('位置信息更新:', vehicleData.location);
    notifyDataListeners('location', vehicleData.location);
    
  } catch (error) {
    console.error('位置信息数据解析错误:', error);
  }
}

/**
 * MQTT连接断开处理
 */
function handleMqttDisconnected() {
  console.log('MQTT连接断开');
  vehicleData.connected = false;
  notifyDataListeners('connection', { connected: false });
}

/**
 * 切换驾驶模式
 * @param {string} mode 驾驶模式 youth | adult | elderly
 * @returns {Promise} 切换结果
 */
function switchDrivingMode(mode) {
  return new Promise((resolve, reject) => {
    if (!DRIVING_MODES[mode]) {
      reject({
        success: false,
        message: `无效的驾驶模式: ${mode}`
      });
      return;
    }
    
    const command = {
      action: 'switch_mode',
      mode: mode,
      timestamp: Date.now()
    };
    
    mqttManager.publish(config.VEHICLE_TOPICS.MODE_CONTROL, JSON.stringify(command))
      .then(result => {
        console.log(`驾驶模式切换指令发送成功: ${mode}`);
        
        // 本地更新模式（实际应等待车辆确认）
        vehicleData.mode.current = mode;
        vehicleData.speed.max = DRIVING_MODES[mode].maxSpeed;
        
        notifyDataListeners('mode', {
          mode: mode,
          config: DRIVING_MODES[mode]
        });
        
        resolve({
          success: true,
          message: `已切换到${DRIVING_MODES[mode].name}`,
          mode: mode,
          config: DRIVING_MODES[mode]
        });
      })
      .catch(error => {
        console.error('驾驶模式切换失败:', error);
        reject({
          success: false,
          message: '驾驶模式切换失败',
          error: error
        });
      });
  });
}

/**
 * 设置速度限制
 * @param {number} speedLimit 速度限制 km/h
 * @returns {Promise} 设置结果
 */
function setSpeedLimit(speedLimit) {
  return new Promise((resolve, reject) => {
    if (speedLimit < 5 || speedLimit > 30) {
      reject({
        success: false,
        message: '速度限制必须在5-30km/h之间'
      });
      return;
    }
    
    const command = {
      action: 'set_speed_limit',
      speed_limit: speedLimit,
      timestamp: Date.now()
    };
    
    mqttManager.publish(config.VEHICLE_TOPICS.SPEED_LIMIT, JSON.stringify(command))
      .then(result => {
        console.log(`速度限制设置指令发送成功: ${speedLimit}km/h`);
        
        // 本地更新速度限制
        vehicleData.speed.max = speedLimit;
        
        notifyDataListeners('speed_limit', {
          speedLimit: speedLimit
        });
        
        resolve({
          success: true,
          message: `速度限制已设置为${speedLimit}km/h`,
          speedLimit: speedLimit
        });
      })
      .catch(error => {
        console.error('速度限制设置失败:', error);
        reject({
          success: false,
          message: '速度限制设置失败',
          error: error
        });
      });
  });
}

/**
 * 获取当前车辆数据
 * @returns {Object} 车辆数据
 */
function getVehicleData() {
  return {
    ...vehicleData,
    modes: DRIVING_MODES
  };
}

/**
 * 添加数据监听器
 * @param {string} type 监听类型
 * @param {function} listener 监听函数
 */
function addDataListener(type, listener) {
  if (!dataListeners.has(type)) {
    dataListeners.set(type, []);
  }
  dataListeners.get(type).push(listener);
}

/**
 * 移除数据监听器
 * @param {string} type 监听类型
 * @param {function} listener 监听函数
 */
function removeDataListener(type, listener) {
  if (dataListeners.has(type)) {
    const listeners = dataListeners.get(type);
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }
}

/**
 * 通知数据监听器
 * @param {string} type 数据类型
 * @param {Object} data 数据内容
 */
function notifyDataListeners(type, data) {
  if (dataListeners.has(type)) {
    dataListeners.get(type).forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('数据监听器执行错误:', error);
      }
    });
  }
}

/**
 * 断开连接
 * @returns {Promise} 断开结果
 */
function disconnect() {
  return mqttManager.disconnect()
    .then(result => {
      vehicleData.connected = false;
      dataListeners.clear();
      console.log('车辆数据管理器已断开');
      return result;
    });
}

module.exports = {
  initialize,
  disconnect,
  switchDrivingMode,
  setSpeedLimit,
  getVehicleData,
  addDataListener,
  removeDataListener,
  DRIVING_MODES
};
