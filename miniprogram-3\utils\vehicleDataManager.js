// vehicleDataManager.js - 电动车数据管理器
const mqttManager = require('./mqttManager.js');
const httpDataManager = require('./httpDataManager.js');
const config = require('./config.js');
const errorHandler = require('./errorHandler.js');
const vehicleDataParser = require('./vehicleDataParser.js');

// 数据源配置
const DATA_SOURCE_CONFIG = {
  // 优先使用HTTP API，失败时降级到MQTT模拟
  primary: 'http',    // 'http' | 'mqtt'
  fallback: 'mqtt',   // 降级方案
  autoSwitch: true    // 是否自动切换
};

let currentDataSource = null; // 当前使用的数据源

// 车辆数据状态
let vehicleData = {
  // 基础状态
  connected: false,
  lastUpdate: null,

  // 电池信息 (对应 car_data.power)
  battery: {
    level: 0,        // 电量百分比 0-100 (来自power字段)
    voltage: 0,      // 电压 V
    current: 0,      // 电流 A
    temperature: 0,  // 温度 °C
    charging: false  // 是否充电中
  },

  // 速度信息 (对应 car_data.speed)
  speed: {
    current: 0,      // 当前速度 km/h (来自speed字段)
    max: 25,         // 最大速度限制 km/h
    average: 0       // 平均速度 km/h
  },

  // 位置信息 (对应 gps_data)
  location: {
    latitude: 0,     // 纬度 (来自latitude字段)
    longitude: 0,    // 经度 (来自longitude字段)
    altitude: 0,     // 海拔 m
    heading: 0,      // 方向角 0-360°
    accuracy: 0      // 精度 m
  },

  // 驾驶模式 (对应 car_data.mod)
  mode: {
    current: 1,      // 当前模式: 1=youth | 2=adult | 3=elderly (来自mod字段)
    available: [1, 2, 3]
  },

  // 用户信息 (对应 user)
  user: {
    id: 0,
    username: '',
    role: '',
    authenticated: false
  },

  // 系统状态
  system: {
    temperature: 0,   // 系统温度 °C
    errors: [],       // 错误列表
    warnings: []      // 警告列表
  }
};

// 驾驶模式配置 (对应 car_data.mod 字段)
const DRIVING_MODES = {
  1: {
    name: '青少年模式',
    maxSpeed: 15,
    acceleration: 'slow',
    description: '限速15km/h，适合青少年使用',
    code: 1
  },
  2: {
    name: '成人模式',
    maxSpeed: 25,
    acceleration: 'normal',
    description: '限速25km/h，正常驾驶模式',
    code: 2
  },
  3: {
    name: '老人模式',
    maxSpeed: 12,
    acceleration: 'gentle',
    description: '限速12km/h，平稳舒适驾驶',
    code: 3
  }
};

// 模式代码映射
const MODE_CODE_MAP = {
  'youth': 1,
  'adult': 2,
  'elderly': 3,
  1: 'youth',
  2: 'adult',
  3: 'elderly'
};

// 数据监听器
let dataListeners = new Map();

/**
 * 初始化车辆数据管理器
 * @returns {Promise} 初始化结果
 */
function initialize() {
  return new Promise((resolve, reject) => {
    console.log('初始化车辆数据管理器...');

    // 尝试初始化主要数据源
    initializePrimaryDataSource()
      .then(result => {
        console.log('车辆数据管理器初始化成功，数据源:', currentDataSource);
        resolve({
          success: true,
          message: `车辆数据管理器初始化成功 (${currentDataSource})`,
          dataSource: currentDataSource,
          data: vehicleData
        });
      })
      .catch(error => {
        console.error('车辆数据管理器初始化失败:', error);
        reject({
          success: false,
          message: '车辆数据管理器初始化失败',
          error: error
        });
      });
  });
}

/**
 * 初始化主要数据源
 * @returns {Promise} 初始化结果
 */
async function initializePrimaryDataSource() {
  const primarySource = DATA_SOURCE_CONFIG.primary;
  const fallbackSource = DATA_SOURCE_CONFIG.fallback;

  console.log(`尝试初始化主要数据源: ${primarySource}`);

  try {
    // 尝试初始化主要数据源
    if (primarySource === 'http') {
      await initializeHttpDataSource();
      currentDataSource = 'http';
    } else if (primarySource === 'mqtt') {
      await initializeMqttDataSource();
      currentDataSource = 'mqtt';
    }

    console.log(`主要数据源 ${primarySource} 初始化成功`);
    return { success: true, dataSource: currentDataSource };

  } catch (error) {
    console.warn(`主要数据源 ${primarySource} 初始化失败:`, error);

    if (DATA_SOURCE_CONFIG.autoSwitch && fallbackSource) {
      console.log(`尝试降级到备用数据源: ${fallbackSource}`);

      try {
        if (fallbackSource === 'http') {
          await initializeHttpDataSource();
          currentDataSource = 'http';
        } else if (fallbackSource === 'mqtt') {
          await initializeMqttDataSource();
          currentDataSource = 'mqtt';
        }

        console.log(`备用数据源 ${fallbackSource} 初始化成功`);
        return { success: true, dataSource: currentDataSource };

      } catch (fallbackError) {
        console.error(`备用数据源 ${fallbackSource} 也初始化失败:`, fallbackError);
        throw fallbackError;
      }
    } else {
      throw error;
    }
  }
}

/**
 * 初始化HTTP数据源
 * @returns {Promise} 初始化结果
 */
async function initializeHttpDataSource() {
  console.log('初始化HTTP数据源...');

  // 设置HTTP数据源事件监听器
  httpDataManager.addEventListener('connected', handleDataSourceConnected);
  httpDataManager.addEventListener('dataUpdate', handleDataUpdate);
  httpDataManager.addEventListener('disconnected', handleDataSourceDisconnected);

  // 初始化HTTP数据管理器
  const result = await httpDataManager.initialize();
  console.log('HTTP数据源初始化成功:', result);
  return result;
}

/**
 * 初始化MQTT数据源
 * @returns {Promise} 初始化结果
 */
async function initializeMqttDataSource() {
  console.log('初始化MQTT数据源...');

  // 设置MQTT事件监听器
  mqttManager.setEventListeners({
    onConnected: handleDataSourceConnected,
    onMessage: handleMqttMessage,
    onDisconnected: handleDataSourceDisconnected
  });

  // 连接MQTT服务器
  const result = await mqttManager.connect();
  console.log('MQTT数据源初始化成功:', result);
  return result;
}

/**
 * 数据源连接成功处理（通用）
 */
function handleDataSourceConnected(data) {
  console.log('数据源连接成功:', data);
  vehicleData.connected = true;
  vehicleData.lastUpdate = new Date().toISOString();

  // 如果是MQTT数据源，需要订阅主题
  if (currentDataSource === 'mqtt') {
    handleMqttConnected();
  }
}

/**
 * 数据源断开连接处理（通用）
 */
function handleDataSourceDisconnected(data) {
  console.log('数据源连接断开:', data);
  vehicleData.connected = false;
}

/**
 * 数据更新处理（通用）
 */
function handleDataUpdate(data) {
  console.log('收到数据更新:', data);

  if (currentDataSource === 'http') {
    // HTTP数据源直接更新
    updateVehicleDataFromHttp(data);
  } else if (currentDataSource === 'mqtt') {
    // MQTT数据源通过消息处理
    handleMqttMessage('vehicle/data', JSON.stringify(data));
  }
}

/**
 * 从HTTP数据更新车辆数据
 * @param {Object} data HTTP API返回的数据
 */
function updateVehicleDataFromHttp(data) {
  try {
    // 更新连接状态
    vehicleData.connected = data.car?.connected || true;
    vehicleData.lastUpdate = data.timestamp || new Date().toISOString();

    // 更新电池信息
    if (data.car?.power !== undefined) {
      vehicleData.battery.level = data.car.power;
    }

    // 更新速度信息
    if (data.car?.speed !== undefined) {
      vehicleData.speed.current = data.car.speed;
    }

    // 更新位置信息
    if (data.gps) {
      vehicleData.location.latitude = data.gps.latitude || 0;
      vehicleData.location.longitude = data.gps.longitude || 0;
      vehicleData.location.accuracy = 10; // 默认精度
      vehicleData.location.lastUpdate = vehicleData.lastUpdate;
    }

    // 更新模式信息
    if (data.car?.mode) {
      vehicleData.mode.current = data.car.mode;
    }

    // 更新原始数据
    vehicleData.rawData = data;

    console.log('车辆数据已更新 (HTTP):', vehicleData);

    // 触发数据更新事件
    triggerDataUpdateEvent();

  } catch (error) {
    console.error('HTTP数据更新失败:', error);
  }
}

/**
 * MQTT连接成功处理
 */
function handleMqttConnected() {
  console.log('MQTT连接成功，开始订阅车辆数据主题...');
  
  // 订阅所有车辆数据主题
  const topics = config.VEHICLE_TOPICS;
  
  Promise.all([
    mqttManager.subscribe(topics.VEHICLE_STATUS, handleVehicleStatus),
    mqttManager.subscribe(topics.BATTERY_INFO, handleBatteryInfo),
    mqttManager.subscribe(topics.SPEED_INFO, handleSpeedInfo),
    mqttManager.subscribe(topics.LOCATION_INFO, handleLocationInfo)
  ])
  .then(() => {
    console.log('所有车辆数据主题订阅成功');
    vehicleData.connected = true;
    notifyDataListeners('connection', { connected: true });
  })
  .catch(error => {
    console.error('车辆数据主题订阅失败:', error);
    errorHandler.handleError('MQTT_SUBSCRIPTION_ERROR', error);
  });
}

/**
 * MQTT消息处理
 * @param {string} topic 主题
 * @param {string} message 消息内容
 */
function handleMqttMessage(topic, message) {
  console.log('收到车辆数据:', { topic, message });

  try {
    // 使用新的数据解析器处理完整的JSON数据
    const parseResult = vehicleDataParser.parseVehicleDataJSON(message);

    if (!parseResult.success) {
      console.error('数据解析失败:', parseResult.error);
      return;
    }

    // 验证数据完整性
    const validation = vehicleDataParser.validateData(JSON.parse(message));
    if (!validation.valid) {
      console.warn('数据验证失败:', validation.errors);
      // 继续处理，但记录警告
    }

    // 转换为标准格式
    const standardData = vehicleDataParser.convertToStandardFormat(parseResult);
    if (standardData) {
      // 更新本地车辆数据
      updateVehicleData(standardData);

      // 通知所有监听器
      notifyDataListeners('data_update', standardData);

      // 处理警告
      if (standardData.system.warnings.length > 0) {
        standardData.system.warnings.forEach(warning => {
          notifyDataListeners('warning', warning);
        });
      }
    }

    vehicleData.lastUpdate = new Date();

  } catch (error) {
    console.error('车辆数据解析错误:', error);
    errorHandler.handleError('VEHICLE_DATA_ERROR', error);
  }
}

/**
 * 更新车辆数据
 * @param {Object} standardData 标准格式的车辆数据
 */
function updateVehicleData(standardData) {
  // 更新电池信息
  vehicleData.battery = { ...vehicleData.battery, ...standardData.battery };

  // 更新速度信息
  vehicleData.speed = { ...vehicleData.speed, ...standardData.speed };

  // 更新位置信息
  vehicleData.location = { ...vehicleData.location, ...standardData.location };

  // 更新驾驶模式
  vehicleData.mode = { ...vehicleData.mode, ...standardData.mode };

  // 更新用户信息
  vehicleData.user = { ...vehicleData.user, ...standardData.user };

  // 更新系统状态
  vehicleData.system = { ...vehicleData.system, ...standardData.system };

  // 更新连接状态和时间戳
  vehicleData.connected = standardData.connected;
  vehicleData.lastUpdate = standardData.lastUpdate;

  console.log('车辆数据已更新:', vehicleData);
}

/**
 * 处理车辆状态数据（保留兼容性）
 * @param {string} topic 主题
 * @param {string} message 消息内容
 */
function handleVehicleStatus(topic, message) {
  // 这个函数现在主要用于向后兼容
  // 实际处理在 handleMqttMessage 中完成
  console.log('处理车辆状态数据:', { topic, message });
}

/**
 * 处理电池信息数据
 * @param {string} topic 主题
 * @param {string} message 消息内容
 */
function handleBatteryInfo(topic, message) {
  try {
    const data = JSON.parse(message);
    
    // 更新电池信息
    vehicleData.battery = { ...vehicleData.battery, ...data };
    
    console.log('电池信息更新:', vehicleData.battery);
    notifyDataListeners('battery', vehicleData.battery);
    
    // 检查电池警告
    if (data.level < 20) {
      notifyDataListeners('warning', {
        type: 'low_battery',
        message: `电池电量低：${data.level}%`,
        level: data.level
      });
    }
    
  } catch (error) {
    console.error('电池信息数据解析错误:', error);
  }
}

/**
 * 处理速度信息数据
 * @param {string} topic 主题
 * @param {string} message 消息内容
 */
function handleSpeedInfo(topic, message) {
  try {
    const data = JSON.parse(message);
    
    // 更新速度信息
    vehicleData.speed = { ...vehicleData.speed, ...data };
    
    console.log('速度信息更新:', vehicleData.speed);
    notifyDataListeners('speed', vehicleData.speed);
    
    // 检查超速警告
    const currentMode = DRIVING_MODES[vehicleData.mode.current];
    if (data.current > currentMode.maxSpeed) {
      notifyDataListeners('warning', {
        type: 'overspeed',
        message: `当前速度${data.current}km/h超过${currentMode.name}限速${currentMode.maxSpeed}km/h`,
        currentSpeed: data.current,
        maxSpeed: currentMode.maxSpeed
      });
    }
    
  } catch (error) {
    console.error('速度信息数据解析错误:', error);
  }
}

/**
 * 处理位置信息数据
 * @param {string} topic 主题
 * @param {string} message 消息内容
 */
function handleLocationInfo(topic, message) {
  try {
    const data = JSON.parse(message);
    
    // 更新位置信息
    vehicleData.location = { ...vehicleData.location, ...data };
    
    console.log('位置信息更新:', vehicleData.location);
    notifyDataListeners('location', vehicleData.location);
    
  } catch (error) {
    console.error('位置信息数据解析错误:', error);
  }
}

/**
 * MQTT连接断开处理
 */
function handleMqttDisconnected() {
  console.log('MQTT连接断开');
  vehicleData.connected = false;
  notifyDataListeners('connection', { connected: false });
}

/**
 * 切换驾驶模式
 * @param {string|number} mode 驾驶模式 'youth'|'adult'|'elderly' 或 1|2|3
 * @returns {Promise} 切换结果
 */
function switchDrivingMode(mode) {
  return new Promise(async (resolve, reject) => {
    try {
      // 转换模式参数
      let modeString;
      let modeCode;

      if (typeof mode === 'string') {
        modeString = mode;
        modeCode = MODE_CODE_MAP[mode];
      } else {
        modeCode = mode;
        modeString = Object.keys(MODE_CODE_MAP).find(key => MODE_CODE_MAP[key] === mode);
      }

      if (!DRIVING_MODES[modeCode]) {
        reject({
          success: false,
          message: `无效的驾驶模式: ${mode}`
        });
        return;
      }

      console.log(`切换驾驶模式: ${modeString} (${modeCode})`);

      // 根据当前数据源选择不同的切换方式
      if (currentDataSource === 'http') {
        // 使用HTTP API切换模式
        const result = await httpDataManager.setVehicleMode(modeString);
        console.log(`HTTP模式切换成功:`, result);

        // 本地更新模式
        vehicleData.mode.current = modeCode;
        vehicleData.speed.max = DRIVING_MODES[modeCode].maxSpeed;

        resolve({
          success: true,
          message: `驾驶模式切换成功: ${DRIVING_MODES[modeCode].name}`,
          mode: modeString,
          dataSource: 'http'
        });

      } else if (currentDataSource === 'mqtt') {
        // 使用MQTT发送切换指令
        const commandJSON = vehicleDataParser.generateControlCommand('switch_mode', {
          mode: modeCode
        });

        const result = await mqttManager.publish(config.VEHICLE_TOPICS.MODE_CONTROL, commandJSON);
        console.log(`MQTT模式切换指令发送成功: ${modeCode} (${DRIVING_MODES[modeCode].name})`);

        // 本地更新模式（实际应等待车辆确认）
        vehicleData.mode.current = modeCode;
        vehicleData.speed.max = DRIVING_MODES[modeCode].maxSpeed;

        notifyDataListeners('mode', {
          mode: modeCode,
          config: DRIVING_MODES[modeCode]
        });

        resolve({
          success: true,
          message: `已切换到${DRIVING_MODES[modeCode].name}`,
          mode: modeString,
          dataSource: 'mqtt'
        });
      } else {
        reject({
          success: false,
          message: '未知的数据源类型'
        });
      }

    } catch (error) {
      console.error('驾驶模式切换失败:', error);
      reject({
        success: false,
        message: '驾驶模式切换失败',
        error: error
      });
    }
  });
}

/**
 * 设置速度限制
 * @param {number} speedLimit 速度限制 km/h
 * @returns {Promise} 设置结果
 */
function setSpeedLimit(speedLimit) {
  return new Promise((resolve, reject) => {
    if (speedLimit < 5 || speedLimit > 30) {
      reject({
        success: false,
        message: '速度限制必须在5-30km/h之间'
      });
      return;
    }
    
    const command = {
      action: 'set_speed_limit',
      speed_limit: speedLimit,
      timestamp: Date.now()
    };
    
    mqttManager.publish(config.VEHICLE_TOPICS.SPEED_LIMIT, JSON.stringify(command))
      .then(result => {
        console.log(`速度限制设置指令发送成功: ${speedLimit}km/h`);
        
        // 本地更新速度限制
        vehicleData.speed.max = speedLimit;
        
        notifyDataListeners('speed_limit', {
          speedLimit: speedLimit
        });
        
        resolve({
          success: true,
          message: `速度限制已设置为${speedLimit}km/h`,
          speedLimit: speedLimit
        });
      })
      .catch(error => {
        console.error('速度限制设置失败:', error);
        reject({
          success: false,
          message: '速度限制设置失败',
          error: error
        });
      });
  });
}

/**
 * 获取当前车辆数据
 * @returns {Object} 车辆数据
 */
function getVehicleData() {
  return {
    ...vehicleData,
    modes: DRIVING_MODES
  };
}

/**
 * 添加数据监听器
 * @param {string} type 监听类型
 * @param {function} listener 监听函数
 */
function addDataListener(type, listener) {
  if (!dataListeners.has(type)) {
    dataListeners.set(type, []);
  }
  dataListeners.get(type).push(listener);
}

/**
 * 移除数据监听器
 * @param {string} type 监听类型
 * @param {function} listener 监听函数
 */
function removeDataListener(type, listener) {
  if (dataListeners.has(type)) {
    const listeners = dataListeners.get(type);
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }
}

/**
 * 通知数据监听器
 * @param {string} type 数据类型
 * @param {Object} data 数据内容
 */
function notifyDataListeners(type, data) {
  if (dataListeners.has(type)) {
    dataListeners.get(type).forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('数据监听器执行错误:', error);
      }
    });
  }
}

/**
 * 断开连接
 * @returns {Promise} 断开结果
 */
function disconnect() {
  return mqttManager.disconnect()
    .then(result => {
      vehicleData.connected = false;
      dataListeners.clear();
      console.log('车辆数据管理器已断开');
      return result;
    });
}

module.exports = {
  initialize,
  disconnect,
  switchDrivingMode,
  setSpeedLimit,
  getVehicleData,
  addDataListener,
  removeDataListener,
  DRIVING_MODES
};
