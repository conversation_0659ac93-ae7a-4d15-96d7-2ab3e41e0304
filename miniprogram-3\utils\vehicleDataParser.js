// vehicleDataParser.js - 车辆数据解析器
// 专门处理您的JSON数据格式

/**
 * 解析完整的车辆数据JSON
 * @param {string} jsonString JSON字符串
 * @returns {Object} 解析后的数据对象
 */
function parseVehicleDataJSON(jsonString) {
  try {
    const data = JSON.parse(jsonString);
    
    return {
      success: true,
      data: {
        user: parseUserData(data.user),
        gps: parseGPSData(data.gps_data),
        car: parseCarData(data.car_data)
      },
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('车辆数据JSON解析失败:', error);
    return {
      success: false,
      error: error.message,
      timestamp: Date.now()
    };
  }
}

/**
 * 解析用户数据
 * @param {Array} userArray 用户数据数组
 * @returns {Object} 解析后的用户数据
 */
function parseUserData(userArray) {
  if (!Array.isArray(userArray) || userArray.length === 0) {
    return {
      id: 0,
      username: '',
      role: '',
      authenticated: false
    };
  }
  
  const user = userArray[0]; // 取第一个用户
  
  return {
    id: user.id || 0,
    username: user.username || '',
    role: user.role || '',
    authenticated: !!(user.username && user.password),
    _id: user._id || 0
  };
}

/**
 * 解析GPS数据
 * @param {Array} gpsArray GPS数据数组
 * @returns {Object} 解析后的GPS数据
 */
function parseGPSData(gpsArray) {
  if (!Array.isArray(gpsArray) || gpsArray.length === 0) {
    return {
      latitude: 0,
      longitude: 0,
      valid: false
    };
  }
  
  const gps = gpsArray[0]; // 取第一个GPS数据
  
  // 处理经度数据（您的数据中经度可能需要格式转换）
  let longitude = gps.longitude || 0;
  let latitude = gps.latitude || 0;
  
  // 如果经度是大数字格式，进行转换
  if (longitude > 1000000) {
    longitude = longitude / 1000000; // 根据实际情况调整转换比例
  }
  
  return {
    id: gps.id || 0,
    latitude: latitude,
    longitude: longitude,
    valid: !!(latitude && longitude),
    _id: gps._id || 0,
    timestamp: Date.now()
  };
}

/**
 * 解析车辆数据
 * @param {Array} carArray 车辆数据数组
 * @returns {Object} 解析后的车辆数据
 */
function parseCarData(carArray) {
  if (!Array.isArray(carArray) || carArray.length === 0) {
    return {
      speed: 0,
      power: 0,
      mode: 2, // 默认成人模式
      valid: false
    };
  }
  
  const car = carArray[0]; // 取第一个车辆数据
  
  return {
    id: car.id || 0,
    speed: car.speed || 0,        // 速度 km/h
    power: car.power || 0,        // 电量百分比
    mode: car.mod || 2,           // 驾驶模式 1=青少年 2=成人 3=老人
    valid: true,
    _id: car._id || 0,
    timestamp: Date.now()
  };
}

/**
 * 转换为标准车辆数据格式
 * @param {Object} parsedData 解析后的数据
 * @returns {Object} 标准格式的车辆数据
 */
function convertToStandardFormat(parsedData) {
  if (!parsedData.success) {
    return null;
  }
  
  const { user, gps, car } = parsedData.data;
  
  return {
    // 基础状态
    connected: true,
    lastUpdate: new Date(),
    
    // 电池信息
    battery: {
      level: car.power,           // 电量百分比
      voltage: 0,                 // 电压（暂无数据）
      current: 0,                 // 电流（暂无数据）
      temperature: 0,             // 温度（暂无数据）
      charging: false             // 充电状态（暂无数据）
    },
    
    // 速度信息
    speed: {
      current: car.speed,         // 当前速度
      max: getModeMaxSpeed(car.mode), // 根据模式获取最大速度
      average: 0                  // 平均速度（需要计算）
    },
    
    // 位置信息
    location: {
      latitude: gps.latitude,     // 纬度
      longitude: gps.longitude,   // 经度
      altitude: 0,                // 海拔（暂无数据）
      heading: 0,                 // 方向角（暂无数据）
      accuracy: gps.valid ? 5 : 0 // 精度估算
    },
    
    // 驾驶模式
    mode: {
      current: car.mode,          // 当前模式代码
      available: [1, 2, 3]        // 可用模式
    },
    
    // 用户信息
    user: {
      id: user.id,
      username: user.username,
      role: user.role,
      authenticated: user.authenticated
    },
    
    // 系统状态
    system: {
      temperature: 0,
      errors: [],
      warnings: generateWarnings(car, gps)
    }
  };
}

/**
 * 根据模式获取最大速度
 * @param {number} modeCode 模式代码
 * @returns {number} 最大速度
 */
function getModeMaxSpeed(modeCode) {
  const speedMap = {
    1: 15, // 青少年模式
    2: 25, // 成人模式
    3: 12  // 老人模式
  };
  
  return speedMap[modeCode] || 25;
}

/**
 * 生成警告信息
 * @param {Object} carData 车辆数据
 * @param {Object} gpsData GPS数据
 * @returns {Array} 警告列表
 */
function generateWarnings(carData, gpsData) {
  const warnings = [];
  
  // 电量警告
  if (carData.power < 20) {
    warnings.push({
      type: 'low_battery',
      message: `电池电量低：${carData.power}%`,
      level: 'warning',
      timestamp: Date.now()
    });
  }
  
  if (carData.power < 10) {
    warnings.push({
      type: 'critical_battery',
      message: `电池电量严重不足：${carData.power}%`,
      level: 'critical',
      timestamp: Date.now()
    });
  }
  
  // 超速警告
  const maxSpeed = getModeMaxSpeed(carData.mode);
  if (carData.speed > maxSpeed) {
    warnings.push({
      type: 'overspeed',
      message: `当前速度${carData.speed}km/h超过限速${maxSpeed}km/h`,
      level: 'warning',
      timestamp: Date.now()
    });
  }
  
  // GPS信号警告
  if (!gpsData.valid) {
    warnings.push({
      type: 'gps_invalid',
      message: 'GPS信号异常，位置信息可能不准确',
      level: 'warning',
      timestamp: Date.now()
    });
  }
  
  return warnings;
}

/**
 * 生成控制指令JSON
 * @param {string} action 动作类型
 * @param {Object} params 参数
 * @returns {string} JSON字符串
 */
function generateControlCommand(action, params = {}) {
  const command = {
    action: action,
    timestamp: Date.now(),
    ...params
  };
  
  switch (action) {
    case 'switch_mode':
      return JSON.stringify({
        car_data: [{
          id: 1,
          mod: params.mode || 2,
          _id: 0
        }]
      });
      
    case 'set_speed_limit':
      // 注意：您的数据结构中没有直接的速度限制字段
      // 这里通过模式来间接控制速度限制
      const modeForSpeed = params.speedLimit <= 15 ? 1 : 
                          params.speedLimit <= 25 ? 2 : 3;
      return JSON.stringify({
        car_data: [{
          id: 1,
          mod: modeForSpeed,
          _id: 0
        }]
      });
      
    case 'emergency_stop':
      return JSON.stringify({
        car_data: [{
          id: 1,
          speed: 0,
          _id: 0
        }]
      });
      
    default:
      return JSON.stringify(command);
  }
}

/**
 * 验证数据完整性
 * @param {Object} data 数据对象
 * @returns {Object} 验证结果
 */
function validateData(data) {
  const errors = [];
  
  // 检查必需字段
  if (!data.user || !Array.isArray(data.user) || data.user.length === 0) {
    errors.push('缺少用户数据');
  }
  
  if (!data.gps_data || !Array.isArray(data.gps_data) || data.gps_data.length === 0) {
    errors.push('缺少GPS数据');
  }
  
  if (!data.car_data || !Array.isArray(data.car_data) || data.car_data.length === 0) {
    errors.push('缺少车辆数据');
  }
  
  // 检查数据范围
  if (data.car_data && data.car_data[0]) {
    const car = data.car_data[0];
    
    if (car.power < 0 || car.power > 100) {
      errors.push('电量数据超出范围 (0-100)');
    }
    
    if (car.speed < 0 || car.speed > 50) {
      errors.push('速度数据超出合理范围 (0-50)');
    }
    
    if (car.mod < 1 || car.mod > 3) {
      errors.push('驾驶模式代码无效 (1-3)');
    }
  }
  
  return {
    valid: errors.length === 0,
    errors: errors
  };
}

/**
 * 格式化显示数据
 * @param {Object} standardData 标准格式数据
 * @returns {Object} 格式化后的显示数据
 */
function formatForDisplay(standardData) {
  if (!standardData) return null;
  
  const modeNames = {
    1: '青少年模式',
    2: '成人模式',
    3: '老人模式'
  };
  
  return {
    // 电池显示
    battery: {
      level: `${standardData.battery.level}%`,
      status: standardData.battery.level > 20 ? '正常' : '电量不足',
      color: standardData.battery.level > 20 ? '#4CAF50' : '#FF5722'
    },
    
    // 速度显示
    speed: {
      current: `${standardData.speed.current} km/h`,
      limit: `限速 ${standardData.speed.max} km/h`,
      status: standardData.speed.current <= standardData.speed.max ? '正常' : '超速'
    },
    
    // 模式显示
    mode: {
      name: modeNames[standardData.mode.current] || '未知模式',
      code: standardData.mode.current,
      description: `限速${getModeMaxSpeed(standardData.mode.current)}km/h`
    },
    
    // 位置显示
    location: {
      coordinates: `${standardData.location.latitude.toFixed(6)}, ${standardData.location.longitude.toFixed(6)}`,
      status: standardData.location.accuracy > 0 ? 'GPS正常' : 'GPS异常'
    },
    
    // 用户显示
    user: {
      info: `${standardData.user.username} (${standardData.user.role})`,
      status: standardData.user.authenticated ? '已认证' : '未认证'
    }
  };
}

module.exports = {
  parseVehicleDataJSON,
  parseUserData,
  parseGPSData,
  parseCarData,
  convertToStandardFormat,
  generateControlCommand,
  validateData,
  formatForDisplay,
  getModeMaxSpeed
};
