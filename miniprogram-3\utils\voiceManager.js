// voiceManager.js - 语音播报管理器
const vehicleDataManager = require('./vehicleDataManager.js');

// 语音播报管理器
const VoiceManager = {
  // 配置参数
  config: {
    enabled: true,           // 是否启用语音播报
    volume: 1.0,            // 音量 (0.0-1.0)
    rate: 1.0,              // 语速 (0.5-2.0)
    pitch: 1.0,             // 音调 (0.0-2.0)
    lang: 'zh-CN',          // 语言
    voiceURI: '',           // 语音引擎
    autoPlay: true,         // 自动播放
    repeatInterval: 30000,  // 重复播报间隔(毫秒)
    maxRetries: 3          // 最大重试次数
  },

  // 播报状态
  state: {
    isPlaying: false,       // 是否正在播放
    lastPlayTime: 0,        // 上次播放时间
    lastInstruction: '',    // 上次播报内容
    retryCount: 0,          // 重试次数
    queue: []              // 播报队列
  },

  // 初始化语音管理器
  init: function() {
    console.log('语音播报管理器初始化');
    
    // 检查微信小程序语音合成API支持
    if (wx.createInnerAudioContext) {
      console.log('支持音频播放API');
    }
    
    // 从本地存储加载配置
    this.loadConfig();
    
    return Promise.resolve();
  },

  // 加载配置
  loadConfig: function() {
    try {
      const savedConfig = wx.getStorageSync('voiceConfig');
      if (savedConfig) {
        this.config = { ...this.config, ...savedConfig };
        console.log('语音配置已加载:', this.config);
      }
    } catch (error) {
      console.warn('加载语音配置失败:', error);
    }
  },

  // 保存配置
  saveConfig: function() {
    try {
      wx.setStorageSync('voiceConfig', this.config);
      console.log('语音配置已保存');
    } catch (error) {
      console.warn('保存语音配置失败:', error);
    }
  },

  // 语音播报主函数
  speak: function(text, options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.config.enabled) {
        console.log('语音播报已禁用');
        resolve();
        return;
      }

      if (!text || text.trim() === '') {
        console.warn('语音播报文本为空');
        resolve();
        return;
      }

      // 检查是否需要播报
      if (!this.shouldSpeak(text)) {
        console.log('跳过重复播报:', text);
        resolve();
        return;
      }

      // 合并配置
      const speakOptions = { ...this.config, ...options };
      
      // 更新状态
      this.state.isPlaying = true;
      this.state.lastPlayTime = Date.now();
      this.state.lastInstruction = text;
      this.state.retryCount = 0;

      console.log('开始语音播报:', text);

      // 使用微信小程序的语音合成
      this.synthesizeAndPlay(text, speakOptions)
        .then(() => {
          this.state.isPlaying = false;
          console.log('语音播报完成:', text);
          resolve();
        })
        .catch(error => {
          this.state.isPlaying = false;
          console.error('语音播报失败:', error);
          reject(error);
        });
    });
  },

  // 语音合成和播放
  synthesizeAndPlay: function(text, options) {
    return new Promise((resolve, reject) => {
      // 微信小程序暂不支持TTS，使用模拟播报
      // 在实际项目中可以集成第三方TTS服务
      
      // 模拟语音播报延迟
      const duration = this.calculateSpeechDuration(text, options.rate);
      
      // 显示语音播报提示
      wx.showToast({
        title: '🔊 ' + text,
        icon: 'none',
        duration: Math.min(duration, 3000)
      });

      // 播放系统提示音
      wx.playBackgroundAudio && wx.playBackgroundAudio({
        dataUrl: '',
        title: '导航语音',
        success: () => {
          setTimeout(resolve, duration);
        },
        fail: () => {
          // 如果播放失败，仍然显示文字提示
          setTimeout(resolve, duration);
        }
      }) || setTimeout(resolve, duration);
    });
  },

  // 计算语音播报时长
  calculateSpeechDuration: function(text, rate = 1.0) {
    // 估算中文语音播报时长：平均每个字符200ms，根据语速调整
    const baseTime = text.length * 200;
    return Math.max(1000, baseTime / rate);
  },

  // 判断是否需要播报
  shouldSpeak: function(text) {
    const now = Date.now();
    
    // 如果正在播放，跳过
    if (this.state.isPlaying) {
      return false;
    }

    // 如果是相同内容且在重复间隔内，跳过
    if (this.state.lastInstruction === text && 
        now - this.state.lastPlayTime < this.config.repeatInterval) {
      return false;
    }

    return true;
  },

  // 停止播报
  stop: function() {
    this.state.isPlaying = false;
    this.state.queue = [];
    
    // 停止背景音频
    wx.stopBackgroundAudio && wx.stopBackgroundAudio();
    
    console.log('语音播报已停止');
  },

  // 设置配置
  setConfig: function(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
    console.log('语音配置已更新:', this.config);
  },

  // 获取配置
  getConfig: function() {
    return { ...this.config };
  },

  // 启用/禁用语音播报
  setEnabled: function(enabled) {
    this.config.enabled = enabled;
    this.saveConfig();
    
    if (!enabled) {
      this.stop();
    }
    
    console.log('语音播报', enabled ? '已启用' : '已禁用');
  },

  // 设置音量
  setVolume: function(volume) {
    this.config.volume = Math.max(0, Math.min(1, volume));
    this.saveConfig();
    console.log('语音音量设置为:', this.config.volume);
  },

  // 设置语速
  setRate: function(rate) {
    this.config.rate = Math.max(0.5, Math.min(2.0, rate));
    this.saveConfig();
    console.log('语音语速设置为:', this.config.rate);
  },

  // 播报导航指令
  speakNavigation: function(instruction, distance = null) {
    let text = instruction;
    
    // 添加距离信息
    if (distance !== null) {
      if (distance > 1000) {
        text += `，剩余${(distance / 1000).toFixed(1)}公里`;
      } else if (distance > 0) {
        text += `，剩余${distance}米`;
      }
    }
    
    return this.speak(text);
  },

  // 播报电动车特有信息
  speakElectricVehicleInfo: function(type, data) {
    let text = '';
    
    switch (type) {
      case 'lowBattery':
        text = `电量不足${data.level}%，建议尽快充电`;
        break;
      case 'chargingStation':
        text = `前方${data.distance}米有充电站，是否前往充电？`;
        break;
      case 'speedLimit':
        text = `注意限速${data.limit}公里每小时`;
        break;
      case 'restrictionZone':
        text = `前方进入限行区域，请注意绕行`;
        break;
      case 'arrival':
        text = '您已到达目的地，导航结束';
        break;
      default:
        text = data.message || '';
    }
    
    if (text) {
      return this.speak(text);
    }
    
    return Promise.resolve();
  },

  // 获取状态信息
  getStatus: function() {
    return {
      ...this.state,
      config: this.getConfig()
    };
  }
};

module.exports = VoiceManager;
